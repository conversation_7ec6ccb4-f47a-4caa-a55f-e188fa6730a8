v1.0.extensionprocessordto.apimodel.description=Extension Processor.
v1.0.extensionprocessordto.apimodelproperty.id.description=The id of extension processor.
v1.0.extensionprocessordto.apimodelproperty.name.description=The name of extension processor.
v1.0.extensionprocessordto.apimodelproperty.description.description=The description of extension processor.
v1.0.extensionprocessordto.apimodelproperty.processorclass.description=Processor class name. This implements the callback points for the totalizer.
v1.0.extensionprocessordto.apimodelproperty.configurationpoint.description=Some configuration point.
v1.0.extensionprocessordto.apimodelproperty.sequence.description=Extension Processor sequence.
v1.0.extensionprocessordto.apimodelproperty.processordataclass.description=Processor Data Class name.
v1.0.extensionprocessordto.apimodelproperty.versions.description=List with versions of processor data.

v1.0.configurationpointdto.apimodel.description=Some configuration point.
v1.0.configurationpointdto.apimodelproperty.id.description=the id of configuration point.
v1.0.configurationpointdto.apimodelproperty.name.description=the name of configuration point.

v1.0.processordatadto.apimodel.description=The version of processor data.
v1.0.processordatadto.apimodelproperty.effectivedate.description=Extension Processor version effective date.
v1.0.processordatadto.apimodelproperty.expirationdate.description=Extension Processor version expiration date.
v1.0.processordatadto.apimodelproperty.ruledata.description=Extension Processor version rule data.

v1.0.processortorule.apimodel.description=Processor to rule association.
v1.0.processortorule.apimodelproperty.processor.description=The processor assigned to association.
v1.0.processortorule.apimodelproperty.ruleType.description=Rule type of the processor.
v1.0.processortorule.apimodelproperty.rules.description=Rules assigned to association.

v1.0.processor.apimodel.description=Processor.
v1.0.processor.apimodelproperty.id.description=The id of processor.
v1.0.processor.apimodelproperty.name.description=The name of processor.

v1.0.rule.apimodel.description=Processor.
v1.0.rule.apimodelproperty.name.description=The name of rule.
v1.0.rule.apimodelproperty.effectivedate.description=The effective date of rule.
