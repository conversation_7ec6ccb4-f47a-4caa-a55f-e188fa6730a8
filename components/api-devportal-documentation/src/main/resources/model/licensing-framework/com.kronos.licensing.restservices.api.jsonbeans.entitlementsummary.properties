v1.0.licensingbean.history.apimodel.description=Entitlement History
v1.0.licensingbean.history.apimodelproperty.id.description=The ID of an entitlement history.
v1.0.licensingbean.history.apimodelproperty.packageName.description=The package name associated with an entitlement history.
v1.0.licensingbean.history.apimodelproperty.packageCode.description=The package code associated with an entitlement history.
v1.0.licensingbean.history.apimodelproperty.seats.description=The seats associated with an entitlement history.
v1.0.licensingbean.history.apimodelproperty.startDate.description=The start date associated with an entitlement history.
v1.0.licensingbean.history.apimodelproperty.endDate.description=The end date associated with an entitlement history.
v1.0.licensingbean.history.apimodelproperty.creationDate.description=The creation date associated with an entitlement history.
v1.0.licensingbean.history.apimodelproperty.creationTime.description=The creation time associated with an entitlement history.
v1.0.licensingbean.history.apimodelproperty.entitlementCode.description=The entitlement code associated with an entitlement history.
v1.0.licensingbean.history.apimodelproperty.orderNumber.description=The order number associated with an entitlement history.
v1.0.licensingbean.history.apimodelproperty.solutionId.description=The solution ID associated with an entitlement history.
