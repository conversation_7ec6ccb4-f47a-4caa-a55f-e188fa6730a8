v1.0.licensingbean.summary.apimodel.description=Entitlement Summary 
v1.0.licensingbean.summary.apimodelproperty.id.description=The ID of an entitlement summary. 
v1.0.licensingbean.summary.apimodelproperty.packageName.description=The package name associated with an entitlement summary.
v1.0.licensingbean.summary.apimodelproperty.seats.description=The seats associated with an entitlement summary.
v1.0.licensingbean.summary.apimodelproperty.assignedSeats.description=The assigned seats associated with an entitlement summary.
v1.0.licensingbean.summary.apimodelproperty.remSeats.description=The remaining seats associated with an entitlement summary.
v1.0.licensingbean.summary.apimodelproperty.solutionId.description=The solution ID associated with an entitlement summary.
v1.0.licensingbean.summary.apimodelproperty.currentDate.description=The current date associated with an entitlement summary.
v1.0.licensingbean.summary.apimodelproperty.implementationMessage.description=The implementation message associated with an entitlement summary.