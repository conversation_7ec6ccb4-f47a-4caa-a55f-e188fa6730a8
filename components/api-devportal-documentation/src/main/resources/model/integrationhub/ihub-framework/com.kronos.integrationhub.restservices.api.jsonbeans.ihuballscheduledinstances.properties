#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihuballscheduledinstances.apimodel.description=Details of all scheduled integration instances
v1.0.ihuballscheduledinstances.apimodelproperty.id.description=instance id
v1.0.ihuballscheduledinstances.apimodelproperty.requestname.description=instance request name
v1.0.ihuballscheduledinstances.apimodelproperty.scheduledinfo.description=schedule information
v1.0.ihuballscheduledinstances.apimodelproperty.createddtm.description=creation time of instance
v1.0.ihuballscheduledinstances.apimodelproperty.processname.description=instance name
v1.0.ihuballscheduledinstances.apimodelproperty.requesttype.description=request type
v1.0.ihuballscheduledinstances.apimodelproperty.isset.description=is the instance a set?
v1.0.ihuballscheduledinstances.apimodelproperty.user.description=user who created the instance
v1.0.ihuballscheduledinstances.apimodelproperty.integrationdeleted.description=whether associated integration has been deleted or not?
