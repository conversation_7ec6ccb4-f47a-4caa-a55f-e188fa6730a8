v1.0.ihubintegrationprocessbean.apimodel.description=The integration process model.
v1.0.ihubintegrationprocessbean.apimodelproperty.name.description=The name of an integration process.
v1.0.ihubintegrationprocessbean.apimodelproperty.id.description=The unique ID of an integration process.
v1.0.ihubintegrationprocessbean.apimodelproperty.description.description=<em>[Optional]</em> The description of an integration process.
v1.0.ihubintegrationprocessbean.apimodelproperty.integrationType.description=<p>The type of integration. Valid values include:</p><ul><li>IMPORT</li><li>EXPORT</li><li>NONE</li></ul>
v1.0.ihubintegrationprocessbean.apimodelproperty.runAsSystemUser.description=<em>[Optional]</em> A Boolean indicator of whether the integration is executed as a system user.
v1.0.ihubintegrationprocessbean.apimodelproperty.integrationTemplate.description=The name of an integration template associated with an integration process.
v1.0.ihubintegrationprocessbean.apimodelproperty.source.description=The name of the source platform.
v1.0.ihubintegrationprocessbean.apimodelproperty.integrationParameters.description=A list of integration parameter objects.
v1.0.ihubintegrationprocessbean.apimodelproperty.sftpConnection.description=The SFTP Connection object.