v1.0.ihubintegrationdatawhere.apimodel.description=Request to fetch details for an integration execution.
v1.0.ihubintegrationdatawhere.apimodelproperty.requestdaterange.description=Request object containing start date time and end date time.
v1.0.ihubintegrationdatawhere.apimodelproperty.integrationname.description=Name of the installed integration that is the basis of this integration run.
v1.0.ihubintegrationdatawhere.apimodelproperty.integrationrunname.description=A unique integration run name to make it easier to identify the run of that integration.
v1.0.ihubintegrationdatawhere.apimodelproperty.integrationexecutionid.description=Unique ID for the integration run.	
v1.0.ihubintegrationdatawhere.apimodelproperty.integrationrunby.description=User executed the integration.