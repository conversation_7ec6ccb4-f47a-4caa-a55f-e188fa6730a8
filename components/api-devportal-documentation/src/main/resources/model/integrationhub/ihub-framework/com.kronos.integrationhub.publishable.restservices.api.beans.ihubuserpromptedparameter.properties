#Comments for Properties
#Thu Sep 28 17:13:00 IST 2017
v1.0.ihubuserpromptedparameter.apimodel.description=Integration parameters whose values can be overridden by user
v1.0.ihubuserpromptedparameter.apimodelproperty.name.description=Name of the parameter
v1.0.ihubuserpromptedparameter.apimodelproperty.value.description=Value of the parameter. The value must correspond to the type of parameter. The following table contains the list of accepted values for each parameter type.<table border="1" cellspacing="1" cellpadding="1" align="left"> <tbody> <tr> <td> <p><strong>Parameter type</strong></p> </td> <td> <p><strong>Subtype</strong></p> </td> <td> <p><strong>Accepted values</strong></p> </td> </tr> <tr> <td> <p>Boolean</p> </td> <td>&nbsp;</td> <td> <p>true/false</p> </td> </tr> <tr> <td> <p>String</p> </td> <td>&nbsp;</td> <td> <p>Any non-blank string</p> </td> </tr> <tr> <td> <p>Number</p> </td> <td>&nbsp;</td> <td> <p>Any valid number</p> </td> </tr> <tr> <td> <p>Date</p> </td> <td>&nbsp;</td> <td> <p>ISO date format (yyyy-MM-dd)</p> </td> </tr> <tr> <td rowspan="2"> <p>Time period</p> </td> <td> <p>Pay period</p> </td> <td> <p>{</p> <p>"symbolicPeriod": {</p> <p>"id": "&lt;payperiod id&gt;"</p> <p>}</p> <p>}</p> </td> </tr> <tr> <td> <p>Date range</p> </td> <td> <p>{</p> <p>"startDate": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",</p> <p>"endDate":"yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"</p> <p>}</p> </td> </tr> <tr> <td rowspan="3"> <p>Hyperfind and Locations</p> </td> <td> <p>Hyperfind</p> </td> <td> <p>{</p> <p>"hyperfind": {</p> <p>"id": &ldquo;&lt;hyperfind id&gt;&rdquo;</p> <p>}</p> <p>}</p> </td> </tr> <tr> <td> <p>Saved locations</p> </td> <td> <p>{</p> <p>"savedLocations": {</p> <p>"id": &ldquo;&lt;saved locations id&gt;&rdquo;</p> <p>}</p> <p>}</p> </td> </tr> <tr> <td> <p>Selected locations</p> </td> <td> <p>{</p> <p>"locations": {</p> <p>"ids": [</p> <p>"&lt;location id1&gt;",</p> <p>"&lt;location id2&gt;"</p> <p>]</p> <p>}</p> <p>}</p> </td> </tr> <tr> <td><p>Date selector</p></td> <td>&nbsp;</td> <td> <p>{</p> <p>"date": &ldquo;&lt;Time Period End Date&gt;&rdquo;,</p> <p>"offset": &ldquo;&lt;Positive or negative number. Used for date calculation&gt;&rdquo;</p> <p>}</p> </td> </tr> </tbody> </table>