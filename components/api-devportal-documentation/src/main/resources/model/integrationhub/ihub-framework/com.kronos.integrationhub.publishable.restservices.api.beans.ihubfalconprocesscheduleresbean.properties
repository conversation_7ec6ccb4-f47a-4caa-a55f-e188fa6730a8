#Comments for Properties
#Thu Sep 28 17:13:00 IST 2017
v1.0.ihubfalconprocesscheduleresbean.apimodel.description=The result returned when an integration is sumbitted for schedule
v1.0.ihubfalconprocesscheduleresbean.apimodelproperty.id.description=Unique Id for the integration schedule
v1.0.ihubfalconprocesscheduleresbean.apimodelproperty.integrationname.descriptio=Name of the installed integration that is the basis of this integration schedule
v1.0.ihubfalconprocesscheduleresbean.apimodelproperty.name.description=A unique integration schedule name to make it easier to identify the schedule of an integration
v1.0.ihubfalconprocesscheduleresbean.apimodelproperty.scheduledinfo.description=Information about the integration schedule.
v1.0.ihubfalconprocesscheduleresbean.apimodelproperty.integrationid.description=The ID of the installed integration that is the basis of this integration schedule
v1.0.ihubfalconprocesscheduleresbean.apimodelproperty.integrationparameters.description=A list of the integration parameters that have custom values.