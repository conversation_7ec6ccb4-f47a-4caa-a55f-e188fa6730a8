#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.responsestatusbean.apimodel.description=The immediate response returned when an integration is sumbitted for execution or scheduling
v1.0.responsestatusbean.apimodelproperty.status.description=(Required) The current state of an integration run after the run started or was scheduled.
v1.0.responsestatusbean.apimodelproperty.description.description=(Required) The detailed description about an integration.
v1.0.responsestatusbean.apimodelproperty.executiondate.description=The date when the integration run started.
v1.0.responsestatusbean.apimodelproperty.errormessage.description=Error message if the integration run fails.
v1.0.responsestatusbean.apimodelproperty.executionresponseid.description=Unique ID for the integration run.
v1.0.responsestatusbean.apimodelproperty.scheduleId.description=(Required) The schedule ID of a scheduled integration.
