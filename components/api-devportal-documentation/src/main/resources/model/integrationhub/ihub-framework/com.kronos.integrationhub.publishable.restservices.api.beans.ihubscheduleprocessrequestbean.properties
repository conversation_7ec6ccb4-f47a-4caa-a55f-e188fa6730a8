#Comments for Properties
#Thu Sep 28 17:13:00 IST 2017
v1.0.ihubscheduleprocessrequestbean.apimodel.description=Basic details related to an integration schedule
v1.0.ihubscheduleprocessrequestbean.apimodelproperty.id.description=A unique identifier for the integration schedule
v1.0.ihubscheduleprocessrequestbean.apimodelproperty.name.description=A unique name given to the integration schedule
v1.0.ihubscheduleprocessrequestbean.apimodelproperty.user.description=Name of the user who created this schedule
v1.0.ihubscheduleprocessrequestbean.apimodelproperty.createddtm.description=The date on which the schedule was created
v1.0.ihubscheduleprocessrequestbean.apimodelproperty.requesttype.description=The type of request
v1.0.ihubscheduleprocessrequestbean.apimodelproperty.integrationid.description=The ID of the installed integration that is the basis of this integration schedule
v1.0.ihubscheduleprocessrequestbean.apimodelproperty.integrationname.description=Name of the installed integration that is the basis of this integration schedule
v1.0.ihubscheduleprocessrequestbean.apimodelproperty.scheduledinfo.description=Information about the integration schedule.
v1.0.ihubscheduleprocessrequestbean.apimodelproperty.integrationdeleted.description=If the integration related to this schedule has already been deleted from the system. In such case the schedule is no longer run in future.
v1.0.ihubscheduleprocessrequestbean.apimodelproperty.schedulestatus.description=Information about the status of the scheduled integration.