#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
# v1.0.userparameter.apimodel.description=The user parameter describes the parameters and display types. It is a dependent object that is associated with a parent entity such as a report or integration.
# v1.0.userparameter.apimodelproperty.name.description=Name of the parameter.
# v1.0.userparameter.apimodelproperty.type.description=Type of parameter.
# v1.0.userparameter.apimodelproperty.value.description=The value of the parameter.
