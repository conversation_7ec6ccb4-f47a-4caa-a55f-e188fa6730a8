v1.0.ihubintegrationprocessparambean.apimodel.description=The integration process parameter model.
v1.0.ihubintegrationprocessparambean.apimodelproperty.name.description=The name of a process parameter.
v1.0.ihubintegrationprocessparambean.apimodelproperty.userPrompted.description=An Boolean indicator of whether or not a process parameter is user-prompted when an integration is executed.
v1.0.ihubintegrationprocessparambean.apimodelproperty.parameterType.description=<p>The type of process parameter. Valid values include:</p><ul><li>Boolean</li><li>Date</li><li>Dropdown</li><li>HyperFind</li><li>List</li><li>Number</li><li>Text</li><li>Time</li><li>Time period</li><li>Time Period End date selector</li></ul>
v1.0.ihubintegrationprocessparambean.apimodelproperty.defaultValue.description=<p>The default values for various types of parameters.</p><p>For the Boolean type:</p><ul><li>true</li><li>false</li></ul><p>For the HyperFind type, the Hyperfind ID is passed. Valid Hyperfind IDs include:</p><ul><li>203</li><li>1</li><li>-3</li><li>-10</li><li>-9</li><li>253</li><li>103</li><li>2</li><li>205</li><li>206</li><li>3</li><li>204</li><li>207</li><li>303</li><li>53</li><li>54</li><li>55</li><li>353</li><li>-5001</li></ul><p>For the time period end date type parameter, default values include:</p><ul><li>Any Day</li><li>Yesterday</li><li>2 Days ago</li><li>3 Days Ago</li><li>4 Days Ago</li></ul><p>For the date type parameter, default values include:</p><ul>Any Valid Date in YYYY-MM-DD Format</ul><p>For the list type parameter, default values include:</p><ul>A listname for a list created in the UI.</ul>
v1.0.ihubintegrationprocessparambean.apimodelproperty.templateParameter.description=The template parameter, which takes any template parameter name from the list of template parameters. Template parameters vary depending on the integration template used.