#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubenvconfigbean.apimodel.description=Environment configuration details
v1.0.ihubenvconfigbean.apimodelproperty.id.description=environment configuration identifier
v1.0.ihubenvconfigbean.apimodelproperty.name.description=environment configuration name
v1.0.ihubenvconfigbean.apimodelproperty.accountid.description=account identifier
v1.0.ihubenvconfigbean.apimodelproperty.accounttoken.description=account token
v1.0.ihubenvconfigbean.apimodelproperty.instanceid.description=instance identifier
v1.0.ihubenvconfigbean.apimodelproperty.integrationenvironmentid.description=integration environment identifier
