#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubuserparamfalconprocessbean.apimodel.description=The details like name and value of integration parameter available to user. These are the configurable properties for an integration and the values provided are used during actual execution
v1.0.ihubuserparamfalconprocessbean.apimodelproperty.name.description=(Required) Name of the API integration parameter.
v1.0.ihubuserparamfalconprocessbean.apimodelproperty.value.description=(Required) Custom value of the property.
