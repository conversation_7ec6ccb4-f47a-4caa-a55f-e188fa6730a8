v1.0.ihubsequenceprocessrequestbean.apimodel.description=Details of the integrations and parameters that are in the integration set.
v1.0.ihubsequenceprocessrequestbean.apimodelproperty.id.description=(Required) Unique ID for an integration in an integration set.
v1.0.ihubsequenceprocessrequestbean.apimodelproperty.name.description=(Required) Name of an integration in an integration set.
v1.0.ihubsequenceprocessrequestbean.apimodelproperty.description.description=Detailed description of an integration in an integration set.
v1.0.ihubsequenceprocessrequestbean.apimodelproperty.user.description=(Required) Person who created this run of the integration in an integration set.
v1.0.ihubsequenceprocessrequestbean.apimodelproperty.requestname.description=(Required) Name of the run of an integration in an integration set
v1.0.ihubsequenceprocessrequestbean.apimodelproperty.userparameters.description=(Required) List of the integration parameters that have custom values.
v1.0.ihubsequenceprocessrequestbean.apimodelproperty.integrationrequestid.description=Unique ID for the instance of an integration in an integration set.
v1.0.ihubsequenceprocessrequestbean.apimodelproperty.integrationtype.description=Type of integration whether it is part of an integration set.
v1.0.ihubsequenceprocessrequestbean.apimodelproperty.integrationsetmappingid.description=(Required) Unique ID for the mapping between an integration and the integration set.