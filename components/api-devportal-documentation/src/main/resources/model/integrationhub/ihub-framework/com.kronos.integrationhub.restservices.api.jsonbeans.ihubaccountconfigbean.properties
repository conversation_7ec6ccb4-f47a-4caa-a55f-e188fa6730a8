#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubaccountconfigbean.apimodel.description=Account configuration details
v1.0.ihubaccountconfigbean.apimodelproperty.id.description=account configuration identifier
v1.0.ihubaccountconfigbean.apimodelproperty.name.description=account name
v1.0.ihubaccountconfigbean.apimodelproperty.integrationuser.description=integration user
v1.0.ihubaccountconfigbean.apimodelproperty.integrationpassword.description=integration password
v1.0.ihubaccountconfigbean.apimodelproperty.integrationurl.description=integration url
v1.0.ihubaccountconfigbean.apimodelproperty.integrationaccountid.description=integration account identifier
v1.0.ihubaccountconfigbean.apimodelproperty.envconfig.description=environment configuration
