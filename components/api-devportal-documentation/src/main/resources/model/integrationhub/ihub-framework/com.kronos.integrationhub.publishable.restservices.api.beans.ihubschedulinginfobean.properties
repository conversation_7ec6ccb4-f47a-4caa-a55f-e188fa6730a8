#Comments for Properties
#Thu Sep 28 17:13:00 IST 2017
v1.0.ihubschedulinginfobean.apimodel.description=The event scheduling configuration parameters. The following table contains the list of accepted values for frequency type, frequency and interval.
v1.0.ihubschedulinginfobean.apimodelproperty.frequency.description=Frequency defines the specific days when a schedule should run based on whether it is scheduled Monthly or Weekly.
v1.0.ihubschedulinginfobean.apimodelproperty.frequencytype.description=It determines how often should a schedule is to be run i.e. Once, Monthly, Weekly, Daily or Hourly
v1.0.ihubschedulinginfobean.apimodelproperty.interval.description=The time to skip between two successive runs. Its definition varies depending on the frequency type selected.  
v1.0.ihubschedulinginfobean.apimodelproperty.starttime.description=The time when the scheduled integration executes for the first time.
v1.0.ihubschedulinginfobean.apimodelproperty.endtime.description=The time when schedule should execute for the last time.
v1.0.ihubschedulinginfobean.apimodelproperty.startdatetime.description=The date when the scheduled integration executes for the first time.
v1.0.ihubschedulinginfobean.apimodelproperty.enddatetime.description=The date when schedule should execute for the last time.
v1.0.ihubschedulinginfobean.apimodelproperty.forever.description=If no end date and time is specified, then the scheule runs forever.
