#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubrealtimeresponsebean.apimodel.description=The API integration execution response model.
v1.0.ihubrealtimeresponsebean.apimodelproperty.executionid.description=(Required) The unique ID for the API integration run.
v1.0.ihubrealtimeresponsebean.apimodelproperty.status.description=(Required) The current state of an API integration run. Status currently supported :- In Progress, Failed, Completed, Completed with errors.
v1.0.ihubrealtimeresponsebean.apimodelproperty.errormessage.description=An error message if the integration run fails.
v1.0.ihubrealtimeresponsebean.apimodelproperty.payload.description=The payload in the request to exchange with the external system. The payload structure is predefined for each integration for the source and destination systems.
v1.0.ihubrealtimeresponsebean.apimodelproperty.runSummary.description=The Run Summary of an API Integration run.
v1.0.ihubrealtimeresponsebean.apimodelproperty.executionrefid.description=The unique execution reference ID of an API Integration run.
v1.0.ihubrealtimeresponsebean.apimodelproperty.executionreferenceid.description=The unique execution reference ID of an API Integration run.