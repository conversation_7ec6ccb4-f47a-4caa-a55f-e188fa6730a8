#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.monitorslatviewjsonbean.apimodel.description=The detailed information, such as the status, of an integration that executed. It aids monitoring of that integration.
v1.0.monitorslatviewjsonbean.apimodelproperty.id.description=The unique ID of an integration run.
v1.0.monitorslatviewjsonbean.apimodelproperty.runname.description=The name of an integration run.
v1.0.monitorslatviewjsonbean.apimodelproperty.requesttype.description=The type of request that triggered an integration run: scheduled or manual.
v1.0.monitorslatviewjsonbean.apimodelproperty.startdate.description=The start date of an integration run in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.monitorslatviewjsonbean.apimodelproperty.enddate.description=The end date of an integration run in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.monitorslatviewjsonbean.apimodelproperty.integrationname.description=The name of an integration.
v1.0.monitorslatviewjsonbean.apimodelproperty.status.description=The current state of an integration run after the run started.
v1.0.monitorslatviewjsonbean.apimodelproperty.user.description=The person who created an integration run.
v1.0.monitorslatviewjsonbean.apimodelproperty.message.description=A message from the external system, sent when an integration run is finished.
v1.0.monitorslatviewjsonbean.apimodelproperty.runsummaryjson.description=Detailed information about an integration run, such as records processed, records failed, output files, and error logs.
v1.0.monitorslatviewjsonbean.apimodelproperty.integrationtype.description=The type of integration: import, export, or neither.
v1.0.monitorslatviewjsonbean.apimodelproperty.integrationparameters.description=A list of integration parameters that have custom values.
v1.0.monitorslatviewjsonbean.apimodelproperty.integrationsetname.description=The name of an integration set.
v1.0.monitorslatviewjsonbean.apimodelproperty.associatedtoset.description=A Boolean indicator of whether or not an integration is part of an integration set.
v1.0.monitorslatviewjsonbean.apimodelproperty.processname.description=The name of an integration or integration set.
v1.0.monitorslatviewjsonbean.apimodelproperty.extendedstatus.description=An extended set of data about the current state of an integration run after the run started.