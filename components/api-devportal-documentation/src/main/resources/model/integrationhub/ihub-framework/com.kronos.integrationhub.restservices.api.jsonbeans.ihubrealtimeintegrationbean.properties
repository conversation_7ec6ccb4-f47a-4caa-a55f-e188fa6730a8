#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubrealtimeintegrationbean.apimodel.description=Details of API integration
v1.0.ihubrealtimeintegrationbean.apimodelproperty.name.description=(Required) Unique name of the API integration.
v1.0.ihubrealtimeintegrationbean.apimodelproperty.description.description=Detailed description about the API integration.
v1.0.ihubrealtimeintegrationbean.apimodelproperty.apiintegrationkey.description=The API integration key is generated when you install an API integration, and is needed only for API integration REST API calls. In contrast, the API key calls all external REST APIs from the HTTP request header.
v1.0.ihubrealtimeintegrationbean.apimodelproperty.parameters.description=(Required) List of the integration parameters that have custom values.