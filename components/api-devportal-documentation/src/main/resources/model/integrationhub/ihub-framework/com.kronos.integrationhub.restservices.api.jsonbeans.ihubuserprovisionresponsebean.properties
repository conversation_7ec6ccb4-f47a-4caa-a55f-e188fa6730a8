#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubuserprovisionresponsebean.apimodel.description=Details of user provisioned on integration designer platform
v1.0.ihubuserprovisionresponsebean.apimodelproperty.status.description=status
v1.0.ihubuserprovisionresponsebean.apimodelproperty.provisionaccountid.description=integration designer platform account identifier
v1.0.ihubuserprovisionresponsebean.apimodelproperty.provisionuserid.description=provisioned user identifier
v1.0.ihubuserprovisionresponsebean.apimodelproperty.provisionrolename.description=role name
v1.0.ihubuserprovisionresponsebean.apimodelproperty.provisionroleid.description=role identifier
v1.0.ihubuserprovisionresponsebean.apimodelproperty.message.description=error message
v1.0.ihubuserprovisionresponsebean.apimodelproperty.errorcode.description=error code
