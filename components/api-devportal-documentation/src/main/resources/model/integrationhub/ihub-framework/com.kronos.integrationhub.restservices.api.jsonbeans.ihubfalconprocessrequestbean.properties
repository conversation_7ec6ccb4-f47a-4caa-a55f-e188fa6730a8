#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubfalconprocessrequestbean.apimodel.description=Detailed information about the integration.
v1.0.ihubfalconprocessrequestbean.apimodelproperty.id.description=(Required) Unique ID for the integration.
v1.0.ihubfalconprocessrequestbean.apimodelproperty.name.description=(Required) Unique name of the integration.
v1.0.ihubfalconprocessrequestbean.apimodelproperty.description.description=Detailed information about the integration.
v1.0.ihubfalconprocessrequestbean.apimodelproperty.user.description=(Required) Person who created the integration run.
v1.0.ihubfalconprocessrequestbean.apimodelproperty.requestname.description=(Required) Name of the integration run.
v1.0.ihubfalconprocessrequestbean.apimodelproperty.userparameters.description=(Required) List of the integration parameters that have custom values.
v1.0.ihubfalconprocessrequestbean.apimodelproperty.scheduledinfo.description=(Required) Detailed information about the scheduled integration run.
v1.0.ihubfalconprocessrequestbean.apimodelproperty.integrationrequestid.description=Unique ID for the integration instance.
v1.0.ihubfalconprocessrequestbean.apimodelproperty.integrationtype.description=Type of integration: import, export, or neither.
v1.0.ihubfalconprocessrequestbean.apimodelproperty.allowminuteinterval.description=A Boolean indicator of whether or not an integration can be scheduled minutely.
