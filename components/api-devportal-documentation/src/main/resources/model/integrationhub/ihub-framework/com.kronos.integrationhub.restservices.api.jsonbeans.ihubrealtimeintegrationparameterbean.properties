#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubrealtimeintegrationparameterbean.apimodel.description=The details like name and value of and API integration parameter. These are the configurable properties for an integration and the values provided are used during actual execution
v1.0.ihubrealtimeintegrationparameterbean.apimodelproperty.name.description=(Required) Name of the API integration parameter.
v1.0.ihubrealtimeintegrationparameterbean.apimodelproperty.description.description=Detailed description about an API integration parameter.