#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubprocessparambean.apimodel.description=Integration parameter details.
v1.0.ihubprocessparambean.apimodelproperty.id.description=parameter identifier
v1.0.ihubprocessparambean.apimodelproperty.name.description=parameter name
v1.0.ihubprocessparambean.apimodelproperty.description.description=parameter description
v1.0.ihubprocessparambean.apimodelproperty.userprompted.description=is parameter user prompted or not
v1.0.ihubprocessparambean.apimodelproperty.defaultvalue.description=default value of parameter
v1.0.ihubprocessparambean.apimodelproperty.createddtm.description=created time
v1.0.ihubprocessparambean.apimodelproperty.updateddtm.description=updation time of parameter
v1.0.ihubprocessparambean.apimodelproperty.parametertype.description=type of parameter
v1.0.ihubprocessparambean.apimodelproperty.templateparameter.description=template parameter
