#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubrealtimerequestbean.apimodel.description=Details of parameters and data required for executing API integration
v1.0.ihubrealtimerequestbean.apimodelproperty.parameters.description=A list of the integration parameters that have custom values.
v1.0.ihubrealtimerequestbean.apimodelproperty.data.description=The payload to exchange with the external system. The payload structure is predefined for each integration for the source and destination systems.
