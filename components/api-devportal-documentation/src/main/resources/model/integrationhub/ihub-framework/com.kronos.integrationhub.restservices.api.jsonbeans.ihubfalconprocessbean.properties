#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubfalconprocessbean.apimodel.description=Integration details.
v1.0.ihubfalconprocessbean.apimodelproperty.id.description=integration identifier
v1.0.ihubfalconprocessbean.apimodelproperty.name.description=integration name
v1.0.ihubfalconprocessbean.apimodelproperty.description.description=integration description
v1.0.ihubfalconprocessbean.apimodelproperty.user.description=user name
v1.0.ihubfalconprocessbean.apimodelproperty.createddtm.description=integration creation date and time.
v1.0.ihubfalconprocessbean.apimodelproperty.updateddtm.description=integration updation date and time.
v1.0.ihubfalconprocessbean.apimodelproperty.integrationprocess.description=integration template
v1.0.ihubfalconprocessbean.apimodelproperty.processparameters.description=integration parameters
v1.0.ihubfalconprocessbean.apimodelproperty.integrationenvironmentid.description=integration environment id
v1.0.ihubfalconprocessbean.apimodelproperty.integrationtype.description=type of integration
v1.0.ihubfalconprocessbean.apimodelproperty.connectiondefinition.description=connection details
v1.0.ihubfalconprocessbean.apimodelproperty.apiintegration.description=is the integration api?
v1.0.ihubfalconprocessbean.apimodelproperty.apiintegrationkey.description=key for api integrations
