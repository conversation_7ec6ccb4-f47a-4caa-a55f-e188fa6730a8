v1.0.ihubprocessapicallbackbean.apimodel.description=API Process Callback Details.
v1.0.ihubprocessapicallbackbean.apimodelproperty.id.description=The ID of a callback, which is generated in the iHub framework and sent to an integration to be used by the integration to return data to the iHub framework.
v1.0.ihubprocessapicallbackbean.apimodelproperty.executionid.description=The ID of an execution, which is a unique identifier set by <PERSON><PERSON> for all future references to that execution.
v1.0.ihubprocessapicallbackbean.apimodelproperty.status.description=The current status of the process run.
v1.0.ihubprocessapicallbackbean.apimodelproperty.callbackmessages.description=The current message of the process run.
v1.0.ihubprocessapicallbackbean.apimodelproperty.runsummary.description=The run summary of the process run.