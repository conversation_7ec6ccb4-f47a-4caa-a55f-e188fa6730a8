#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubintegrationsequencemappingbean.apimodel.description=Details of the integrations defined in an integration set.
v1.0.ihubintegrationsequencemappingbean.apimodelproperty.id.description=The mapping ID of an integration set.
v1.0.ihubintegrationsequencemappingbean.apimodelproperty.integrationsetid.description=The ID of an integration set.
v1.0.ihubintegrationsequencemappingbean.apimodelproperty.integrationid.description=The ID of an integration.
v1.0.ihubintegrationsequencemappingbean.apimodelproperty.executionorder.description=The numerical order of execution assigned to an integration.
v1.0.ihubintegrationsequencemappingbean.apimodelproperty.sequencepointer.description=The sequence pointer of an integration.
v1.0.ihubintegrationsequencemappingbean.apimodelproperty.sequencetype.description=The sequence type of an integration.