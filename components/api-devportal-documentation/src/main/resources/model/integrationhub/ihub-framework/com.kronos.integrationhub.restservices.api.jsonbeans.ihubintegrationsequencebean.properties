#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubintegrationsequencebean.apimodel.description=Details of an integration set.
v1.0.ihubintegrationsequencebean.apimodelproperty.id.description=The ID of an integration set.
v1.0.ihubintegrationsequencebean.apimodelproperty.name.description=The name of an integration set.
v1.0.ihubintegrationsequencebean.apimodelproperty.description.description=The description of an integration set.
v1.0.ihubintegrationsequencebean.apimodelproperty.abortonfailure.description=A Boolean indicator of whether or not to abort an integration set on failure.
v1.0.ihubintegrationsequencebean.apimodelproperty.integrations.description=A list of integrations associated with this integration set.
v1.0.ihubintegrationsequencebean.apimodelproperty.createdby.description=The user or entity who created an integration set.
v1.0.ihubintegrationsequencebean.apimodelproperty.createddtm.description=The date and time an integration set was created in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.ihubintegrationsequencebean.apimodelproperty.deleted.description=A Boolean indicator of whether or not an integration set has been marked for deletion.
v1.0.ihubintegrationsequencebean.apimodelproperty.ihubfalconprocessbean.description=An array containing the system process associated with an integration set.
v1.0.ihubintegrationsequencebean.apimodelproperty.updatedby.description=The user or entity who updated an integration set.
v1.0.ihubintegrationsequencebean.apimodelproperty.updateddtm.description=The date and time an integration set was updated in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).