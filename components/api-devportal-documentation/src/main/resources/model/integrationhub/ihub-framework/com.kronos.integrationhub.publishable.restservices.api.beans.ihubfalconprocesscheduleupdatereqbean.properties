v1.0.ihubfalconprocesscheduleupdatereqbean.apimodel.description=The schedule integration model.
v1.0.ihubfalconprocesscheduleupdatereqbean.apimodelproperty.id.description=The unique ID of a schedule integration.
v1.0.ihubfalconprocesscheduleupdatereqbean.apimodelproperty.integrationid.description=The ID of the installed integration that is the basis of this integration schedule.
v1.0.ihubfalconprocesscheduleupdatereqbean.apimodelproperty.name.description=A unique integration schedule name to make it easier to identify the schedule of that integration.
v1.0.ihubfalconprocesscheduleupdatereqbean.apimodelproperty.integrationparameters.description=A list of the integration parameters that have custom values.
v1.0.ihubfalconprocesscheduleupdatereqbean.apimodelproperty.scheduledinfo.description=The information related to a scheduled integration run.