#Comments for Properties
#Thu Sep 28 17:13:00 IST 2017
v1.0.ihubfalconprocesschedulereqbean.apimodel.description=Data required for scheduling of an integration
v1.0.ihubfalconprocesschedulereqbean.apimodelproperty.name.description=A unique integration schedule name to make it easier to identify the schedule of that integration
v1.0.ihubfalconprocesschedulereqbean.apimodelproperty.integrationparameters.description=A list of the integration parameters that have custom values.
v1.0.ihubfalconprocesschedulereqbean.apimodelproperty.scheduledinfo.description=The information related to a scheduled integration run.