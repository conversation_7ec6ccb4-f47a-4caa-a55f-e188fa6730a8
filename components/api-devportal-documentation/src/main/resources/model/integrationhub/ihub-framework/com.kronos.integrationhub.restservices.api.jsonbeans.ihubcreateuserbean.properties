#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubcreateuserbean.apimodel.description=Details of roles on integration designer platform
v1.0.ihubcreateuserbean.apimodelproperty.id.description=user role identifier
v1.0.ihubcreateuserbean.apimodelproperty.accountid.description=account identifier
v1.0.ihubcreateuserbean.apimodelproperty.userid.description=user identifier
v1.0.ihubcreateuserbean.apimodelproperty.roleid.description=role identifier
v1.0.ihubcreateuserbean.apimodelproperty.federationid.description=federation identifier
v1.0.ihubcreateuserbean.apimodelproperty.rolename.description=role name
v1.0.ihubcreateuserbean.apimodelproperty.notifyuser.description=whether to notify user or not?
