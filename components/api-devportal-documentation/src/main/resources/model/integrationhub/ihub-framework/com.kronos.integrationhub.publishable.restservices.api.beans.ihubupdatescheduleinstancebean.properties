v1.0.ihubUpdateScheduleInstanceBean.apimodel.description=The configuration data for updating a scheduled integration model.
v1.0.ihubUpdateScheduleInstanceBean.apimodelproperty.ihubfalconprocesscheduleupdatereqbean.description=A set of basic information related to an integration schedule, such as name and parameter values to configure.
v1.0.ihubUpdateScheduleInstanceBean.apimodelproperty.ihubschedulinginfobean.description=A set of information related to the schedule.