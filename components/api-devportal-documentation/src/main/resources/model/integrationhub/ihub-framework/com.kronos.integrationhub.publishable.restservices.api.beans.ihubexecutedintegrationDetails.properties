#Comments for Properties
#Thu Sep 28 17:13:00 IST 2017

v1.0.ihubexecutedintegrationdetails.apimodel.description=The detailed information model, including status, of an integration that has been executed, which aids in the monitoring of that integration.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.id.description=The unique ID of an integration run.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.integrationrunname.description=A unique integration run name to make it easier to identify the run of that integration.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.integrationsetname.description=A unique integration set name to make it easier to identify the set of an integration run.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.requesttype.description=The type of request that triggered the integration run, which can be scheduled or manual.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.startdatetime.description=The date when an integration run started in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddTHH:MM:SS).
v1.0.ihubexecutedintegrationdetails.apimodelproperty.enddatetime.description=The date when an integration run finished in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddTHH:MM:SS).
v1.0.ihubexecutedintegrationdetails.apimodelproperty.integrationrefid.description=The ID of the installed integration that is the basis of an integration run.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.integrationname.description=The name of the installed integration that is the basis of an integration run.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.integrationstatus.description=The current state of an integration run after the run started.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.integrationtype.description=The type of an integration run.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.user.description=The person who created an integration run.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.message.description=A message from an external system when the integration run finished.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.runsummaryjson.description=A set of detailed information about the integration run, including records processed, records failed, output files, and error logs.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.extendedstatus.description=A set of additional information about the current state of an integration run after the run started.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.integrationparameters.description=A list of the integration parameters that have custom values.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.integrationexecutionid.description=A unique reference ID for an integration run.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.associatedtoset.description=A Boolean indicator of whether or not an integration run is associated with a set.
v1.0.ihubexecutedintegrationdetails.apimodelproperty.processname.description=The name of the process associated with an integration run.