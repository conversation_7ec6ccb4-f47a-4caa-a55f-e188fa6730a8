#Comments for Properties
#Thu Sep 28 17:13:00 IST 2017
v1.0.ihubintegrationresponsebean.apimodel.description=The details of an integration process installed in the system and available to be scheduled or executed as needed. It also includes the configuration parameter details whose values can be overriden by user before submitting this integration for execution or scheduling.
v1.0.ihubintegrationresponsebean.apimodelproperty.id.description=The unique Id of the integration
v1.0.ihubintegrationresponsebean.apimodelproperty.name.description=The unique name given to an installed integration.
v1.0.ihubintegrationresponsebean.apimodelproperty.description.description=The details related to an integration's purpose and use.
v1.0.ihubintegrationresponsebean.apimodelproperty.integrationparameters.description= List of the integration parameters whosevalues can be customized by user.
