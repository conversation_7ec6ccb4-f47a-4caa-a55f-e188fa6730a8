#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubprocessrequestbean.apimodel.description=The integration instance model.
v1.0.ihubprocessrequestbean.apimodelproperty.id.description=The unique ID for the integration instance.
v1.0.ihubprocessrequestbean.apimodelproperty.name.description=(Required) The unique name of the integration run.
v1.0.ihubprocessrequestbean.apimodelproperty.user.description=(Required) The user who created the integration run.
v1.0.ihubprocessrequestbean.apimodelproperty.createddtm.description=(Required) The date and time the integration run was created in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.ihubprocessrequestbean.apimodelproperty.userparameters.description=(Required) A list of the integration parameters that have custom values.
v1.0.ihubprocessrequestbean.apimodelproperty.requesttype.description=(Required) The type of request that triggered the integration run: scheduled or manual.
v1.0.ihubprocessrequestbean.apimodelproperty.integrationid.description=(Required) The unique ID for the integration.
v1.0.ihubprocessrequestbean.apimodelproperty.integrationname.description=The unique name of the integration.
v1.0.ihubprocessrequestbean.apimodelproperty.scheduledinfo.description=Information about the scheduled integration run.
v1.0.ihubprocessrequestbean.apimodelproperty.integrationdeleted.description=A Boolean indicator of whether or not the integration for this integration run has been deleted.
v1.0.ihubprocessrequestbean.apimodelproperty.eventid.description=The event ID of an integration instance.