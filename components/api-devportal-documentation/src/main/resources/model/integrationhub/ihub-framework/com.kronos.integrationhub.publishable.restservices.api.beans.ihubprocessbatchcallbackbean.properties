v1.0.ihubprocessbatchcallbackbean.apimodel.description=Batch Process Callback Details.
v1.0.ihubprocessbatchcallbackbean.apimodelproperty.id.description=The ID of a callback, which is generated in the iHub framework and sent to an integration to be used by the integration to return data to the iHub framework.
v1.0.ihubprocessbatchcallbackbean.apimodelproperty.executionid.description=The ID of an execution, which is a unique identifier set by <PERSON><PERSON> for all future references to that execution.
v1.0.ihubprocessbatchcallbackbean.apimodelproperty.status.description=The current status of the process run.
v1.0.ihubprocessbatchcallbackbean.apimodelproperty.callbackmessages.description=The current message of the process run.
v1.0.ihubprocessbatchcallbackbean.apimodelproperty.runsummary.description=The run summary of the process run.