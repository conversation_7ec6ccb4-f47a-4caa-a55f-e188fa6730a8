#Comments for Properties
#Thu Sep 28 17:13:00 IST 2017
v1.0.ihubexecutedintegration.apimodel.description=The detailed information, such as the status, of an integration that has been executed. It aids monitoring of that integration.
v1.0.ihubexecutedintegration.apimodelproperty.id.description=Unique ID for the integration run.
v1.0.ihubexecutedintegration.apimodelproperty.name.description=A unique integration run name to make it easier to identify the run of that integration
v1.0.ihubexecutedintegration.apimodelproperty.requesttype.description=Type of request that triggered the integration run: scheduled or manual.
v1.0.ihubexecutedintegration.apimodelproperty.startdate.description=The date when the integration run started.
v1.0.ihubexecutedintegration.apimodelproperty.enddate.description=The date when the integration run finished.
v1.0.ihubexecutedintegration.apimodelproperty.integrationid.description=The ID of the installed integration that is the basis of this integration run
v1.0.ihubexecutedintegration.apimodelproperty.integrationname.description=Name of the installed integration that is the basis of this integration run
v1.0.ihubexecutedintegration.apimodelproperty.status.description=The current state of an integration run after the run started.
v1.0.ihubexecutedintegration.apimodelproperty.user.description=Person who created the integration run.
v1.0.ihubexecutedintegration.apimodelproperty.message.description=Message from the external system when the integration run finished.
v1.0.ihubexecutedintegration.apimodelproperty.runsummary.description=Detailed information about the integration run such as records processed, records failed, output files, error logs.
v1.0.ihubexecutedintegration.apimodelproperty.extendedStatus.description=Additional information about the current state of an integration run after the run started.
v1.0.ihubexecutedintegration.apimodelproperty.integrationparameters.description=List of the integration parameters that have custom values.
v1.0.ihubexecutedintegration.apimodelproperty.integrationparameters.description=A list of the integration parameters that have custom values.
v1.0.ihubexecutedintegration.apimodelproperty.executionReferenceId.description=Unique reference ID for an integration run
