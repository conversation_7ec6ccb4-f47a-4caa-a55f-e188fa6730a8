#Comments for Properties
#Fri Mar 24 08:49:09 IST 2017
v1.0.ihubintgsequenceinstancebean.apimodel.description=Detailed information about the integration set.
v1.0.ihubintgsequenceinstancebean.apimodelproperty.integrationsetdefinitionid.description=(Required) Unique ID for the integration set.
v1.0.ihubintgsequenceinstancebean.apimodelproperty.name.description=(Required) Unique name of the integration set.
v1.0.ihubintgsequenceinstancebean.apimodelproperty.description.description=Detailed description of an integration in an integration set.
v1.0.ihubintgsequenceinstancebean.apimodelproperty.user.description=(Required) Person who created this run of the integration set.
v1.0.ihubintgsequenceinstancebean.apimodelproperty.createddtm.description=(Required) Date the scheduled run of the integration set was created.
v1.0.ihubintgsequenceinstancebean.apimodelproperty.scheduledinfo.description=(Required) Information about the scheduled run of the integration set.
v1.0.ihubintgsequenceinstancebean.apimodelproperty.integrationsettype.description=(Required) Type of request that triggered the run of the integration set: scheduled or manual.
v1.0.ihubintgsequenceinstancebean.apimodelproperty.requestname.description=(Required) Name of the run of the integration set.
v1.0.ihubintgsequenceinstancebean.apimodelproperty.intgsetinstancerequestid.description=Unique ID for the run of the integration set instance.
v1.0.ihubintgsequenceinstancebean.apimodelproperty.integrationlist.description=(Required) List of the integrations and parameters that are in the integration set.
