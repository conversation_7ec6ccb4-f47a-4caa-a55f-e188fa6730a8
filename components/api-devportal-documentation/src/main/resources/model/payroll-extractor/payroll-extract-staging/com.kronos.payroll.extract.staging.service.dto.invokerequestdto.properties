v1.0.invokerequest.apimodel.description=The payroll staging request model.
v1.0.invokereqeust.apimodelproperty.where.description=The where criteria in which conditions for filtering employees are specified.
v1.0.invokerequest.apimodelproperty.employees.description=The employees for whom payroll is run.
v1.0.invokerequest.apimodelproperty.datereange.description=The date range for the staged payroll run.
v1.0.invokerequest.apimodelproperty.customparameter1.description=The first custom parameter.
v1.0.invokerequest.apimodelproperty.customparameter2.description=The second custom parameter.
v1.0.invokerequest.apimodelproperty.ignoresignoff.description=A Boolean indicator of whether or not to ignore sign off.
v1.0.invokerequest.apimodelproperty.includepaycodes.description=A reference to the include pay codes.
v1.0.invokerequest.apimodelproperty.excludepaycodes.description=A reference to the exclude pay codes.
v1.0.invokerequest.apimodelproperty.exporttype.description=The export type. Valid values include 'Payroll', 'Accrual', and 'Both'.
v1.0.invokerequest.apimodelproperty.exporttype.allowablevalues=Payroll,Accrual,Both
v1.0.invokerequest.apimodelproperty.processterminatedemployeesonly.description=A Boolean indicator of whether or not to only process terminated employees.
v1.0.invokerequest.apimodelproperty.paycodemappingtable.description=The pay code mapping table.
v1.0.invokerequest.apimodelproperty.executionId.description=The execution ID of a payroll staging request.
v1.0.invokerequest.apimodelproperty.historicalcorrections.description=A string that determines how to handle historical corrections. Valid values include 'Included in Totals', 'Exclude in Totals', and 'Correction only'.
v1.0.invokerequest.apimodelproperty.historicalCorrections.allowablevalues=Included in Totals,Exclude in Totals,Correction only
v1.0.invokerequest.apimodelproperty.processBasedOn.description=The conditions on which to base payroll processing.
v1.0.invokerequest.apimodelproperty.includeCommentsForPayCodes.description=The Pay Codes for which you want to extract Comments and Notes.
v1.0.invokerequest.apimodelproperty.customPersonDates.description=The object reference models for which you want to extract Custom Person Dates.
v1.0.invokerequest.apimodelproperty.customFields.description=The object reference models for which you want to extract Custom Fields.