v1.0.processBasedOnDTO.apimodel.description=The payroll process-based-on model.
v1.0.processBasedOnDTO.apimodelproperty.type.description=<p>The type on which the payroll staging process is based.</p><p>Possible values include:</p><ul><li>PRIMARY</li><li>WORKED</li><li>POSITION_ASSIGNMENT</li></ul>
v1.0.processBasedOnDTO.apimodelproperty.filterBy.description=<p>The conditions by which payroll processing is filtered. This property is only needed if the type is <code>WORKED</code> or <code>POSITION_ASSIGNMENT</code>. The default value is <code>JOB</code> and valid values include:</p><ul><li>JOB</li><li>LABOR_CATEGORY</li><li>COST_CENTER</li><li>POSITION_CUSTOM_FIELD</li></ul>
