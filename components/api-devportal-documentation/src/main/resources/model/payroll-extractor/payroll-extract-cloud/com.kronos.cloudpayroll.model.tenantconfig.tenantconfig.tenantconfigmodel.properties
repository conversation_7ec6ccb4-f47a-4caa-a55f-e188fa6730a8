v1.0.tenantconfigmodel.apimodel.description=Model describing tenant config.
v1.0.tenantconfigmodel.apimodelproperty.customtableapicallscountlimit.description=The custom table api calls count limit.
v1.0.tenantconfigmodel.apimodelproperty.apiasyncpingseconds.description=The api async ping seconds.
v1.0.tenantconfigmodel.apimodelproperty.exportprocessedbyteslimit.description=The export processed bytes limit.
v1.0.tenantconfigmodel.apimodelproperty.exportstagingtablecountlimit.description=The export staging table count limit.
v1.0.tenantconfigmodel.apimodelproperty.customtableuploadbyteslimit.description=The custom table upload bytes limit.
v1.0.tenantconfigmodel.apimodelproperty.customtablecountlimit.description=The custom table count limit.
v1.0.tenantconfigmodel.apimodelproperty.customtablecolumnsmaxnum.description=The custom table columns maximum number.
v1.0.tenantconfigmodel.apimodelproperty.exportprocessedbyteslimitforsync.description=The export processed bytes limit for the Sync call.