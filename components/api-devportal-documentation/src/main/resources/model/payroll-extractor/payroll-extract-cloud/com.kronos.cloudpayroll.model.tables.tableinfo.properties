v1.0.tableinfo.apimodel.description=Model describing table info.
v1.0.tableinfo.apimodelproperty.name.description=The table info name.
v1.0.tableinfo.apimodelproperty.type.description=The table info type.
@v1.0.tableinfo.apimodelproperty.type.allowablevalues=CUSTOM, STAGE
v1.0.tableinfo.apimodelproperty.rows.description=The number of rows.
v1.0.tableinfo.apimodelproperty.creationtime.description=The time when table info was created.
v1.0.tableinfo.apimodelproperty.partitionconfig.description=The partition config.
v1.0.tableinfo.apimodelproperty.schema.description=The list of table schema columns.
