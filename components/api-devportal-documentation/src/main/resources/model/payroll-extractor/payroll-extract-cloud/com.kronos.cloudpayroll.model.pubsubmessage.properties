v1.0.pubsubmessage.apimodel.description=Model describing pub sub message.
v1.0.pubsubmessage.apimodelproperty.message.description=The pub sub message.

v1.0.message.apimodel.description=The message that is published by publishers and consumed by subscribers.
v1.0.message.apimodelproperty.messageid.description=The ID of the message, assigned by the server when the message is published.
v1.0.message.apimodelproperty.publishtime.description=The time at which the message was published.
v1.0.message.apimodelproperty.data.description=A base64-encoded message string.
