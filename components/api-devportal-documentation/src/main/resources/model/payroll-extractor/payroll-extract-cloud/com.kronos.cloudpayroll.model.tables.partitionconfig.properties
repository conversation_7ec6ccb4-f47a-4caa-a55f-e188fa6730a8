v1.0.partitionconfig.apimodel.description=Model describing partition config.
v1.0.partitionconfig.apimodelproperty.column.description=The name of the partitioning column. In the table schema, this column must be a TIMESTAMP, DATETIME, or DATE type.
v1.0.partitionconfig.apimodelproperty.granularity.description=The partitioning granularity.
v1.0.partitionconfig.apimodelproperty.expirationhours.description=The expiration time for the table's partitions, in hours.
