v1.0.defaulttenantconfigmodel.apimodel.description=Model describing default tenant config.
v1.0.defaulttenantconfigmodel.apimodelproperty.customtableapicallscountlimit.description=The custom table api calls count limit.
v1.0.defaulttenantconfigmodel.apimodelproperty.apiasyncpingseconds.description=The api async ping seconds.
v1.0.defaulttenantconfigmodel.apimodelproperty.exportprocessedbyteslimit.description=The export processed bytes limit.
v1.0.defaulttenantconfigmodel.apimodelproperty.exportstagingtablecountlimit.description=The export staging table count limit.
v1.0.defaulttenantconfigmodel.apimodelproperty.customtableuploadbyteslimit.description=The custom table upload bytes limit.
v1.0.defaulttenantconfigmodel.apimodelproperty.customtablecountlimit.description=The custom table count limit.
v1.0.defaulttenantconfigmodel.apimodelproperty.customtablecolumnsmaxnum.description=The custom table columns maximum number.
v1.0.defaulttenantconfigmodel.apimodelproperty.exportprocessedbyteslimitforsync.description=The export processed bytes limit for the Sync call.