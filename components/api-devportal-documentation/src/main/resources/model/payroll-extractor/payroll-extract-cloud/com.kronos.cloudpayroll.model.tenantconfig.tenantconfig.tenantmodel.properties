v1.0.tenantmodel.apimodel.description=Model describing tenant.
v1.0.tenantmodel.apimodelproperty.shortname.description=The tenant short name.
v1.0.tenantmodel.apimodelproperty.status.description=The tenant status.
v1.0.tenantmodel.apimodelproperty.status.allowablevalues=ACTIVE, INACTIVE, DELETED
v1.0.tenantmodel.apimodelproperty.tenantconfig.description=The tenant config.
v1.0.tenantmodel.apimodelproperty.defaulttenantconfig.description=The tenant default config.
v1.0.tenantmodel.apimodelproperty.customtables.description=The list of custom table models.
v1.0.tenantmodel.apimodelproperty.stagingtables.description=The list of staging table models.
v1.0.tenantmodel.apimodelproperty.customtableapicallscount.description=The custom table api calls count.
