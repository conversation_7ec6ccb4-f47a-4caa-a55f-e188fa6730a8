v1.0.forecastingaudit.apimodel.description=Model for Forecasting Audit.
v1.0.forecastingaudit.apimodelproperty.id.description=The ID of the forecasting audit item.
v1.0.forecastingaudit.apimodelproperty.audittype.description=The Forecasting Audit Type object.
v1.0.forecastingaudit.apimodelproperty.actiontype.description=The Audit Action object.
v1.0.forecastingaudit.apimodelproperty.user.description=A reference to the user object.
v1.0.forecastingaudit.apimodelproperty.auditdatetime.description=The date and time of the auditing.
v1.0.forecastingaudit.apimodelproperty.datasource.description=The datasource object which includes a functional area name, client name, server name, and user name.
v1.0.forecastingaudit.apimodelproperty.originalvalue.description=The original value before action was performed.
v1.0.forecastingaudit.apimodelproperty.changedvalue.description=The edited value.
v1.0.forecastingaudit.apimodelproperty.orgnode.description=A reference to the org node object.
v1.0.forecastingaudit.apimodelproperty.childnode.description=A reference to the child org node object.
v1.0.forecastingaudit.apimodelproperty.datespan.description=The date span, provided as startDate and endDate, for which audited action was performed.
v1.0.forecastingaudit.apimodelproperty.date.description=The date for which the audited action was performed.
v1.0.forecastingaudit.apimodelproperty.driver.description=A reference to the driver object.
v1.0.forecastingaudit.apimodelproperty.datetimespan.description=The date and time span, provided as startDateTime and endDateTime, for which audited action was performed.