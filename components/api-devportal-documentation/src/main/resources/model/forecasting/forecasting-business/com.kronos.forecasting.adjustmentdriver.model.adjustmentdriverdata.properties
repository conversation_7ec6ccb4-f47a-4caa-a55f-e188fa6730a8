v1.0.adjustmentdriverdata.apimodel.description=Model for Adjustment Driver Data.
v1.0.adjustmentdriverdata.apimodelproperty.daterange.description=The date range, provided as startDate and endDate.
v1.0.adjustmentdriverdata.apimodelproperty.location.description=A reference to the location object.
v1.0.adjustmentdriverdata.apimodelproperty.driver.description=A reference to the adjustment driver object.
v1.0.adjustmentdriverdata.apimodelproperty.weeklyamount.description=The weekly amount value of an adjustment driver.