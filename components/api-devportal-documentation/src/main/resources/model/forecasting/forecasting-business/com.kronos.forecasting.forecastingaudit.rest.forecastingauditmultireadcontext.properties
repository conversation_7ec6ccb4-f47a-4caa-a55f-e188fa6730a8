v1.0.forecastingauditingmultireadcontext.apimodel.description=Request Context for Search Forecasting Audit data.
v1.0.forecastingauditingmultireadcontext.apimodelproperty.orgNodes.description=A list of references to org node objects.
v1.0.forecastingauditingmultireadcontext.apimodelproperty.entityTypes.description=A list of forecasting audit type names.
v1.0.forecastingauditingmultireadcontext.apimodelproperty.entityTypes.allowablevalues=VOLUME_FORECAST, VOLUME_FORECAST_TOTAL, VOLUME_FORECAST_RESTORE, CUSTOM_LABOR_DRIVER, ADJUSTMENT_LABOR_DRIVER, LABOR_FORECAST_HEADCOUNT, TRAFFIC_PATTERN, LABOR_BUDGET, VOLUME_BUDGET, VOLUME_FORECAST_INTERVAL
v1.0.forecastingauditingmultireadcontext.apimodelproperty.daterange.description=The date range, provided as startDate and endDate or as a reference to a symbolicPeriod.
v1.0.forecastingauditingmultireadcontext.apimodelproperty.includeauditdataforchildcategoryorgnodes.description=A Boolean indicator of whether or not to include Forecasting Audit data for child organizational nodes with the type 'Category' in the response.
