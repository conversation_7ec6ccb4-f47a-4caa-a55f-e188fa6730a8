v1.0.laborforecastlimit.apimodel.description=Model for labor forecast limit.
v1.0.laborforecastlimit.apimodelproperty.id.description=The unique ID of a labor forecast limit.
v1.0.laborforecastlimit.apimodelproperty.name.description=The unique name of a labor forecast limit.
v1.0.laborforecastlimit.apimodelproperty.description.description=The description of a labor forecast limit.
v1.0.laborforecastlimit.apimodelproperty.active.description=A Boolean indicator of whether or not a labor forecast limit is active.
v1.0.laborforecastlimit.apimodelproperty.version.description=The version of a labor forecast limit.
v1.0.laborforecastlimit.apimodelproperty.breakfactor.description=The value of a break factor expressed as a percent, from 0 to 100.
v1.0.laborforecastlimit.apimodelproperty.fatiguefactor.description=The value of a fatigue factor expressed as a percent, from 0 to 100.
v1.0.laborforecastlimit.apimodelproperty.items.description=A list of labor forecast limit items.
