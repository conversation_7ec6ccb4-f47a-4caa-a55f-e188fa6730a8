v1.0.categorypropertysetassignment.apimodel.description=Model for Category Property Set Assignment.
v1.0.categorypropertysetassignment.apimodelproperty.category.description=A reference to the Category object.
v1.0.categorypropertysetassignment.apimodelproperty.categorypropertyset.description=The Category Property Set object assigned to a given Category.
v1.0.categorypropertysetassignment.apimodelproperty.datespan.description=The date span of a Category Property Set Assignment, provided as <code>startDate</code> and <code>endDate</code>.
v1.0.categorypropertysetassignment.apimodelproperty.inherited.description=A Boolean indicator of whether or not a Category Property Set Assignment is inherited.
