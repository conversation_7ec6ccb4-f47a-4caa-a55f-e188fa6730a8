v1.0.taskgroup.laborhourtotalallocationrule.apimodel.description=Model for labor hour allocation rule.
v1.0.taskgroup.laborhourtotalallocationrule.apimodelproperty.laborhourallocationrule.description=A type indicating how to allocate labor hours. The default value is ACTUAL_DAY. ACTUAL_DAY: Labor hours are split as they fall across the day divide. START_DAY: All hours for the job are applied to the day on which the labor hours begin. END_DAY: All hours for the job are applied to the day on which the labor hours end.
v1.0.taskgroup.laborhourtotalallocationrule.apimodelproperty.laborhourallocationrule.allowablevalues=ACTUAL_DAY, START_DAY, END_DAY
v1.0.taskgroup.laborhourtotalallocationrule.apimodelproperty.skipbreaksinlabor.description=A Boolean indicator of whether or not the Forecast Manager ignores breaks when it calculates labor hours for a given day when there are gaps of a specified duration in the labor requirements on either side of the day divide.
v1.0.taskgroup.laborhourtotalallocationrule.apimodelproperty.maximumbreakduration.description=The number of minutes that defines a break in labor from 0 to 120. The number must be a multiple of 15.
v1.0.taskgroup.laborhourtotalallocationrule.apimodelproperty.breakplacementwindow.description=A window (in minutes) on each side of the day divide. Any gap in labor with a start day and end day that falls within this window and meets the maximum break duration is ignored. For example, if you define the window to be 60 minutes, then the window extends from one hour before the day divide to one hour after the day divide. The numerical value of this window represents minutes and can be 0 to 360. The value must be a multiple of 15 and greater than or equal to the maximum break duration.