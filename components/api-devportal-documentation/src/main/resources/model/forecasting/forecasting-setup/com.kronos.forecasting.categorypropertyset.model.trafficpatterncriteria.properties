v1.0.trafficpatterncriteria.apimodel.description=Model for Traffic Pattern Criteria.
v1.0.trafficpatterncriteria.apimodelproperty.eventdayscore.description=The event day percentage score for traffic pattern calculation.
v1.0.trafficpatterncriteria.apimodelproperty.nearestdayscore.description=The nearest day percentage score for traffic pattern calculation.
v1.0.trafficpatterncriteria.apimodelproperty.openclosehoursscore.description=The open-close hours percentage score for traffic pattern calculation.
v1.0.trafficpatterncriteria.apimodelproperty.samedayscore.description=The same day percentage score for traffic pattern calculation.
v1.0.trafficpatterncriteria.apimodelproperty.weekofpatternhistory.description=The number of weeks of history needed to calculate traffic pattern.