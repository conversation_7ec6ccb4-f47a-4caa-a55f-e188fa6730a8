v1.0.laborconstraintentry.apimodel.description=Model for a Labor Constraint Entry.
v1.0.laborconstraintentry.apimodelproperty.alljobschecked.description=A Boolean indicator of whether or not all jobs are included.
v1.0.laborconstraintentry.apimodelproperty.genericdepartmentjobs.description=The generic department job pairs of a Labor Constraint Entry.
v1.0.laborconstraintentry.apimodelproperty.sundaychecked.description=A Boolean indicator of whether or not Sunday is included.
v1.0.laborconstraintentry.apimodelproperty.mondaychecked.description=A Boolean indicator of whether or not Monday is included.
v1.0.laborconstraintentry.apimodelproperty.tuesdaychecked.description=A Boolean indicator of whether or not Tuesday is included.
v1.0.laborconstraintentry.apimodelproperty.wednesdaychecked.description=A Boolean indicator of whether or not Wednesday is included.
v1.0.laborconstraintentry.apimodelproperty.thursdaychecked.description=A Boolean indicator of whether or not Thursday is included.
v1.0.laborconstraintentry.apimodelproperty.fridaychecked.description=A Boolean indicator of whether or not Friday is included.
v1.0.laborconstraintentry.apimodelproperty.saturdaychecked.description=A Boolean indicator of whether or not Saturday is included.
v1.0.laborconstraintentry.apimodelproperty.percent.description=The percent of a Labor Constraint Entry.
v1.0.laborconstraintentry.apimodelproperty.order.description=The order of a Labor Constraint Entry.