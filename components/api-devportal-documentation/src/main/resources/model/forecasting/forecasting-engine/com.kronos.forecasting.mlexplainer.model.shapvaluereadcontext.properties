v1.0.shapvaluereadcontext.apimodel.description=Read and delete SHAP Values request model.
v1.0.shapvaluereadcontext.apimodelproperty.orgnode.description=Location object reference (may be parent of categories).
v1.0.shapvaluereadcontext.apimodelproperty.volumedriver.description=Volume driver object reference.
v1.0.shapvaluereadcontext.apimodelproperty.mlconfiguration.description=Machine learning configuration object Reference.
v1.0.shapvaluereadcontext.apimodelproperty.datespan.description=Date span.
v1.0.shapvaluereadcontext.apimodelproperty.explainertype.description=SHAP Values calculation type: ML_EXPLAINER_GLOBAL or ML_EXPLAINER_LOCAL.