v1.0.leavecasedefaultsdto.apimodel.description=The leave case defaults model.
v1.0.leavecasedefaultsdto.apimodelproperty.leaveCase.description=A reference to the leave case to which defaults belong.
v1.0.leavecasedefaultsdto.apimodelproperty.commitTo.description=<p>The destination where Leave edits are committed to, either Timecard or Schedule.</p><p><strong>Note:</strong> Scheduled shifts are overridden when <code>overrideShiftType</code> is defined. If <code>overrideShiftType</code> is not defined then scheduled shifts are not overridden. This property is only applicable when <code>commitTo</code> is set to <code>schedule</code>.</p>
v1.0.leavecasedefaultsdto.apimodelproperty.overrideShiftType.description=<p>The override shift type.</p><p><strong>Note:</strong> Scheduled shifts are overridden when <code>overrideShiftType</code> is defined. If <code>overrideShiftType</code> is not defined then scheduled shifts are not overridden. This property is only applicable when <code>commitTo</code> is set to <code>schedule</code>.</p>
v1.0.leavecasedefaultsdto.apimodelproperty.startTime.description=The Leave Edit start time in ISO_LOCAL_TIME format (HH:mm:ss.SSS) which is used during Add Leave Time by default.
v1.0.leavecasedefaultsdto.apimodelproperty.defaultPaidAmountInSeconds.description=The Leave Edit paid amount in seconds which is used during Add Leave Time by default.
v1.0.leavecasedefaultsdto.apimodelproperty.defaultTrackingAmountInSeconds.description=The Leave Edit tracking amount in seconds which is used during Add Leave Time by default.
v1.0.leavecasedefaultsdto.apimodelproperty.dateDefaultsType.description=The date defaults type which allows the caller to choose days or to use scheduled days. Valid values include: <code>Include days</code> and <code>Use scheduled days only</code>.
v1.0.leavecasedefaultsdto.apimodelproperty.defaultWeekdays.description=The default days of the week which is used during Add Leave Time by default.
v1.0.leavecasedefaultsdto.apimodelproperty.transfer.description=The Leave Edit transfer which is used during Add Leave Time by default.
v1.0.leavecasedefaultsdto.apimodelproperty.leaveCaseAssignments.description=The employee assignments associated to a leave case.