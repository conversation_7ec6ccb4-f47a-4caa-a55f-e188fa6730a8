v2.0.whatifemployeesruleviolationsrequest.apimodel.description=Model containing information for rule violation evaluation.
v2.0.whatifemployeesruleviolationsrequest.apimodelproperty.dateSpan.description=The effective datespan for which a result is obtained.
v2.0.whatifemployeesruleviolationsrequest.apimodelproperty.employeeSchedules.description=A collection of employees and their "what if" schedules for which rule violations are evaluated, It will also be used to save the schedule accordingly.
v2.0.whatifemployeesruleviolationsrequest.apimodelproperty.scheduleRules.description=Model containing rule set configuration.
v2.0.whatifemployeesruleviolationsrequest.apimodelproperty.includeHoursAndCosts.description=A Boolean indicator of whether or not additional KPI hours and costs information is returned.


v2.0.whatifemployeesruleviolationsupdaterequest.apimodelproperty.rulesToExclude.description=The rules to exclude from the evaluation.
v2.0.whatifemployeesruleviolationsupdaterequest.apimodelproperty.maximumAllowedSeverity.description=The maximum allowed severity of the rule violations, NO_SAVE is not allowed.
v2.0.whatifemployeesruleviolationsupdaterequest.apimodelproperty.saveOption.description=Save option to toggle if to only evaluate the schedule for the employees or if true, save the schedule after successful evaluation.