#Comments for Properties
v1.0.scheduling.business.metrics.indicatorspanvalue.apimodel.description=Model containing a series of day values associated with an indicator.
v1.0.scheduling.business.metrics.indicatorspanvalue.apimodelproperty.indicator.description=The indicator for this span.
v1.0.scheduling.business.metrics.indicatorspanvalue.apimodelproperty.daysvalues.description=The list of day values for this span.
v1.0.scheduling.business.metrics.indicatorspanvalue.apimodelproperty.horizontaltotal.description=The horizontal total for this span.
v1.0.scheduling.business.metrics.indicatorspanvalue.apimodelproperty.formattedhorizontaltotal.description=The formatted horizontal total for this span.
v1.0.scheduling.business.metrics.indicatorspanvalue.apimodelproperty.scale.description=The scale for this span.
v1.0.scheduling.business.metrics.indicatorspanvalue.apimodelproperty.valuetype.description=The value type for values in this span.
