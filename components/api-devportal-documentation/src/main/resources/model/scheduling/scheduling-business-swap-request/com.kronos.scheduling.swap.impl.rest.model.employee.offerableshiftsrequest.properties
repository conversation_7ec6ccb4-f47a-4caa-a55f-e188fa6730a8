#Model
v1.0.offerableshiftsrequest.apimodel.description=The offerable shift requests model.
#Properties
v1.0.offerableshiftsrequest.apimodelproperty.requestsubtype.description=The swap request subtype reference.
v1.0.offerableshiftsrequest.apimodelproperty.startdate.description=Start date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.offerableshiftsrequest.apimodelproperty.enddate.description=End date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.offerableshiftsrequest.apimodelproperty.position.description=A reference to the position object.