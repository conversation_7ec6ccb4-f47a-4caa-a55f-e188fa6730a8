#Comments for Properties
#Tue Mar 21 06:43:12 IST 2017
v1.0.createmembershipforemployeeswhere.apimodel.description=Parameters for adding employee to a group membership.
v1.0.createmembershipforemployeeswhere.apimodelproperty.groupref.description=The ID for the schedule group.
v1.0.createmembershipforemployeeswhere.apimodelproperty.startdate.description=Inclusive start date range of the schedule group membership.
v1.0.createmembershipforemployeeswhere.apimodelproperty.enddate.description=Exclusive date range of the schedule group membership.
v1.0.createmembershipforemployeeswhere.apimodelproperty.isforever.description=Whether membership in the group goes to forever or has an end date.
v1.0.createmembershipforemployeeswhere.apimodelproperty.removefromothergroups.description=If true, removes employees from existing memberships.
