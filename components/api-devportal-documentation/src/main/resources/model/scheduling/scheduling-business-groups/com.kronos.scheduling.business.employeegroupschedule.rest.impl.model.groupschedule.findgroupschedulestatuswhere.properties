v1.0.findgroupschedulestatuswhere.apimodel.description=A model of the conditions related to employee, hyperfind, groups or employment terms, or locations used in Retrieve Group or Employment Terms Schedule Statuses requests.
v1.0.findgroupschedulestatuswhere.apimodelproperty.groups.description=An object representing a collection of groups or employment terms.
v1.0.findgroupschedulestatuswhere.apimodelproperty.employees.description=An object representing a collection of employees.
v1.0.findgroupschedulestatuswhere.apimodelproperty.locations.description=An object representing a collection of locations.
v1.0.findgroupschedulestatuswhere.apimodelproperty.hyperfind.description=A reference to a Hyperfind query.