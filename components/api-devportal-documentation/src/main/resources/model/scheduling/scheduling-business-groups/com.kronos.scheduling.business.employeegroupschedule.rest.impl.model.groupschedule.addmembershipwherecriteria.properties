#Comments for Properties
#Tue Mar 21 06:43:12 IST 2017
v1.0.addmembershipwherecriteria-where.apimodel.description=Remove membership query parameters.
v1.0.addmembershipwherecriteria-where.apimodelproperty.employeeids.description=Employees ids to filter against.
v1.0.addmembershipwherecriteria-where.apimodelproperty.employeerefs.description=Employees refs to filter against.
v1.0.addmembershipwherecriteria-where.apimodelproperty.startdate.description=The start date range -- inclusive.
v1.0.addmembershipwherecriteria-where.apimodelproperty.enddate.description=The end date range -- exclusive.
v1.0.addmembershipwherecriteria-where.apimodelproperty.groupid.description=Group id to remove employees from.
v1.0.addmembershipwherecriteria-where.apimodelproperty.groupref.description=Group ref to remove employees from.
v1.0.addmembershipwherecriteria-where.apimodelproperty.isforever.description=Memberships ends at end of time (forever)
v1.0.addmembershipwherecriteria-where.apimodelproperty.removefromothergroups.description=If true, removes employees from existing memberships.
v1.0.addmembershipwherecriteria.apimodel.description=Parameters for add employee to a group.
v1.0.addmembershipwherecriteria.apimodelproperty.where.description=Where criteria for remove.
