#Comments for Properties
v1.0.teamdefinition.apimodel.description=The team definition model.
v1.0.teamdefinition.apimodelproperty.id.description=The ID of a team definition.
v1.0.teamdefinition.apimodelproperty.name.description=The name of a team definition.
v1.0.teamdefinition.apimodelproperty.description.description=The description of a team definition.
v1.0.teamdefinition.apimodelproperty.startDate.description=Effective start date.
v1.0.teamdefinition.apimodelproperty.endDate.description=Effective end date.
v1.0.teamdefinition.apimodelproperty.teamSource.description=The team source properties.
v1.0.teamdefinition.apimodelproperty.createdByManager.description=The name of the manager who created the team definition.
v1.0.teamdefinition.apimodelproperty.processing.description=A Boolean indicator of whether or not employee team members are still being resolved.
v1.0.teamdefinition.apimodelproperty.teamDefinitionEmployees.description=The employee team members determined by the team source properties.
v1.0.teamdefinition.apimodelproperty.guidedRecommendation.description=The attributes specific to a team definition for guided recommendations.
v1.0.teamdefinition.apimodelproperty.paycodeprofile.description=A reference to a paycode profile object.