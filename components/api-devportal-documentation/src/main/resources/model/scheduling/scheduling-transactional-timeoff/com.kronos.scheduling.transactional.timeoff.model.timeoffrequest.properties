#Comments for Properties
v1.0.timeoffrequest.apimodel.description=Model containing information for a transactional time off request.
v1.0.timeoffrequest.apimodelproperty.id.description=The ID for the time-off request.
# v1.0.timeoffrequest.apimodelproperty.creator.description=The user id who created the time off request.
# v1.0.timeoffrequest.apimodelproperty.employee.description=The employee id whom the time off request is for.
v1.0.timeoffrequest.apimodelproperty.createdatetime.description=The date and time when the time-off request was created in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss). The date and time are specified according to Greenwich Mean Time (GMT). This property is read only.
# v1.0.timeoffrequest.apimodelproperty.requestsubtype.description=The sub type for this time off request.
# v1.0.timeoffrequest.apimodelproperty.currentstatus.description=The current status of the time off request.
v1.0.timeoffrequest.apimodelproperty.periods.description=The periods contained in this time off request.
v1.0.timeoffrequest.apimodelproperty.approvalperiods.description=The approval periods contained in this time off request.
v1.0.timeoffrequest.apimodelproperty.nextvalidstatuses.description=A list of statuses to which a time-off request can transition.
v1.0.timeoffrequest.apimodelproperty.createopenshiftsexcludedon.description=A list of dates for which open shifts will not be created if underlying shifts are overridden. This property cannot be passed in a Create Employee Time Off Request (POST /v1/scheduling/employee_timeoff) request payload, but is valid in a Create Time Off Request as Manager (POST /v1/scheduling/timeoff) request payload.
v1.0.timeoffrequest.apimodelproperty.commentnotes.description=A list of comments and notes.
v1.0.timeoffrequest.apimodelproperty.requeststatuschanges.description=A list of historical status changes for the time-off request.
v1.0.timeoffrequest.apimodelproperty.revisionid.description=The revision id of the time off request.
# v1.0.timeoffrequest.apimodelproperty.version.description=The version of the time off request in long value (milliseconds)
# v1.0.timeoffrequest.apimodelproperty.approvalinfo.description=The approval info of the time off request.
