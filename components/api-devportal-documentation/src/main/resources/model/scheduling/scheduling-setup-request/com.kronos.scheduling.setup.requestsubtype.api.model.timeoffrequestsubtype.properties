v1.0.timeoffrequestsubtype.apimodel.description=Model representing the details of a Time Off request subtype.
v1.0.timeoffrequestsubtype.apimodelproperty.id.description=The ID of a Time Off request subtype.
v1.0.timeoffrequestsubtype.apimodelproperty.name.description=The name of a Time Off request subtype.
v1.0.timeoffrequestsubtype.apimodelproperty.localizedname.description=The localized name of a Time Off request subtype.
v1.0.timeoffrequestsubtype.apimodelproperty.description.description=The description of a Time Off request subtype.
v1.0.timeoffrequestsubtype.apimodelproperty.symbol.description=The symbol of a Time Off request subtype.
v1.0.timeoffrequestsubtype.apimodelproperty.requesttype.description=The Time Off request subtype.
v1.0.timeoffrequestsubtype.apimodelproperty.priority.description=The priority of a Time Off request subtype.
v1.0.timeoffrequestsubtype.apimodelproperty.displayaccruals.description=A Boolean indicator of whether or not to display accruals for Time Off requests.
v1.0.timeoffrequestsubtype.apimodelproperty.displayaddanotherbuttonforadditionaldates.description=A Boolean indicator of whether or not to display a button for additional dates for Time Off requests.
v1.0.timeoffrequestsubtype.apimodelproperty.displayrequestsummary.description=A Boolean indicator of whether or not to display a request summary for Time Off requests.
v1.0.timeoffrequestsubtype.apimodelproperty.durationbeforepaycodepanelorder.description=The duration before paycode for a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.enterhoursasstarttimeplusduration.description=The start time plus duration of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.customdurationlabel.description=The custom duration label of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.localizedcustomdurationlabel.description=The localized custom duration label of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.custompaycodelabel.description=The custom paycode label of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.localizedcustompaycodelabel.description=The localized custom paycode label of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.defaultpaycode.description=The default pay code of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.displayadditionalpaycodes.description=A Boolean indicator of whether or not to display additional pay codes.
v1.0.timeoffrequestsubtype.apimodelproperty.managersavailablepaycodes.description=The managers available pay codes of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.defaultsymbolicsource.description=The default symbolic source of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.fixedvaluehours.description=The fixed value (hours) of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.defaultstarttime.description=The default start time of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.defaultsymbolicamounts.description=The list of default symbolic amounts of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.employeesubmissionnotification.description=The employee submission notification of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.employeependingnotification.description=The employee pending notification of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.employeenotification.description=The employee notification of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.managernotification.description=The manager notification of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.colleaguesnotification.description=The colleagues notification of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.addapprovedrequesttooutlookcalendar.description=A Boolean indicator of whether or not to add approved request to outlook calendar.
v1.0.timeoffrequestsubtype.apimodelproperty.automaticapproval.description=A Boolean indicator of whether or not to automatic approval.
v1.0.timeoffrequestsubtype.apimodelproperty.automaticapprovalforcancellation.description=A Boolean indicator of whether or not to apply automatic approval for cancellation.
v1.0.timeoffrequestsubtype.apimodelproperty.allowrequesteditingformanager.description=A Boolean indicator of whether or not to allow request editing by manager.
v1.0.timeoffrequestsubtype.apimodelproperty.requestcancellation.description=The request cancellation of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.restorescheduleonlyifopenshiftavailable.description=A Boolean indicator of whether or not to restore schedule only if open shift is available.
v1.0.timeoffrequestsubtype.apimodelproperty.verifyatemployeesubmission.description=A Boolean indicator of whether or not to verify at employee submission.
v1.0.timeoffrequestsubtype.apimodelproperty.verifyatmanagersubmission.description=A Boolean indicator of whether or not to verify at manager submission.
v1.0.timeoffrequestsubtype.apimodelproperty.automaticallycreateopenshift.description=A Boolean indicator of whether or not to automatically create open shift.
v1.0.timeoffrequestsubtype.apimodelproperty.controlopenshiftcreationatapproval.description=A Boolean indicator of whether or not to control open shift creation at approval.
v1.0.timeoffrequestsubtype.apimodelproperty.overrideshiftsonalldaysexceptholidays.description=A Boolean indicator of whether or not to override shifts on all days except holidays.
v1.0.timeoffrequestsubtype.apimodelproperty.overrideshiftsonholidays.description=A Boolean indicator of whether or not to override shifts on holidays.
v1.0.timeoffrequestsubtype.apimodelproperty.paycodefilterset.description=The paycode filter set of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.approvalsettings.description=The approval settings of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.revieweroverride.description=The reviewer override of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.remindertemplate.description=The reminder template of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.automaticactiontemplate.description=The automatic action template of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.holidayandunscheduleddayrequestsetting.description=The holiday and unscheduled day request setting.
v1.0.timeoffrequestsubtype.apimodelproperty.unavailableamount.description=The unavailable amount (hours) of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.requestsubtypetransition.description=The request subtype transition of a Time Off request.
v1.0.timeoffrequestsubtype.apimodelproperty.automaticbreakplacement.description=A Boolean indicator of whether or not to automatically adjust break.
v1.0.timeOffRequestSubType.apimodelproperty.enableDurationSelectionInTile.description=A Boolean indicator of whether or not to enable duration selection in the Time Off tile.
v1.0.timeOffRequestSubType.apimodelproperty.guidedRecommendationsForManager.description=A Boolean indicator of whether or not to enable guided recommendations for managers.
v1.0.timeOffRequestSubType.apimodelproperty.entertimeoffdefaultstate.description=The enter time off default state of a Time Off request.
