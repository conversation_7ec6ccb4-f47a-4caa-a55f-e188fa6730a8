v1.0.scheduling-employee_visibility_periods.eligiblesubmissionperioddetail.apimodel.description=Model representing Eligible Submission Period Detail.
v1.0.scheduling-employee_visibility_periods.eligiblesubmissionperioddetail.apimodelproperty.submissionPeriod.description=The date time span representing Eligible Submission Period.
v1.0.scheduling-employee_visibility_periods.eligiblesubmissionperioddetail.apimodelproperty.requestPeriod.description=The date span representing Request Period.
v1.0.scheduling-employee_visibility_periods.eligiblesubmissionperioddetail.apimodelproperty.projectedSubmissionPeriodEvaluationDate.description=The projected date on which the exact time slot for a submission period will be assigned in ISO_LOCAL_DATE format (YYYY-MM-DD). This date is present when the Employee Visibility Period (EVP) has not yet been evaluated.