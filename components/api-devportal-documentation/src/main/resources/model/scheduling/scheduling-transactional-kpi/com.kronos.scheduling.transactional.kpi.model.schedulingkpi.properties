v1.0.schedulingkpi.apimodel.description=A model used to persist the data corresponding to an indicator and it's evaluations for a given employee (or org job) and date.
v1.0.schedulingkpi.apimodelproperty.id.description=The id of the scheduling KPI.
v1.0.schedulingkpi.apimodelproperty.indicator.description=The indicator used to evaluate.
v1.0.schedulingkpi.apimodelproperty.employee.description=The employee the scheduling KPI is for.
v1.0.schedulingkpi.apimodelproperty.orgjob.description=The org job the scheduling KPI is for.
v1.0.schedulingkpi.apimodelproperty.group.description=The schedule group the scheduling KPI is for.
v1.0.schedulingkpi.apimodelproperty.date.description=The date the scheduling KPI is far.
v1.0.schedulingkpi.apimodelproperty.stale.description=If the data is stale.
v1.0.schedulingkpi.apimodelproperty.version.description=The version of the data.
v1.0.schedulingkpi.apimodelproperty.projectedworkeditems.description=A collection of projected work items.
v1.0.schedulingkpi.apimodelproperty.indicatorevaluations.description=A collection of evaluations of the indicator, for each permutation of basic dimensions.