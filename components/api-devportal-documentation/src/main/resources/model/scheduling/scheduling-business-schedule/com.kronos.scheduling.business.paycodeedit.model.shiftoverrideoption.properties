#Comments for Properties
#Wed Mar 01 09:25:39 IST 2017
v1.0.shiftoverrideoption.paycodeedit.apimodel.description=Model containing the selected override options for the creation of a pay code edit.
v1.0.shiftoverrideoption.apimodelproperty.overridetype.description=The type of override to use during creation.
v1.0.shiftoverrideoption.apimodelproperty.shiftid.description=The id of the shift to override.
v1.0.shiftoverrideoption.apimodelproperty.createopenshift.description=A Boolean indicator of whether or not to create an open shift.
v1.0.shiftoverrideoption.apimodelproperty.createOpenShiftFrom.description=Optional date and time to further control when an open shift may be created.
v1.0.shiftoverrideoption.apimodelproperty.iswholeday.description=A Boolean indicator of whether or not a whole day is selected.