#Comments for Properties
#Wed Mar 01 09:25:39 IST 2017
v1.0.findschedulewherehyperfind.apimodel.description=Model containing information related to the searching Hyperfind.
v1.0.findschedulewherehyperfind.apimodelproperty.startdate.description=Deprecated; use <code>daterange</code> instead. The inclusive starting date for the required schedule data date range.
v1.0.findschedulewherehyperfind.apimodelproperty.enddate.description=Deprecated; use <code>daterange</code> instead. The exclusive end date for the required schedule data date range.
v1.0.findschedulewherehyperfind.apimodelproperty.symbolicperiodref.description=A reference to the symbolic period for the schedule data date range, exclusive with startDate and endDate.
v1.0.findschedulewherehyperfind.apimodelproperty.hyperfindref.description=Deprecated; use <code>hyperfind</code> instead. A reference to the Hyperfind request to use.
v1.0.findschedulewherehyperfind.apimodelproperty.includeemployeetransfer.description=A Boolean indicator of whether or not retrieved employees have the location in their transfer set, but did not work at the location during the schedule period.
v1.0.findschedulewherehyperfind.apimodelproperty.hyperfind.description=A reference to the Hyperfind request to use.
v1.0.findschedulewherehyperfind.apimodelproperty.daterange.description=The schedule data date range, consisting of a startDate and endDate in ISO_LOCAL_DATE format (YYYY-MM-DD).
