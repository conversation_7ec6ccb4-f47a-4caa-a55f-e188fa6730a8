#Comments for Properties
#Wed Mar 01 09:25:39 IST 2017
v1.0.schedulecriteriaforemployee.apimodel.description=Model containing conditions pertaining to find employee schedule request.
v1.0.schedulecriteriaforemployee.apimodelproperty.includeshifts.description=Include the shifts in the schedule.
v1.0.schedulecriteriaforemployee.apimodelproperty.includepaycodeedits.description=Include the pay code edits in the schedule.
v1.0.schedulecriteriaforemployee.apimodelproperty.includeleaveedits.description=Include the leave edits in the schedule.
v1.0.schedulecriteriaforemployee.apimodelproperty.includeholidays.description=Include the holidays in the schedule.
v1.0.schedulecriteriaforemployee.apimodelproperty.includescheduledays.description=Include the schedule days summary.
v1.0.schedulecriteriaforemployee.apimodelproperty.includetimeoffrequests.description=Include the time off requests in the schedule.
v1.0.schedulecriteriaforemployee.apimodelproperty.includeopenshiftsummary.description=Include a summary of the open shifts in the schedule.
v1.0.schedulecriteriaforemployee.apimodelproperty.includeopenshiftgroupedlist.description=Include the open shifts in the schedule.
v1.0.schedulecriteriaforemployee.apimodelproperty.includebreaks.description=Include the breaks in the schedule.
v1.0.schedulecriteriaforemployee.apimodelproperty.includemodifiedsince.description=Only include entities with modifications starting on a certain date in the schedule.
v1.0.schedulecriteriaforemployee.apimodelproperty.includeavailabilities.description=Include the availability in the schedule.