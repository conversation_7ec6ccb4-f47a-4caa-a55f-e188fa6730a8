#Comments for Properties
#Wed Mar 01 09:25:39 IST 2017
v1.0.applycreatepreviewdo.apimodel.description=Model describing the structure to create a Preview element of a Schedule PayCodeEdit.
v1.0.applycreatepreviewdo.apimodelproperty.paycodeedit.description=The name of the pay code edit.
v1.0.applycreatepreviewdo.apimodelproperty.shiftoverridetype.description=The type of override.
v1.0.applycreatepreviewdo.apimodelproperty.shiftid.description=The ID for the shift to override.
v1.0.applycreatepreviewdo.apimodelproperty.createopenshift.description=Whether or not to create an open shift.
v1.0.applycreatepreviewdo.apimodelproperty.bypassaccrualwarning.description=Whether or not to bypass the accruals balance warning.
v1.0.applycreatepreviewdo.apimodelproperty.previewyokedpaycode.description=Preview the creation of yoked pay code edit.
v1.0.applycreatepreviewdo.apimodelproperty.previewtime.description=Whether or not to preview the final time and duration of the pay code edit.
v1.0.applycreatepreviewdo.apimodelproperty.overrideavailability.description=Whether to override the availability when creating a pay code edit.
v1.0.applycreatepreviewdo.apimodelproperty.availabilitystarttime.description=The start time of the availability override.
v1.0.applycreatepreviewdo.apimodelproperty.availabilityduration.description=The duration in seconds of the availability override.