v1.0.processingstatus.apimodel.description=A model containing the processing status of an entity.
v1.0.processingstatus.apimodelproperty.startdate.description=The start date for which processing statuses are retrieved in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.processingstatus.apimodelproperty.enddate.description=The end date for which processing statuses are retrieved in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.processingstatus.apimodelproperty.processingtype.description=The type of processing status.
v1.0.processingstatus.apimodelproperty.blocking.description=A Boolean indicator of whether or not the processing status is blocking any further actions on the entity.
