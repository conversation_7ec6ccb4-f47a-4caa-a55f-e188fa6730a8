#Comments for Properties
#Wed Mar 01 09:25:39 IST 2017
v1.0.updatescheduleresponseformanager.apimodel.description=Model for a Business Schedule multi update response.
v1.0.updatescheduleresponseformanager.apimodelproperty.employeeref.description=A reference to the employee whose schedule was updated.
v1.0.updatescheduleresponseformanager.apimodelproperty.messages.description=The text of the messages returned to the manager after schedule update.
v1.0.updatescheduleresponseformanager.apimodelproperty.shifts.description=The set of changed shifts.
v1.0.updatescheduleresponseformanager.apimodelproperty.paycodeedits.description=The set of changed pay code edits.
v1.0.updatescheduleresponseformanager.apimodelproperty.availabilities.description=The set of changed availabilities.
v1.0.updatescheduleresponseformanager.apimodelproperty.daylocks.description=The set of changed daylocks.
v1.0.updatescheduleresponseformanager.apimodelproperty.holidays.description=The set of changed holidays.
v1.0.updatescheduleresponseformanager.apimodelproperty.scheduletags.description=The set of changed schedule tags.
v1.0.updatescheduleresponseformanager.apimodelproperty.errordetails.description=The error details associated with a Business Schedule multi_update transaction.