#Comments for Properties
#Wed Mar 01 09:25:39 IST 2017
v1.0.findschedulewherelocation.apimodel.description=Model containing information related to the searching location.
v1.0.findschedulewherelocation.apimodelproperty.daterange.description=The start and end dates for the required schedule data date range.
v1.0.findschedulewherelocation.apimodelproperty.symbolicperiodref.description=A reference to the symbolic period for the schedule data date range, exclusive with startdate and enddate.
v1.0.findschedulewherelocation.apimodelproperty.locations.description=The locations included in the schedule.
v1.0.findschedulewherelocation.apimodelproperty.includeemployeetransfer.description=A Boolean indicator of whether or not to include employees who have the location in their transfer set, but did not work at a location during the schedule period.
v1.0.findschedulewherelocation.apimodelproperty.workloadspans.description=The workload spans associated with a schedule.
v1.0.findschedulewherelocation.apimodelproperty.includecoveragespans.description=A Boolean indicator of whether or not to include coverage spans in the schedule data.