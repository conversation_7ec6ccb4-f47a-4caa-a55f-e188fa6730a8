#Comments for Properties
#Wed Mar 01 16:46:17 IST 2017
v1.0.employeegroupmembership.apimodel.description=Represents the relation between one employee and one group.
v1.0.employeegroupmembership.apimodelproperty.id.description=The group membership ID.
v1.0.employeegroupmembership.apimodelproperty.startdatetime.description=The start date and time of the employee's membership in the schedule group; in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.employeegroupmembership.apimodelproperty.enddatetime.description=The end date and time of the employee's membership in the schedule group; in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.employeegroupmembership.apimodelproperty.group.description=The group name or ID.
v1.0.employeegroupmembership.apimodelproperty.employee.description=The employee's name or ID.
v1.0.employeegroupmembership.apimodelproperty.position.description=The position ID.