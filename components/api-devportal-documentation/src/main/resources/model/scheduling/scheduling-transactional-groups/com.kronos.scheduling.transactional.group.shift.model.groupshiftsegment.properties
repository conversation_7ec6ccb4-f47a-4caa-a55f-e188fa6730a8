#Comments for Properties
#Wed Mar 01 16:46:17 IST 2017
v1.0.groupshiftsegment.apimodel.description=Model containing the informations of a group shift segment.
v1.0.groupshiftsegment.apimodelproperty.id.description=The ID of this group shift segment.
v1.0.groupshiftsegment.apimodelproperty.segmenttyperef.description=A reference to the type of this group shift segment.
v1.0.groupshiftsegment.apimodelproperty.startdatetime.description=The inclusive start date and time of the shift segment in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.groupshiftsegment.apimodelproperty.enddatetime.description=The inclusive end date time of the group shift segment.
v1.0.groupshiftsegment.apimodelproperty.userenteredlaborcategories.description=The selected labor categories and entries for the group shift segment.
v1.0.groupshiftsegment.apimodelproperty.userenteredcostcenter.description=The selected cost center for the group shift segment.
v1.0.groupshiftsegment.apimodelproperty.userenteredworkruleref.description=A reference to the work rule of this group shift segment.
v1.0.groupshiftsegment.apimodelproperty.skillcertprofilerefs.description=References to the skill certifications required by this group shift segment.
v1.0.groupshiftsegment.apimodelproperty.shiftsegmentdetailtypeid.description=A reference to this group shift segment details.
v1.0.groupshiftsegment.apimodelproperty.transferstring.description=An ordered, semi-colon separated list of Labor Category Entries and Cost Center.
