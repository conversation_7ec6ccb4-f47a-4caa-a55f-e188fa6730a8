#Comments for Properties
v1.0.scheduling.setup.metrics.formula.apimodel.description=Model containing information about a Formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.id.description=The id of the formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.name.description=The name of the formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.localizedname.description=The localized name of the formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.description.description=The description of the formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.valuetype.description=The value type of the formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.valueclassname.description=The value class name of the formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.expression.description=The expression of the formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.golddata.description=Is the formula gold data.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.active.description=Is the formula active.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.version.description=The version of the formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.expressionusageclassname.description=The expression usage class name of the formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.visible.description=Is the formula visible.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.productname.description=The product name of the formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.operationitems.description=The collection of opertation items used by the formula.
v1.0.scheduling.setup.metrics.formula.apimodelproperty.parameters.description=The collection of formula parameters used by the formula.