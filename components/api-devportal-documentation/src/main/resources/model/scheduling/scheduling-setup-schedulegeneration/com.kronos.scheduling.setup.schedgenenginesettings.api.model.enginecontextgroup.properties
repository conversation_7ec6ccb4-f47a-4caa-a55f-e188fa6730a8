v1.0.schedule-generation-setup.enginecontextgroup.apimodel.description=Model describing Engine Context Group.
v1.0.schedule-generation-setup.enginecontextgroup.apimodelproperty.generateOpenShifts.description=The generateOpenShifts of the Engine context group.
v1.0.schedule-generation-setup.enginecontextgroup.apimodelproperty.generateEmployeeShifts.description=The generateEmployeeShifts of the Engine context group.
v1.0.schedule-generation-setup.enginecontextgroup.apimodelproperty.assignInitialOpenShifts.description=The assignInitialOpenShifts of the Engine context group.
v1.0.schedule-generation-setup.enginecontextgroup.apimodelproperty.optimizeShiftContents.description=The optimizeShiftContents of the Engine context group.
v1.0.schedule-generation-setup.enginecontextgroup.apimodelproperty.deleteEmployeeShifts.description=The deleteEmployeeShifts of the Engine context group.
v1.0.schedule-generation-setup.enginecontextgroup.apimodelproperty.deleteOpenShifts.description=The deleteOpenShifts of the Engine context group.
v1.0.schedule-generation-setup.enginecontextgroup.apimodelproperty.overwriteExistingOpenShifts.description=The overwriteExistingOpenShifts of the Engine context group.
v1.0.schedule-generation-setup.enginecontextgroup.apimodelproperty.overwriteExistingEmployeeShifts.description=The overwriteExistingEmployeeShifts of the Engine context group.
v1.0.schedule-generation-setup.enginecontextgroup.apimodelproperty.treatManuallyCreatedShiftsAsLock.description=The treatManuallyCreatedShiftsAsLock of the Engine context group.
v1.0.schedule-generation-setup.enginecontextgroup.apimodelproperty.overwriteOpenShiftBreaks.description=The overwriteOpenShiftBreaks of the Engine context group.