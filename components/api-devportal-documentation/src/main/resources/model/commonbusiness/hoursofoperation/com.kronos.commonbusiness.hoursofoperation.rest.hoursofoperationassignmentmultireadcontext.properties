v1.0.hoursofoperationassignmentmultireadcontext.apimodel.description=The Retrieve Hours of Operation Assignments request model.
v1.0.hoursofoperationassignmentmultireadcontext.apimodelproperty.locations.description=A list of references to location objects.
v1.0.hoursofoperationassignmentmultireadcontext.apimodelproperty.effectivedate.description=The date associated with locations in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.hoursofoperationassignmentmultireadcontext.apimodelproperty.adjustboundaryassignmentdates.description=A Boolean indicator of whether or not boundary assignment dates should be adjusted to match the effective date span of the location when the difference between the assignment start or end dates and the effective start or end dates of the location are a week or less apart, respectively.
