v1.0.hoursofoperationoverrideitem.apimodel.description=Model context for an hours of operation override item.
v1.0.hoursofoperationoverrideitem.apimodelproperty.id.description=The ID of an hours of operation override item.
v1.0.hoursofoperationoverrideitem.apimodelproperty.startdate.description=The start date associated with an hours of operation override item in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.hoursofoperationoverrideitem.apimodelproperty.enddate.description=The end date associated with an hours of operation override item in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.hoursofoperationoverrideitem.apimodelproperty.opentime.description=The opening time associated with an hours of operation override item.
v1.0.hoursofoperationoverrideitem.apimodelproperty.closetime.description=The closing time associated with an hours of operation override item.
v1.0.hoursofoperationoverrideitem.apimodelproperty.isclosed.description=A Boolean indicator of whether or not an hours of operation override item is closed.
v1.0.hoursofoperationoverrideitem.apimodelproperty.reason.description=The reason for an hours of operation override item.
