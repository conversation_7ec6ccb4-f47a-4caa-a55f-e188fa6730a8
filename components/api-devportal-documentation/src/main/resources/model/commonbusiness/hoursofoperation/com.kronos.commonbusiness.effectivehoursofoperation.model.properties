v2.0.effectivehoursofoperation.apimodel.description=Model context for effective version of Hours of Operation.
v2.0.effectivehoursofoperation.apimodelproperty.id.description=The ID of effective version of Hours of Operation object.
v2.0.effectivehoursofoperation.apimodelproperty.effectivespan.description=The date span, provided as startDate and endDate in ISO_LOCAL_DATE format (YYYY-MM-DD), endDate is not used.
v2.0.effectivehoursofoperation.apimodelproperty.unrestrictededit.description=A Boolean indicator of whether or not an unrestricted edit is associated with Hours of Operation. Defaults to false.
v2.0.effectivehoursofoperation.apimodelproperty.items.description=A list of Hours of Operation items.
