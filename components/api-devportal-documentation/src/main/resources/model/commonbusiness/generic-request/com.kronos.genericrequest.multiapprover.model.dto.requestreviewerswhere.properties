#Comments for Properties
v1.0.commons-reviewer_lists-reviewers.where.apimodel.description=Model containing properties to retrieve reviewers and listeners request.
v1.0.commons-reviewer_lists-reviewers.where.apimodelproperty.reviewerList.description=The ObjectRef model describing reviewer list.
v1.0.commons-reviewer_lists-reviewers.where.apimodelproperty.employee.description=The model describing employee and his position.
v1.0.commons-reviewer_lists-reviewers.where.apimodelproperty.symbolicPurpose.description=The ObjectRef model describing symbolic purpose.
v1.0.commons-reviewer_lists-reviewers.where.apimodelproperty.nextStep.description=The model describing next step.
v1.0.commons-reviewer_lists-reviewers.where.apimodelproperty.requestItemInfo.description=The model describing request item info.