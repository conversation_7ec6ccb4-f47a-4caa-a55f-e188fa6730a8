#Comments for Properties
v1.0.approvalinfoaudit.apimodel.description=Model containing the approval information of a request.
v1.0.approvalinfoaudit.apimodel.audittimestamp.description=The date and time that this revision was made.
v1.0.approvalinfoaudit.apimodel.audittype.description=The type of this revision.
v1.0.approvalinfoaudit.apimodel.audituser.description=The full name of the user who made the revision.
v1.0.approvalinfoaudit.apimodel.revisionid.description=The Id of the this revision or audit record.
v1.0.approvalinfoaudit.apimodel.approvalinfo.description=The approval information as it existed at the time of the given revision.
v1.0.approvalinfoaudit.apimodel.approvalinfoid.description=The ID of the approval information.