#Comments for Properties
v1.0.approvalstepsetting.apimodel.description=Model describing the details of the approval setting definition.
v1.0.approvalstepsetting.apimodelproperty.id.description=The ID of an approval step setting.
v1.0.approvalstepsetting.apimodelproperty.name.description=The name of an approval step setting.
v1.0.approvalstepsetting.apimodelproperty.description.description=The description of an approval step setting.
v1.0.approvalstepsetting.apimodelproperty.active.description=A Boolean indicator of whether or not the step setting is active.
v1.0.approvalstepsetting.apimodelproperty.version.description=The version of the step setting.
v1.0.approvalstepsetting.apimodelproperty.approverNotificationRequestReceptionId.description=The ID of the approvers notification upon request reception.
v1.0.approvalstepsetting.apimodelproperty.reminderId.description=The ID of a reminder notification.
v1.0.approvalstepsetting.apimodelproperty.reminderDelay.description=The reminder delay in days.
v1.0.approvalstepsetting.apimodelproperty.autoActionName.description=The automatic action name.
v1.0.approvalstepsetting.apimodelproperty.autoActionDelay.description=The automatic action delay in days.
v1.0.approvalstepsetting.apimodelproperty.inactiveApproverNotificationId.description=The ID of an inactive approver notification.
v1.0.approvalstepsetting.apimodelproperty.inactiveApproverManagerNotificationId.description=The ID of an inactive approver's manager notification.