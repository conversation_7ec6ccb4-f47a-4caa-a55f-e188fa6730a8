#Comments for Properties
v1.0.reviewerlistinfo.apimodel.description=Model containing information for reviewer list information.
v1.0.reviewerlistinfo.apimodelproperty.employeeId.description=The employee ID of the reviewer, which is an ID that uniquely identifies an employee. This is not a person number. 
v1.0.reviewerlistinfo.apimodelproperty.purposeId.description=The purpose ID of the reviewer.
v1.0.reviewerlistinfo.apimodelproperty.defaultReviewerListId.description=The default review list of the reviewer.
v1.0.reviewerlistinfo.apimodelproperty.previousReviewerId.description=The previous reviewer ID of the reviewer.
v1.0.reviewerlistinfo.apimodelproperty.stepIndex.description=The step index of the reviewer.
