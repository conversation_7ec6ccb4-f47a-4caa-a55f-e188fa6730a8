#Comments for Properties
v1.0.reviewer.apimodel.description=Model containing information for a reviewer.
v1.0.reviewer.apimodelproperty.employeeId.description=The employee ID of a reviewer, which is an ID that uniquely identifies an employee. This is not a person number. 
v1.0.reviewer.apimodelproperty.personNumber.description=The person number of a reviewer, which is A number that uniquely identifies a person. This is not an employee ID.
v1.0.reviewer.apimodelproperty.fullName.description=The full name of the reviewer.
v1.0.reviewer.apimodelproperty.approver.description=A Boolean indicator of whether or not the reviewer approved the approval step.