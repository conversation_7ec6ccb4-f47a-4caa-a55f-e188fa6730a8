#Comments for Properties
v1.0.approvalsetting.apimodel.description=Model describing the details of the approval setting definition.
v1.0.approvalsetting.apimodelproperty.id.description=The ID of an approval setting.
v1.0.approvalsetting.apimodelproperty.name.description=The name of an approval setting.
v1.0.approvalsetting.apimodelproperty.description.description=The description of an approval setting.
v1.0.approvalsetting.apimodelproperty.active.description=A Boolean indicator of whether or not the approval setting is active.
v1.0.approvalsetting.apimodelproperty.version.description=The version of an approval setting.
v1.0.approvalsetting.apimodelproperty.approvalSequenceId.description=The sequence ID of an approval setting.
v1.0.approvalsetting.apimodelproperty.useAllSteps.description=A Boolean indicator of whether or not all steps should be used. 
v1.0.approvalsetting.apimodelproperty.defaultApprovalStep.description=The default name of approval steps.
v1.0.approvalsetting.apimodelproperty.approvalStepSettings.description=The settings of approval steps.
v1.0.approvalsetting.apimodelproperty.approvalStepLengthControls.description=The request length of approval steps.
v1.0.approvalsetting.apimodelproperty.automaticActionName.description=The name of an automatic action.
v1.0.approvalsetting.apimodelproperty.automaticActionDays.description=The number of days before the automatic action.
v1.0.approvalsetting.apimodelproperty.employeeNotificationSubmitId.description=The ID of an employee notification upon submission.
v1.0.approvalsetting.apimodelproperty.employeeNotificationPendingId.description=The ID of an employee notification upon pending.
v1.0.approvalsetting.apimodelproperty.employeeNotificationFinalApprovalId.description=The ID of an employee notification upon final approval.
v1.0.approvalsetting.apimodelproperty.employeeNotificationRefusalId.description=The ID of an employee notification upon refusal.
v1.0.approvalsetting.apimodelproperty.employeeNotificationCancellationId.description=The ID of an employee notification upon cancellation.
v1.0.approvalsetting.apimodelproperty.listenerNotificationInitialRequestId.description=The ID of a listener notification upon initial request.
v1.0.approvalsetting.apimodelproperty.listenerNotificationFinalApprovalId.description=The ID of a listener notification upon final approval.
v1.0.approvalsetting.apimodelproperty.listenerNotificationRefusalId.description=The ID of a listener notification upon refusal.
v1.0.approvalsetting.apimodelproperty.listenerNotificationCancelSubmitId.description=The ID of a listener notification upon cancel submission.
v1.0.approvalsetting.apimodelproperty.listenerNotificationCancellationId.description=The ID of a listener notification upon cancellation.
v1.0.approvalsetting.apimodelproperty.listenerNotificationDeletionId.description=The ID of a listener notification upon deletion.
v1.0.approvalsetting.apimodelproperty.approversNotificationAutoApproveId.description=The ID of an approver's notification upon automatic approval.
v1.0.approvalsetting.apimodelproperty.approversNotificationRefusalId.description=The ID of an approver's notification upon refusal.
v1.0.approvalsetting.apimodelproperty.approversNotificationCancelSubmitId.description=The ID of an approver's notification upon cancel submission.
v1.0.approvalsetting.apimodelproperty.approversNotificationCancellationId.description=The ID of an approver's notification upon cancellation.
v1.0.approvalsetting.apimodelproperty.approversNotificationDeletionId.description=The ID of an approver's notification upon deletion.