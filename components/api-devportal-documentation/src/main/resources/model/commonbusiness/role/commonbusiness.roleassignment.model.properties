# RoleAssignmentDto
v1.0.commonbusiness-role_assignment.apimodel.ra.description=The Role Assignment data.
v1.0.commonbusiness-role_assignment.apimodelproperty.ra.id=The ID of a Role Assignment.
v1.0.commonbusiness-role_assignment.apimodelproperty.ra.name=The name of a Role Assignment.
v1.0.commonbusiness-role_assignment.apimodelproperty.ra.description=The description of a Role Assignment.
v1.0.commonbusiness-role_assignment.apimodelproperty.ra.isDefault=A Boolean indicator of whether or not a role assignment is the default role assignment.
v1.0.commonbusiness-role_assignment.apimodelproperty.ra.attributes=The attributes of a Role Assignment.

# RoleAssignmentAttributeDto
v1.0.commonbusiness-role_assignment.apimodel.raa.description=The Role Assignment Attribute data.
v1.0.commonbusiness-role_assignment.apimodelproperty.raa.id=The ID of a Role Assignment Attribute.
v1.0.commonbusiness-role_assignment.apimodelproperty.raa.type=The type of a Role Assignment Attribute. Valid qualifiers include: FUNCTION_ACCESS_PROFILE, DISPLAY_PROFILE, PAY_CODE_EDIT_PROFILE, WORK_RULE_PROFILE, REPORT_PROFILE, LABOR_CATEGORY_PROFILE, EMPLOYEE_GROUP, and MANAGER_JOB_TRANSFER_SET.
v1.0.commonbusiness-role_assignment.apimodelproperty.raa.value=The value of a Role Assignment Attribute.
v1.0.commonbusiness-role_assignment.apimodelproperty.raa.homeHyperfindQuery=The home Hyperfind query of a Role Assignment Attribute.
v1.0.commonbusiness-role_assignment.apimodelproperty.raa.effectiveDate=The effective date of a Role Assignment Attribute.
v1.0.commonbusiness-role_assignment.apimodelproperty.raa.expirationDate=The expiration date of a Role Assignment Attribute.

# PersonIdentity
v1.0.commonbusiness-role_assignment.apimodel.pi.description=The Person Identity.
v1.0.commonbusiness-role_assignment.apimodelproperty.pi.id=The ID of a Person Identity.
v1.0.commonbusiness-role_assignment.apimodelproperty.pi.number=The number of Person Identity.

# PersonRoleAssignmentDto
v1.0.commonbusiness-role_assignment.apimodel.pra.description=The Person Role Assignment data.
v1.0.commonbusiness-role_assignment.apimodelproperty.pra.personIdentity=The Person Identity.
v1.0.commonbusiness-role_assignment.apimodelproperty.pra.roleAssignments=The Role Assignments.

# RoleAssignmentChanges
v1.0.commonbusiness-role_assignment.apimodel.rac.description=The Role Assignment Changes data.
v1.0.commonbusiness-role_assignment.apimodelproperty.rac.add=The Role Assignment Add data.
v1.0.commonbusiness-role_assignment.apimodelproperty.rac.update=The Role Assignment Update data.
v1.0.commonbusiness-role_assignment.apimodelproperty.rac.delete=The Role Assignment Delete data.

# PersonRoleAssignmentChangeSet
v1.0.commonbusiness-role_assignment.apimodel.pcs.description=The Person Role Assignment change set data.
v1.0.commonbusiness-role_assignment.apimodelproperty.pcs.personIdentity=The Person Identity.
v1.0.commonbusiness-role_assignment.apimodelproperty.pcs.changeSet=The Role Assignment Changes data.

# BulkRoleAssignmentResult
v1.0.commonbusiness-role_assignment.apimodel.br.description=The Role Assignment Bulk Results.
v1.0.commonbusiness-role_assignment.apimodelproperty.br.successResult=The Success Results.
v1.0.commonbusiness-role_assignment.apimodelproperty.br.failResult=The Fail Results.

# RoleAssignmentWhere
v1.0.commonbusiness-role_assignment.apimodel.raw.description=The Role Assignment Search Where Clause.

# WhereEmployees
v1.0.commonbusiness-role_assignment.apimodel.we.description=The Role Assignment Employee Search Key And Values.

# MultiReadEmployees
v1.0.commonbusiness-role_assignment.apimodel.mre.description=The Role Assignment List Of Employee Search Key And Values.
v1.0.commonbusiness-role_assignment.apimodelproperty.mre.employees=The Role Assignment Employee Search Key And Values.
