v1.0.commonbusiness.customtile.apimodel.description=Custom tile contains the set of properties which are required for creating a custom tile.
v1.0.commonbusiness.customtile.apimodelproperty.id.description=The ID of the custom tile.
v1.0.commonbusiness.customtile.apimodelproperty.name.description=The name of the custom tile.
v1.0.commonbusiness.customtile.apimodelproperty.labels.description=The list of labels in differnt locales used as tile headers.
v1.0.commonbusiness.customtile.apimodelproperty.height.description=The height of the custom tile. Valid values include: SHORT, MEDIUM, and TALL.
v1.0.commonbusiness.customtile.apimodelproperty.configuration.description=The configuration data required for creating a custom tile.
v1.0.commonbusiness.customtile.apimodelproperty.licenses.description=A list of the license building blocks required to control tile access.
v1.0.commonbusiness.customtile.apimodelproperty.permissions.description=A list of the access control points required to control tile access.