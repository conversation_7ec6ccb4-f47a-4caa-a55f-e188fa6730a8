v1.0.wifi_accesspoints.apimodel.description=WiFi Access Point model.
v1.0.wifi_accesspoints.apimodelproperty.id.description=The ID of a WiFi Access Point.
v1.0.wifi_accesspoints.apimodelproperty.name.description=The name of a WiFi Access Point.
v1.0.wifi_accesspoints.apimodelproperty.description.description=The description of a WiFi Access Point.
v1.0.wifi_accesspoints.apimodelproperty.bssid.description=The BSSID of a WiFi Access Point.
v1.0.wifi_accesspoints.apimodelproperty.qualifier.description=The qualifier (optional) of a WiFi Access Point.
v1.0.wifi_accesspoints.apimodelproperty.version.description=The version of a WiFi Access Point (internal).
v1.0.wifi_accesspoints.apimodelproperty.sdmKey.description=The SDM key of a WiFi Access Point (internal).
v1.0.wifi_accesspoints.apimodelproperty.display_name.description=The display name of a WiFi Access Point.