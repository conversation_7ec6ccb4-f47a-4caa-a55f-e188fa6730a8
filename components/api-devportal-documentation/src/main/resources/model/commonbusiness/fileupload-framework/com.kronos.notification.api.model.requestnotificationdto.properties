# #Comments for Properties
# #Wed Feb 22 15:42:56 IST 2017
# v1.0.requestnotificationdto.apimodel.description=Request notification context.
# v1.0.requestnotificationdto.apimodelproperty.approvalstatus.description=The approval status of a request notification.
# v1.0.requestnotificationdto.apimodelproperty.approvername.description=The approver name of a request notification.
# v1.0.requestnotificationdto.apimodelproperty.durationdays.description=The duration days of a request notification.
# v1.0.requestnotificationdto.apimodelproperty.durationhours.description=The duration hours of a request notification.
# v1.0.requestnotificationdto.apimodelproperty.enddate.description=The end date of a request notification.
# v1.0.requestnotificationdto.apimodelproperty.endtime.description=The end time of a request notification.
# v1.0.requestnotificationdto.apimodelproperty.requestid.description=The request ID of a request notification.
# v1.0.requestnotificationdto.apimodelproperty.changedatapproval.description=A Boolean indicator of whether or not a request notification was changed at approval.
# v1.0.requestnotificationdto.apimodelproperty.requestedperiods.description=rThe equested periods of a request notification.
# v1.0.requestnotificationdto.apimodelproperty.startdate.description=The start date of a request notification.
# v1.0.requestnotificationdto.apimodelproperty.starttime.description=The start time of a request notification.
# v1.0.requestnotificationdto.apimodelproperty.submissiondate.description=The submission date of a request notification in ISO_LOCAL_DATE format (YYYY-MM-DD).
# v1.0.requestnotificationdto.apimodelproperty.subtype.description=The request subtype of a request notification.
# v1.0.requestnotificationdto.apimodelproperty.requestername.description=The name of the requester of a request notification.
