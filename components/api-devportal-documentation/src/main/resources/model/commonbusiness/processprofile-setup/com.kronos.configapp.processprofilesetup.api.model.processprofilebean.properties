v1.0.processprofilegetallresponse.apimodel.description=The All Process Profile response model.
v1.0.processprofilegetallresponse.apimodelproperty.totalelements.description=The total number of elements.
v1.0.processprofilegetallresponse.apimodelproperty.records.description=The list of Process Profiles.

v1.0.processprofilebase.apimodel.description=The Process Profile base model.
v1.0.processprofilebase.apimodelproperty.id.description=The ID of a Process Profile.
v1.0.processprofilebase.apimodelproperty.name.description=The name of a Process Profile.
v1.0.processprofilebase.apimodelproperty.description.description=The description of a Process Profile.

v1.0.processprofileresponse.apimodel.description=The selected Processes response model.
v1.0.processprofileresponse.apimodelproperty.selectedprocesses.description=The list of references to processes.

v1.0.processprofileavailableresponse.apimodel.description=The available Processes response model.
v1.0.processprofileavailableresponse.apimodelproperty.availableprocess.description=The list of references to available processes.

v1.0.processprofilemultireadresponse.apimodel.description=Represents the response structure for Process Profiles, including a list of selected processes.
v1.0.processprofilemultireadresponse.apimodelproperty.selectedprocess.description=A list of processes associated with the profile, each containing an ID and a name.

v1.0.processprofilerequest.apimodel.description=Defines the request structure for searching Process Profiles, allowing filtering based on ObjectRefList.
v1.0.processprofilerequest.apimodelproperty.where.description=Contains filtering criteria for retrieving Process Profiles, including an option to request all details.

v1.0.processprofilewhere.apimodel.description=Defines the filtering criteria for searching Process Profiles, including profile references and a flag to retrieve complete details.
v1.0.processprofilewhere.apimodelproperty.processprofiles.description=A list of process profile references used for filtering the search results.
v1.0.processprofilewhere.apimodelproperty.allDetails.description=A boolean indicator of whether to fetch complete details (true) or only essential information (false).  When false, only `id` and `name` are returned.