v1.0.roleprofiledto.apimodel.description=Represents a Role Profile Data Transfer Object (DTO)
v1.0.roleprofiledto.apimodelproperty.id.description=Unique identifier for the Role Profile
v1.0.roleprofiledto.apimodelproperty.name.description=Name of the Role Profile
v1.0.roleprofiledto.apimodelproperty.description.description=Detailed description of the Role Profile
v1.0.roleprofiledto.apimodelproperty.functionAccessProfile.description=Profile defining function access permissions
v1.0.roleprofiledto.apimodelproperty.displayProfile.description=Profile defining display settings
v1.0.roleprofiledto.apimodelproperty.delegateAlerts.description=Indicates if alerts can be delegated
v1.0.roleprofiledto.apimodelproperty.showInDelegation.description=Indicates if the Role Profile is visible in delegation