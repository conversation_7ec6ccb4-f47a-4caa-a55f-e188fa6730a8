# Access Method Profile Model DTO

v1.0.access_method_profile.apimodel.description=The Access Method Profile object model.
v1.0.access_method_profile.apimodelproperty.displaydescription.description=The displayed description of an access method profile.
v1.0.access_method_profile.apimodelproperty.displayname.description= The displayed name of an access method profile.
v1.0.access_method_profile.apimodelproperty.accessmethodconditions.description=An array of access method conditions.
v1.0.access_method_profile.apimodelproperty.name.description=The name of an access method profile.
v1.0.access_method_profile.apimodelproperty.description.description=The description of an access method profile.
v1.0.access_method_profile.apimodelproperty.id.description=The ID of an access method profile.
v1.0.displayprofile.apimodelproperty.exceptiontile.description=A reference to the exception tile associated with an access method profile.

v1.0.access_method_condition.apimodel.description=The Access Method Condition object model.
v1.0.access_method_condition.apimodelproperty.id.description=The ID of an access method condition.
v1.0.access_method_condition.apimodelproperty.priority.description=The priority of an access method condition.
v1.0.access_method_condition.apimodelproperty.access_method_details.description=The access method details.
v1.0.access_method_condition.apimodelproperty.role_profile.description=A reference to the role profile object.

v1.0.access_method_detail.apimodel.description=The Access Method Detail object model.
v1.0.access_method_detail.apimodelproperty.accessmethodtype.description=The access method type.
v1.0.access_method_detail.apimodelproperty.accessmethodvalue.description=A reference to the access method value object.