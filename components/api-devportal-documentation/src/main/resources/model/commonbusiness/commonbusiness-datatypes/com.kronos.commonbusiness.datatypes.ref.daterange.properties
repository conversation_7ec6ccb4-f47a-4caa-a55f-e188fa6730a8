# v1.0.daterange.apimodel.description=A date range object used to select either a start and end date or a symbolic period.
v1.0.daterange.apimodelproperty.startdate.description=The start date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.daterange.apimodelproperty.enddate.description=The end date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.daterange.apimodelproperty.symbolicperiod.description=A symbolic identifier that represents a timeframe or a span of time.