v1.0.commons-transaction_assistant.apimodel.request.where.description=Request to fetch details for transaction assistant data.
v1.0.commons-transaction_assistant.apimodelproperty.index.description=Representing page number from a paginated result with 0 denoting as page 1.
v1.0.commons-transaction_assistant.apimodelproperty.count.description=Number of records returned per page.
v1.0.commons-transaction_assistant.apimodelproperty.requestdaterange.description=Request object containing start date time and end date time.
v1.0.commons-transaction_assistant.apimodelproperty.sourcetype.description=Source of execution like SDM, UDM or Ihub.
v1.0.commons-transaction_assistant.apimodelproperty.transactiontype.description=The type of transactions run.
v1.0.commons-transaction_assistant.apimodelproperty.datasourceid.description=ID belongs to the failed transaction error details.
v1.0.commons-transaction_assistant.apimodelproperty.integrationname.description=Name of the installed integration that is the basis of this integration run.
v1.0.commons-transaction_assistant.apimodelproperty.integrationrunname.description=A unique integration run name to make it easier to identify the run of that integration.
v1.0.commons-transaction_assistant.apimodelproperty.integrationexecutionid.description=Unique ID for the integration run.
v1.0.commons-transaction_assistant.apimodelproperty.errortransactionid.description=Unique transaction for failed records.
v1.0.commons-transaction_assistant.apimodelproperty.errorcodes.description=Error codes corresponding to failed transactions.
v1.0.commons-transaction_assistant.apimodelproperty.errordescription.description=Description belongs to failed transactions.
v1.0.commons-transaction_assistant.apimodelproperty.propertyvalues.description=Key identifier from the source causing the error.
v1.0.commons-transaction_assistant.apimodelproperty.apiendpoint.description=The API end point for the transaction.
v1.0.commons-transaction_assistant.apimodelproperty.requestdaterange.startdatetime.description=The starting date time of a date range. 
v1.0.commons-transaction_assistant.apimodelproperty.requestdaterange.enddatetime.description=The ending date time of a date range.