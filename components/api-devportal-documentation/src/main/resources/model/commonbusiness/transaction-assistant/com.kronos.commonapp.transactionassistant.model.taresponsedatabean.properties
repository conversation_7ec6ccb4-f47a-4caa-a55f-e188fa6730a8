v1.0.commons-transaction_assistant.apimodel.response.description=Response object represent details of transaction assistant data.
v1.0.commons-transaction_assistant.apimodelproperty.startdatetime.description=The starting date time of a date range.
v1.0.commons-transaction_assistant.apimodelproperty.enddatetime.description=The ending date time of a date range.
v1.0.commons-transaction_assistant.apimodelproperty.sourcetype.description=Source of execution like SDM, UDM or Ihub.
v1.0.commons-transaction_assistant.apimodelproperty.retrysourcetype.description=The retry source type.
v1.0.commons-transaction_assistant.apimodelproperty.transactiontype.description=The type of transactions run.
v1.0.commons-transaction_assistant.apimodelproperty.datasourceid.description=ID belongs to the failed transaction error details.
v1.0.commons-transaction_assistant.apimodelproperty.sourcedetails.description=Source of the transaction error.
v1.0.commons-transaction_assistant.apimodelproperty.integrationname.description=Name of the installed integration that is the basis of this integration run.
v1.0.commons-transaction_assistant.apimodelproperty.integrationrunname.description=A unique integration run name to make it easier to identify the run of that integration.
v1.0.commons-transaction_assistant.apimodelproperty.integrationexecutionid.description=Unique ID for the integration run.
v1.0.commons-transaction_assistant.apimodelproperty.errortransactionid.description=Unique transaction for failed records.
v1.0.commons-transaction_assistant.apimodelproperty.errorcode.description=Error codes corresponding to failed transactions.
v1.0.commons-transaction_assistant.apimodelproperty.errordescription.description=Description belongs to failed transactions.
v1.0.commons-transaction_assistant.apimodelproperty.errordatetime.description=Date time of transaction error.
v1.0.commons-transaction_assistant.apimodelproperty.propertyname.description=Key identifier from the source causing the error.
v1.0.commons-transaction_assistant.apimodelproperty.propertyvalue.description=Key identifier from the source causing the error.
v1.0.commons-transaction_assistant.apimodelproperty.apiendpoint.description=The API end point for the transaction.
v1.0.commons-transaction_assistant.apimodelproperty.requestkey.description=Request key.
v1.0.commons-transaction_assistant.apimodelproperty.requestheader.description=Request header.
v1.0.commons-transaction_assistant.apimodelproperty.requestbody.description=Request body.
v1.0.commons-transaction_assistant.apimodelproperty.requestbodysize.description=Request body size.