v1.0.aggregatedtransferrestservice.name=Aggregated Transfer Data
v1.0.aggregatedtransferrestservice.parent=root.wtk.default
v1.0.aggregatedtransferrestservice.description=This resource allows you to get Labor Category entries, Work Rules and Cost Centers in a single API call based on employee's transfer permissions

v1.0.aggregatedtransferrestservice.getaggregatedtransferdata.nickname=Return Aggregated Transfer Data
v1.0.aggregatedtransferrestservice.getaggregatedtransferdata.notes=<p>This operation returns Aggregated Transfer Data.</p><br /><p>The associated Access Control Points are EA_WORKRULE_TRANSFERS, EA_COSTCENTER_TRANSFERS, and EA_ACCOUNT_TRANSFERS.</p>
v1.0.aggregatedtransferrestservice.getaggregatedtransferdata.summary=Return Aggregated Transfer Data
v1.0.aggregatedtransferrestservice.getaggregatedtransferdata.response.200.message=Successfully return Aggregated Transfer Data
v1.0.aggregatedtransferrestservice.getaggregatedtransferdata.response.400.message=Bad Request: <ul><li>[WCO-121201] - The value '{select-value}' specified in the select property is invalid. Valid values are: LABOR_ENTRIES, COST_CENTERS, or WORK_RULES. To return all entities, omit the select property.</li><li>[WCO-121202] - The where property is required.</li><li>[WCO-121203] - The date property within where is required.</li><li>[WCO-103015] - The organizational object reference is mandatory and cannot be missing or empty.</li><li>[laborcategory-common:47] - Location or Job not found. Location/Job: '{location}', Date: '{date}'</li></ul>

v1.0.aggregatedtransferdatarequest.apimodel.description=Aggregated Transfer Data Request model
v1.0.aggregatedtransferdatarequest.apimodelproperty.select.description=Select parameter defines all possible objects (Labor Entries, Cost Centers, Work Rules) that can be selected to be returned in the Aggregated Transfer Data request
v1.0.aggregatedtransferdatarequest.apimodelproperty.where.description=The criteria definition of an Aggregated Transfer Data request. Job and Date can be specified.

v1.0.aggregatedtransferdatawhere.apimodel.description=Aggregated Transfer Data Where Request model
v1.0.aggregatedtransferdatawhere.apimodelproperty.date.description=Date property on where part of the request
v1.0.aggregatedtransferdatawhere.apimodelproperty.orgJob.description=OrgObjectRef property of the request 

v1.0.aggregatedtransferdata.apimodel.description=Aggregated Transfer Data model
v1.0.aggregatedtransferdata.apimodelproperty.laborEntries.description=Collection of AggregatedTransferLaborEntry 
v1.0.aggregatedtransferdata.apimodelproperty.costCenters.description=Collection of all active Cost Centers
v1.0.aggregatedtransferdata.apimodelproperty.workRules.description=Collection of Work Rules assigned to logged user

v1.0.aggregatedtransferlaborentry.apimodel.description=Aggregated Transfer Labor Entry model
v1.0.aggregatedtransferdatawhere.apimodelproperty.id.description=Id of Labor Category
v1.0.aggregatedtransferdatawhere.apimodelproperty.name.description=Name of Labor Category
v1.0.aggregatedtransferdatawhere.apimodelproperty.allowedLaborLevelEntry.description=Collection of Labor Category Entries associated with Labor Entry