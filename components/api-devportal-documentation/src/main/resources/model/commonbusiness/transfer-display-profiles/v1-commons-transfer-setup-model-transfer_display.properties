# Transfer Display Profile Model DTO

v1.0.transfer-display.apimodel.description=The Transfer Display Model object model.
v1.0.transfer-display.apimodelproperty.id.description=The ID of a transfer display profile.
v1.0.transfer-display.apimodelproperty.name.description=The name of a transfer display profile.
v1.0.transfer-display.apimodelproperty.description,description=The description of a transfer display profile.
v1.0.transfer-display.apimodelproperty.panelDisplayType.description=The panel display type of a transfer display profile. Valid values include BOTH(0), JOB(1), and LOCATION(2).
v1.0.transfers-display.apimodelproperty.orgNodeTypes.description=A list of references to <code>orgNodeType</code> objects, which are nodes on the Business Structure.
v1.0.transfers-display.apimodelproperty.expiredOrgNodeTypes.description=A list of references to expired `orgNodeType` objects, which are nodes on the Business Structure.
v1.0.transfers-display.apimodelproperty.displayInTransfer.description=An optional array containing `orgNodeTypes` and `skippable` properties, which support the Simplified Business Structure.