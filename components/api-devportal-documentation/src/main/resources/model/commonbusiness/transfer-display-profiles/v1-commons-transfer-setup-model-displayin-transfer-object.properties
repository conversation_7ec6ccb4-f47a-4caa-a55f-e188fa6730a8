v1.0.transfer-display-locationTypes.apimodel.description=The Display In Transfer object model to support transfer display profiles in Simplified Business Structure. 
v1.0.transfer-display-locationTypes.apimodelproperty.locationType.description=This object contains the `orgNodeType` references to which a `displayInTranfer` is assigned.
v1.0.transfer-display-locationTypes.apimodelproperty.skippable.description=A Boolean indicator of whether or not the selected location type is marked as non-mandatory or skippable. The default value is false, which indicates the selected location type is mandatory.