#Comments for Properties
#Tue May 16 10:54:42 IST 2017
v1.0.restpostbean.apimodel.description=Model for REST POST request.
v1.0.restpostbean.apimodelproperty.url.description=(Required)The end-point address (URL) for the request.
v1.0.restpostbean.apimodelproperty.headers.description=(Required) The HTTP header field that provides required information about the request.
v1.0.restpostbean.apimodelproperty.postbody.description=(Required) The body of the request.
