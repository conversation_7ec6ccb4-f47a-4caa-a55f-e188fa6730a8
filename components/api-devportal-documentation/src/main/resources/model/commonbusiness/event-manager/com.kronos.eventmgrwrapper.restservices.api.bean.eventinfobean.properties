#Comments for Properties
#Tue May 16 10:54:42 IST 2017
# v1.0.eventinfobean.apimodel.description=Description of the event.
# v1.0.eventinfobean.apimodelproperty.deleted.description=A Boolean indicator of whether or not an event has been deleted.
# v1.0.eventinfobean.apimodelproperty.modified.description=A Boolean indicator of whether or not an event has been modified.
# v1.0.eventinfobean.apimodelproperty.statuschange.description=A Boolean indicator of whether or not an event's status has been changed.
# v1.0.eventinfobean.apimodelproperty.newflag.description=A Boolean indicator of whether or not an event is new.
# v1.0.eventinfobean.apimodelproperty.useraccess.description=The user access status of an event.
# v1.0.eventinfobean.apimodelproperty.eventaction.description=The event action associated with an event.
# v1.0.eventinfobean.apimodelproperty.eventstatus.description=The event status associated with an event.
# v1.0.eventinfobean.apimodelproperty.jobid.description=The unique ID for the job associated with an event.
# v1.0.eventinfobean.apimodelproperty.eventtype.description=The type of an event.
# v1.0.eventinfobean.apimodelproperty.useraccountid.description=The unique ID for the user account associated with an event.
# v1.0.eventinfobean.apimodelproperty.comment.description=A Comment associated with an event.
# v1.0.eventinfobean.apimodelproperty.datemodified.description=The date of the last modification to an event.
# v1.0.eventinfobean.apimodelproperty.emaillist.description=The email list associated with an event.
# v1.0.eventinfobean.apimodelproperty.errortext.description=The error message passed if an event fails.
# v1.0.eventinfobean.apimodelproperty.eventname.description=The name of an event.
# v1.0.eventinfobean.apimodelproperty.fullname.description=The full name of an event.
# v1.0.eventinfobean.apimodelproperty.lastrundatetime.description=The date and time of the most recent scheduled event run.
# v1.0.eventinfobean.apimodelproperty.modifiedbyusername.description=The user who modified the event.
# v1.0.eventinfobean.apimodelproperty.nextrundatetime.description=The date and time of the next scheduled event run.
# v1.0.eventinfobean.apimodelproperty.summary.description=The summary of an event.
# v1.0.eventinfobean.apimodelproperty.username.description=The person who created the event.
# v1.0.eventinfobean.apimodelproperty.lastmanualrundatetime.description=The date of the most recent manually triggered event run.
# v1.0.eventinfobean.apimodelproperty.personid.description=An ID that uniquely identifies an employee associated with an event. This ID is the same as <code>employeeId</code> and is not a person number.
# v1.0.eventinfobean.apimodelproperty.roleid.description=The Role ID of the person who is associated with an event.
# v1.0.eventinfobean.apimodelproperty.delegatorId.description=The employee ID of the person who is acting as a delegator. This is not a person number.
# v1.0.eventinfobean.apimodelproperty.xmlkey.description=The XML key associated with an event.
