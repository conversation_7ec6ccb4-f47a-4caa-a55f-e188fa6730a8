#Comments for Properties
#Tue May 16 10:54:41 IST 2017
# v1.0.schedulinginfobean.apimodel.description=Detailed information about the schedule
# v1.0.schedulinginfobean.apimodelproperty.frequency.description=Frequency of the event.
# v1.0.schedulinginfobean.apimodelproperty.frequencytype.description=Type of frequency. Examples: monthly, weekly, daily.
# v1.0.schedulinginfobean.apimodelproperty.interval.description=Time interval between event runs.
# v1.0.schedulinginfobean.apimodelproperty.starttime.description=Start time
# v1.0.schedulinginfobean.apimodelproperty.endtime.description=End time
# v1.0.schedulinginfobean.apimodelproperty.forever.description=If the end time of the schedule is not specified, the end time is forever.
# v1.0.schedulinginfobean.apimodelproperty.startdatetime.description=The start date and time
# v1.0.schedulinginfobean.apimodelproperty.enddatetime.description=The end date and time

# This file is a duplicate of /components/api-devportal-documentation/src/main/resources/model/report/report-executionframework/com.kronos.eventmgrwrapper.restservices.api.bean.schedulinginfobean.properties