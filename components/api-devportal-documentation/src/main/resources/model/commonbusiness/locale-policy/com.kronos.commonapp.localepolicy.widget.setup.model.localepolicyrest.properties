#Comments for Properties
#Tue Feb 28 10:30:27 IST 2017
v1.0.localepolicyrest.apimodel.description=Model for Locale Policy.
v1.0.localepolicyrest.apimodelproperty.id.description=The ID of a locale policy.
v1.0.localepolicyrest.apimodelproperty.activesw.description=The status of a locale policy.
v1.0.localepolicyrest.apimodelproperty.istenantdefault.description=A Boolean indicator of whether or not the locale policy is the default for a tenant.
v1.0.localepolicyrest.apimodelproperty.displaydatetimeformats.description=The format used to display date and time to the user defined in a locale policy.
v1.0.localepolicyrest.apimodelproperty.datetimeformats.description=The format of date and time defined in a locale policy.
v1.0.localepolicyrest.apimodelproperty.numberformat.description=The format of numbers defined in a locale policy.
v1.0.localepolicyrest.apimodelproperty.currencyformat.description=The format of currency defined in a locale policy.
v1.0.localepolicyrest.apimodelproperty.weekstartday.description=The starting day of the week defined in a locale policy.
v1.0.localepolicyrest.apimodelproperty.showweeknumber.description=A Boolean indicator for displaying or hiding week numbers.
v1.0.localepolicyrest.apimodelproperty.enforceemailuniqueness.description=A boolean indicator of whether or not the locale policy enforces email uniqueness.
