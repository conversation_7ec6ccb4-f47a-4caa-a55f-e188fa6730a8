#Comments for Properties
v1.0.commons-delegate_profile_jobstatus.apimodel.description=The Retrieve Summary of Asynchronous Delegate Profile Jobs response model.
v1.0.delegateprofile.jobstatus.apimodelproperty.status.description=The status of a delegate profile asynchronous request job.
v1.0.delegateprofile.jobstatus.apimodelproperty.message.description=The detailed description of the status of a request, such as PENDING, FAILED and SUCCESSFUL.
v1.0.delegateprofile.jobstatus.apimodelproperty.expiresAt.description=The date and time a submitted Job expires in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.delegateprofile.jobstatus.apimodelproperty.nextPing.description=The time duration in seconds of the next status call ping of an in-progress Job.
v1.0.delegateprofile.jobstatus.get.delegateprofile.jobid=The job ID returned in asynchronous operation's response.