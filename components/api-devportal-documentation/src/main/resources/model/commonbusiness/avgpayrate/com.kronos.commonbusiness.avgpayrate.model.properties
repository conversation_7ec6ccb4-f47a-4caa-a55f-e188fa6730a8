v1.0.averagepayrate.apimodel.description=Model context for an average pay rate.
v1.0.averagepayrate.apimodelproperty.effectiveDate.description=The effective date of an average pay rate in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.averagepayrate.apimodelproperty.enddate.description=The end date of an average pay rate in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.averagepayrate.apimodelproperty.rate.description=The rate of an average pay rate.
v1.0.averagepayrate.apimodelproperty.location.description=A reference to the location object.