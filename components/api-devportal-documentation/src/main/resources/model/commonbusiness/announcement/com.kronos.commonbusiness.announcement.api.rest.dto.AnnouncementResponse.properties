v1.0.announcement-response.apimodel.description=Object containing information regarding an Announcement Response.
v1.0.announcement-response.apimodelproperty.id.description=The ID of an announcement response.
v1.0.announcement-response.apimodelproperty.sentdatetime.description=The date and time an announcement response is sent in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.announcement-response.apimodelproperty.updateddatetime.description=The date and time an announcement response is updated in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.announcement-response.apimodelproperty.employee.description=The ID and qualifier of the employee who received and/or responded to the announcement message.
v1.0.announcement-response.apimodelproperty.action.description=The ID and name of the response action chosen by the employee.