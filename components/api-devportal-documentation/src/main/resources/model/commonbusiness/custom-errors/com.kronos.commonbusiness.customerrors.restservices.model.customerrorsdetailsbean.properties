v1.0.customerrorsdetailsrequest.apimodel.description=The Custom Errors Details request object model.
v1.0.customerrorsdetailsrequest.apimodelproperty.message.description=A message representing the batch that was processed or the action that was taken.
v1.0.customerrorsdetailsrequest.apimodelproperty.errorKey.description=The domain entity for the recorded error.
v1.0.customerrorsdetailsrequest.apimodelproperty.errorValue.description=The actual error that resulted in a failure or business disqualification.
v1.0.customerrorsdetailsrequest.apimodelproperty.description.description=The description of the custom error that includes details of the error to help troubleshoot, or the steps to resolve.
v1.0.customerrorsdetailsrequest.apimodelproperty.value.description=The actual error that resulted in a failure or business disqualification.
v1.0.customerrorsdetailsrequest.apimodelproperty.key.description=The key of a custom error details object representing the domain entity.