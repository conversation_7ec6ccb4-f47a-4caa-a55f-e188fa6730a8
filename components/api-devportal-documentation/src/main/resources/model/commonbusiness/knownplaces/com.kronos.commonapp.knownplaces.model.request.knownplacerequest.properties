v1.0.knownplaces.apimodel.request.description=Known Place request model.
v1.0.knownplace.apimodel.request.description=Known Place request model.
v1.0.knownplace.apimodel.request.property.id.description=The ID of a Known Place.
v1.0.knownplace.apimodel.request.property.name.description=The name of a Known Place.
v1.0.knownplace.apimodel.request.property.description.description=The description of a Known Place.
v1.0.knownplace.apimodel.request.property.latitude.description=The latitude associated with a Known Place.
v1.0.knownplace.apimodel.request.property.longitude.description=The longitude associated with a Known Place.
v1.0.knownplace.apimodel.request.property.radius.description=The radius of a Known Place.
v1.0.knownplace.apimodel.request.property.version.description=The version of a Known Place.
v1.0.knownplace.apimodel.request.property.active.description=A Boolean indicator of whether or not a Known Place is active.
v1.0.knownplace.apimodel.request.property.locations.description=A list of locations associated with a Known Place.
v1.0.knownplace.apimodel.request.property.accuracy.description=The GPS accuracy of a Known Place.
v1.0.knownplace.apimodel.request.property.validation_order.description=An array defining the location validation order of a Known Place which supports two values: WIFI and GPS. The user's location is validated based on the prioritized order of this array.
v1.0.knownplace.apimodel.request.property.wifi_networks.description=A list of WiFi networks associated with a Known Place.
v1.0.knownplace.apimodel.request.property.sdmKey.description=Known Place SDM key (internal).
v1.0.knownplace.apimodel.request.property.multi_factor.description=A Boolean indicator of whether or not a Known Place supports multi_factor.
v1.0.knownplace.apimodel.request.property.ranking_qr.description=The ranking QR of Known Place.
v1.0.knownplace.apimodel.request.property.persistentid.description=The persistent ID associated with a Known Place.