v1.0.laborcategoryentrylistassignment.apimodel.description=Entity used for associating a labor category entry list with an organization node.
v1.0.laborcategoryentrylistassignment.apimodel.value=The value of a labor category entry list assignment.
v1.0.laborcategoryentrylistassignment.apimodelproperty.id.description=The ID of a labor category entry list assignment.
v1.0.laborcategoryentrylistassignment.apimodelproperty.orgnode.description=A reference to an organization node object.
v1.0.laborcategoryentrylistassignment.apimodelproperty.laborcategory.description=A reference to a labor category object.
v1.0.laborcategoryentrylistassignment.apimodelproperty.laborcategorylist.description=A reference to a labor category entry list object.
v1.0.laborcategoryentrylistassignment.apimodelproperty.effectivedate.description=The effective date of the association between a labor category entry list and an organization node. An effective date represents the date the association became active.
v1.0.laborcategoryentrylistassignment.apimodelproperty.expirationdate.description=The date the association between a labor category entry list and an organization node ceases to be active.
v1.0.laborcategoryentrylistassignment.apimodelproperty.version.description=The current version of the entity. This is used to ensure that an entity is not updated with stale data.
