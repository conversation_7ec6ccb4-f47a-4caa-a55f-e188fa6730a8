v1.0.laborcategorylaborentrylistaccessrequestdataforrest.apimodel.description=Retrieve Labor Entry List Access request model.
v1.0.laborcategorylaborentrylistaccessrequestdataforrest.apimodel.value=The value associated with labor entry list access.
v1.0.laborcategorylaborentrylistaccessrequestdataforrest.apimodelproperty.isManagerRole.description=A Boolean indicator of whether or not the currently logged in user has a manager role.
v1.0.laborcategorylaborentrylistaccessrequestdataforrest.apimodelproperty.employee.description=A reference to the employee object for which the labor entry list access is retrieved.
v1.0.laborcategorylaborentrylistaccessrequestdataforrest.apimodelproperty.position.description=A reference to the employee's position object.
v1.0.laborcategorylaborentrylistaccessrequestdataforrest.apimodelproperty.orgJob.description=The organizational job on the business structure.
v1.0.laborcategorylaborentrylistaccessrequestdataforrest.apimodelproperty.asOfDate.description=The transaction effective date in ISO_LOCAL_DATE format (YYYY-MM-DD).
