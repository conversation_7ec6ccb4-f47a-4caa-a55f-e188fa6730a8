v1.0.exchangerateparam.apimodel.description=DTO that contains parameters for getting the exchange rate between two currencies.
v1.0.exchangerateparam.apimodelproperty.source-currency-ref.description=A reference to the source currency.
v1.0.exchangerateparam.apimodelproperty.target-currency-ref.description=A reference to the target currency.
v1.0.exchangerateparam.apimodelproperty.apply-date.description=The date for which an exchange rate is required in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.exchangerateparam.apimodelproperty.date-range.description=The date range for which an exchange rate is required.