#Comments for Properties
#Wed Feb 22 09:31:44 IST 2017
v1.0.userparameter.apimodel.description=The user parameter describes the parameters and display types. It is a dependent object that is associated with a parent entity such as a report or integration.
v1.0.userparameter.apimodelproperty.name.description=The unique name of a parameter.
v1.0.userparameter.apimodelproperty.type.description=The type of parameter display: RADIO, COMBO, TEXT, or LIST.
v1.0.userparameter.apimodelproperty.value.description=The value of a parameter.
v1.0.userparameter.apimodelproperty.label.description=The label of a parameter.
v1.0.userparameter.apimodelproperty.description.description=The description of a parameter.
v1.0.userparameter.apimodelproperty.listvalues.description=A list of values for drop-down (COMBO) or LIST display parameters.
v1.0.userparameter.apimodelproperty.mandatory.description=A Boolean indicator of whether or not the parameter is required.
v1.0.userparameter.apimodelproperty.templateparamname.description=The name of an integration template parameter.