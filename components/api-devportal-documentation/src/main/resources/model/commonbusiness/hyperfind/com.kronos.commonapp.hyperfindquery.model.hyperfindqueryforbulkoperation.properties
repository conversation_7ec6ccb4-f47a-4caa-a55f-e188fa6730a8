v1.0.hyperfindqueryforbulkoperation.apimodel.description=The Hyperfind query model.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.id.description=The unique ID of a Hyperfind query.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.name.description=The name of a Hyperfind query.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.description.description=The description of a Hyperfind query.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.visibility.description=The visibility type of a Hyperfind query.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.requiresvalidation.description=A Boolean indicator of whether or not the Hyperfind requires validation.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.createdbyuserid.description=The ID of the user creating the Hyperfind query.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.createdbypersonnum.description=Person Number of user for which the Hyperfind will be created.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.updatedbyuserid.description=The ID of the user updating the hyperfind query.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.constraints.description=A list of constraints included in this Hyperfind query.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.persistentid.description=The persistent ID of a Hyperfind query.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.usagetype.description=The usage type of a Hyperfind query. Valid values include HOME and REGULAR.
v1.0.hyperfindqueryforbulkoperation.apimodelproperty.usagetype.allowablevalues=HOME, REGULAR