v1.0.multi-delete-ref.apimodel.description=The Retrieve Hyperfind Queries multi_delete references model, which contains the actual criteria for retrieving Hyperfind queries.
v1.0.multi-delete-ref.apimodelproperty.ids.description=An array of IDs of Hyperfind queries.
v1.0.multi-delete-ref.apimodelproperty.qualifiers.description=An array of qualifiers of Hyperfind queries.
v1.0.multi-delete-ref.apimodelproperty.refs.description=An array of references to Hyperfind queries.
v1.0.multi-delete-ref.apimodelproperty.persistentIds.description=An array of persistent IDs of Hyperfind queries. Deletion using persistentIds only work for Public hyperfind.