v1.0.filterinformation.apimodel.description=This model is used to set filter properties.
v1.0.filterinformation.apimodelproperty.condition.description=The condition is a clause which populates the left operands in a query. These are the criteria upon which constraints are applied.
v1.0.filterinformation.apimodelproperty.operator.description=The operator used in a constraint.
v1.0.filterinformation.apimodelproperty.data.description=The data which is used by the right operands in a query.
v1.0.filterinformation.apimodelproperty.asof.description=This contains the as-of criteria to filter data for any pay period.
v1.0.filterinformation.apimodelproperty.range.description=The range parameter is used to determine the having clause in the generated query.
