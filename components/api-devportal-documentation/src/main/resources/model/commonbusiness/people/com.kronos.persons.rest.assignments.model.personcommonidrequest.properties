#Comments for Properties
#Saturday Sept 02 2017
v1.0.personcommonidrequest.apimodel.description=PersonCommonId defines an Object Identifier for a person.
v1.0.personcommonidrequest.apimodelproperty.personidentity.description=The IDentifies the person as specified in <code>PersonIdentity</code>.
v1.0.personcommonidrequest.apimodelproperty.coid.description=The Client Object ID (COID) of a person in the system.
v1.0.personcommonidrequest.apimodelproperty.aoid.description=The Associate Object ID (AOID) of a person in the system.
v1.0.personcommonidrequest.apimodelproperty.error.description=The error details in the event of an exception.
