#Comments for Properties
#Thu Apr 13 09:33:40 IST 2017
v1.0.useraccountbean.apimodel.description=UserAccount returns logon information for the specified person, including the user's username and password.
v1.0.useraccountbean.apimodelproperty.consecutivebadlogons.description=Number of consecutive bad logon attempts the user is permitted before being locked out of the system.
v1.0.useraccountbean.apimodelproperty.lockoutresetdatetime.description=The date and time after which a user who is currently locked out may log on to the system.
v1.0.useraccountbean.apimodelproperty.logonprofilename.description=The logon profile column contains the name of the user's logon profile. the logon profile provides rules for password behavior or provides rules for automatic logout and lockout behavior.
v1.0.useraccountbean.apimodelproperty.passwordupdateddatetime.description=The date and time that the password was last updated.
v1.0.useraccountbean.apimodelproperty.passwordupdateflag.description=A Boolean indicator of whether or not the user must change the password the next time he or she logs on.
v1.0.useraccountbean.apimodelproperty.recordmanagerlockoutflag.description=A Boolean indicator of whether or not the user account is locked out of the target system until after the record management system has finished copying data. If false, the user account is able to access the target database regardless of the record management system status.
v1.0.useraccountbean.apimodelproperty.username.description=This is the user name that allows the user to access the timekeeper system. a unique logon name is required.
v1.0.useraccountbean.apimodelproperty.userpassword.description=The user's password must be specified if username is specified.
v1.0.useraccountbean.apimodelproperty.mfarequired.description=<p>A Boolean indicator of whether or not Multi-Factor Authentication (MFA) is required by an employee extension. <strong>Note:</strong> You can enable or disable MFA for managers only if the global property <code>global.authentication.mfa.manager.override</code> is true. By default, MFA is enabled for basic authentication of new users and managers.</p>
v1.0.useraccountbean.apimodelproperty.clockonlyuser.description=A Boolean indicator of whether or not the user is a clock-only user.
v1.0.useraccountbean.apimodelproperty.identityid.description=A unique identifier for a user assigned by the identity provider.
v1.0.useraccountbean.apimodelproperty.accountlocked.description=The account locked status associated with an employee extension. This property is read-only and cannot be updated through the API.
v1.0.useraccountbean.apimodelproperty.ssousername.description=The single sign-on (SSO) user name.