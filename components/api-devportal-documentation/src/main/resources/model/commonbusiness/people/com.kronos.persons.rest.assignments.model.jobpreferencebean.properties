#Comments for Properties
#Thu Apr 13 09:33:40 IST 2017
v1.0.jobPreferenceBean.apimodel.description=Job Preference enables you to request access to job preferences for any given employee.
v1.0.jobPreferenceBean.apimodel.value=Job Preference
v1.0.jobPreferenceBean.apimodelproperty.job.description=The job preference path.
v1.0.jobPreferenceBean.apimodelproperty.preference.description=The preference level of a job. Valid values are 0 to 10.
v1.0.jobPreferenceBean.apimodelproperty.seniorityDate.description=The seniority date.
v1.0.jobPreferenceBean.apimodelproperty.workRuleId.description=The ID of a work rule.
v1.0.jobPreferenceBean.apimodelproperty.schedulingContextSet.description=An array of context reference objects used for assigning job preferences.