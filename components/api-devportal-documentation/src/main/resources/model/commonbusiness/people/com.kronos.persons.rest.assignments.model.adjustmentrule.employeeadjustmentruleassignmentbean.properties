#Comments for Properties
#Sunday Sep 03 2017
v1.0.employeeadjustmentruleassignmentbean.apimodel.value=Employee Adjustment Rule Assignment
v1.0.employeeadjustmentruleassignmentbean.apimodel.description=Assign Adjustment Rules defined on a system to an employee. This assignment is effective dated.
v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.effectivedate.description=The first date that an Adjustment Rule applies to an employee.
v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.expirationdate.description=The last date of an association of an Adjustment Rule and an employee. To establish an expiration date which occurs before 1/1/3000 for an adjustment rule assignment, specify a a new adjustment rule (which optionally can contain no triggers if you want to end all adjustments) and assign it with an effective date equal to the day you wish the first adjustment rule to terminate.
v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.originaleffectivedate.description=The original effective date of the association of an extension processor and an employee. This property must be used with the "Update" action.
v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.personidentity.description=The <code>PersonIdentity</code> of the employee to be associated with an Adjustment Rule.
v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.processor.description=This property is deprecated. Use <code>adjustmentRule</code> instead.
v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.adjustmentRule.description=The adjustment rule associated with an assignment. Pass an ID of -1 with no qualifier to assign the 'Empty Profile' adjustment rule. <strong>Note:</strong> This property replaces the deprecated <code>processor</code> property. Pass a qualifier to duplicate the functionality of the deprecated <code>processor</code> property. 
