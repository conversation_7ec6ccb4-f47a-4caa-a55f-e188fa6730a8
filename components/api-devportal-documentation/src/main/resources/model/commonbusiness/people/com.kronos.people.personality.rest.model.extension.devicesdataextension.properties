#Comments for Properties
#Thu Apr 13 09:33:40 IST 2017
v1.0.devicesdataextension.apimodel.description=Device extension model.
v1.0.devicesdataextension.apimodel.name=DeviceExtension
v1.0.devicesdataextension.apimodelproperty.badgedetails.description=The badge details associated with a device extension.
v1.0.devicesdataextension.apimodelproperty.brazildevicegroup.description=The Brazil device group name associated with a device extension.
v1.0.devicesdataextension.apimodelproperty.brazildevicegroupid.description=The Brazil device group ID associated with a device extension.
v1.0.devicesdataextension.apimodelproperty.devicegroupid.description=The device group ID associated with a device extension.
v1.0.devicesdataextension.apimodelproperty.fingerrequired.description=A Boolean indicator of whether or not a finger scan is required.
v1.0.devicesdataextension.apimodelproperty.fingerscans.description=The finger scans associated with a device extension.
v1.0.devicesdataextension.apimodelproperty.fingerconsentdetails.description=The finger consent details.
v1.0.devicesdataextension.apimodelproperty.faceconsentdetails.description=The face consent details.
v1.0.devicesdataextension.apimodelproperty.udmdevicegroup.description=The UDM device group name associated with a device extension.
v1.0.devicesdataextension.apimodelproperty.devicegroup.description=The device group name associated with a device extension.
v1.0.devicesdataextension.apimodelproperty.facescan.description=The face scans associated with a device extension.
v1.0.devicesdataextension.apimodelproperty.facerequired.description=A Boolean indicator of whether or not a face scan is required.
v1.0.devicesdataextension.apimodelproperty.ttipemployee.description=A Boolean indicator of whether or not an employee is a TeleTime IP employee.
v1.0.devicesdataextension.apimodelproperty.changepasswordisrequired.description=A Boolean indicator of whether or not a change of passwords is required.
v1.0.devicesdataextension.apimodelproperty.ttipid.description=The TeleTime IP ID associated with a device extension.
v1.0.devicesdataextension.apimodelproperty.ttipuserprofileid.description=The TeleTime IP User profile ID associated with a device extension.
v1.0.devicesdataextension.apimodelproperty.ttipuserprofilename.description=The TeleTime IP User profile name associated with a device extension.

#Face Scan Data Extension

v1.0.facescandataextension.apimodel.description=Face scan data model.
v1.0.facescandataextension.apimodelproperty.enrollmentdatetime.description=The date and time of face scan enrollment in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.facescandataextension.apimodelproperty.thresholdlabel.description=The threshold label associated with a face scan.
v1.0.facescandataextension.apimodelproperty.templatedeleted.description=A Boolean indicator of whether or not a template is deleted.
v1.0.facescandataextension.apimodelproperty.qualityscore.description=The quality score associated with a face scan.
v1.0.facescandataextension.apimodelproperty.threshold.description=The threshold associated with a face scan.
v1.0.facescandataextension.apimodelproperty.enrolled.description=A Boolean indicator of whether or not an employee is enrolled in face scanning.