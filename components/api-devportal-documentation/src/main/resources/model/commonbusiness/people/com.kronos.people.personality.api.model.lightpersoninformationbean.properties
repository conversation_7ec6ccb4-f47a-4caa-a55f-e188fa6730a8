v1.0.lightpersoninformationbean.apimodel.description=The lightweight person model.
v1.0.lightpersoninformationbean.apimodelproperty.personid.description=The person ID of a person, which is the same as an employee ID and is not a person number.
v1.0.lightpersoninformationbean.apimodelproperty.personnum.description=The person number of a person, which is not the same identifier as an employee ID or person ID.
v1.0.lightpersoninformationbean.apimodelproperty.firstName.description=The first name of a person.
v1.0.lightpersoninformationbean.apimodelproperty.lastName.description=The last name of a person.
v1.0.lightpersoninformationbean.apimodelproperty.birthDate.description=The birth date of a person.
v1.0.lightpersoninformationbean.apimodelproperty.updatedTime.description=The last time a person's record was updated in ISO_LOCAL_TIME format (HH:mm:ss.SSS).
v1.0.lightpersoninformationbean.apimodelproperty.emailAddresses.description=The set of email addresses associated with a person.
v1.0.lightpersoninformationbean.apimodelproperty.telephoneNumbers.description=The set of telephone numbers associated with a person.
v1.0.lightpersoninformationbean.apimodelproperty.records.description=An array of records associated with a person.
v1.0.lightpersoninformationbean.apimodelproperty.totalelements.description=The total number of elements associated with a person.