#Comments for Properties
#Thu Apr 13 09:33:40 IST 2017
v1.0.personalovertimerulebean.apimodel.description=PersonalOvertimeRule provides the amount information associated with a personal overtime assignment. An overtime rule establishes the limits and reset methods for accumulating overtime hours. When employees reach overtime goals within specified time periods, they start to earn overtime hours. There is always one personal overtime rule for each personal overtime assignment.
v1.0.personalovertimerulebean.apimodelproperty.personalovertimelimits.description=The list of personal overtime limit tags that are associated with the personal overtime rule.
v1.0.personalovertimerulebean.apimodelproperty.personalovertimeruledisplayname.description=The name of a personal overtime rule used for an external user.
v1.0.personalovertimerulebean.apimodelproperty.usescheduleflag.description=A Boolean indicator of whether or not use a schedule flag. When true, the personal overtime rule uses the days in the schedule to determine when overtime is allowed.
