v1.0.employeeJobPreferencesBean.apimodel.description=The employee job preferences model, which enables you to specify request to access job preferences for any given employee.
v1.0.employeeJobPreferencesBean.apimodelproperty.employeeJobPreferences.description=A list of employee job preferences.
v1.0.jobpreferencebean.apimodelproperty.job.description=The job preference path.
v1.0.jobPreferenceBean.apimodelproperty.preference.description=The preference level of a job. Valid values are 0 to 10.
v1.0.jobPreferenceBean.apimodelproperty.schedulingContextSet.description=An array of context reference objects used for assigning job preferences.