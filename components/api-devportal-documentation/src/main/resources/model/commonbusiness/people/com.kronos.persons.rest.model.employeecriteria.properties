#Comments for Properties
#Thu Sep 7 09:33:40 IST 2017
v1.0.employeecriteria.apimodel.name=EmployeeCriteria
v1.0.employeecriteria.apimodel.description=Criteria for employee information.
v1.0.employeecriteria.apimodelproperty.key.description=The key associated with employee information.
v1.0.employeecriteria.apimodelproperty.values.description=The search values associated with employee information.
v1.0.employeecriteria.apimodelproperty.multiKey.description=The multi-search key pair. The key pair can be Associate Object ID (AOID) and Client Object ID (COID) or Person Identifier (personId or personNumber) and Position Identifier (positionId, positionName and positionExternalId).
v1.0.employeecriteria.apimodelproperty.multiKeyValues.description=The multi-search value pair. The value pair can be Associate Object ID (AOID) and Client Object ID (COID) or Person Identifier (personId or personNumber) and Position Identifier (positionId, positionName and positionExternalId).
