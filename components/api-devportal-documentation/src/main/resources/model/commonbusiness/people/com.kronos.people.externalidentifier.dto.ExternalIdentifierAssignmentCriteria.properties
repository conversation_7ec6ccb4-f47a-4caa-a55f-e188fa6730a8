v1.0.externalIdentifierAssignmentCriteria.apimodel.description=Model for search criteria in multi_read having combination of key and value.
v1.0.externalIdentifierAssignmentCriteria.apimodelproperty.key.description=This is key field which will hold the value of identifier type in multi_read of external identifier.
v1.0.externalIdentifierAssignmentCriteria.apimodelproperty.values.description=This is values field which will hold the values of identifier value in multi_read of external identifier.
