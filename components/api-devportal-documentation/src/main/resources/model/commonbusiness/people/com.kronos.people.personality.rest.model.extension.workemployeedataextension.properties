v1.0.workemployeedataextension.apimodel.description=Work employee details.
v1.0.workemployeedataextension.apimodelproperty.profileid.description=The ID of activity profile.
v1.0.workemployeedataextension.apimodelproperty.profilename.description=The name of activity profile.
v1.0.workemployeedataextension.apimodelproperty.defaultactivityid.description=The ID of default activity.
v1.0.workemployeedataextension.apimodelproperty.defaultactivityname.description=The name of default activity.
v1.0.workemployeedataextension.apimodelproperty.idleactivityid.description=The ID of idle activity.
v1.0.workemployeedataextension.apimodelproperty.idleactivityname.description=The name of idle activity.
v1.0.workemployeedataextension.apimodelproperty.unpaidactivityid.description=The ID of unpaid meal activity.
v1.0.workemployeedataextension.apimodelproperty.unpaidactivityname.description=The name of meal activity.
v1.0.workemployeedataextension.apimodelproperty.paidactivityid.description=The ID of paid meal activity.
v1.0.workemployeedataextension.apimodelproperty.paidactivityname.description=The name of paid break activity.
v1.0.workemployeedataextension.apimodelproperty.autoresolveactivityid.description=The ID of an auto-resolve activity.
v1.0.workemployeedataextension.apimodelproperty.autoresolveactivityname.description=The name of an auto-resolve activity.
v1.0.workemployeedataextension.apimodelproperty.currentlistqueryid.description=The ID of current list query.
v1.0.workemployeedataextension.apimodelproperty.currentlistqueryname.description=The name of current list query.
v1.0.workemployeedataextension.apimodelproperty.activityDefaults.description=A list of activity default entities assigned to a person.