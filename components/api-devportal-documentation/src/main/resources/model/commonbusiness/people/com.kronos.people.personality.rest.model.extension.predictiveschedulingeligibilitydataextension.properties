v1.0.predictiveschedulingeligibilitydataextension.apimodel.description=Model for the predictive scheduling eligibility record associated with a Scheduling extension.
v1.0.predictiveschedulingeligibilitydataextension.apimodelproperty.iseligible.description=A Boolean indicator of whether or not a person is eligible for predictive scheduling.

#Predictive Scheduling Rule

v1.0.predsched.rule.apimodel.description=Model for a predictive scheduling rule.
v1.0.predsched.rule.apimodelproperty.name.description=The name of a predictive scheduling rule.
v1.0.predsched.rule.apimodelproperty.description.description=The description of a predictive scheduling rule.
v1.0.predsched.rule.apimodelproperty.id.description=The ID of a predictive scheduling rule.
v1.0.predsched.rule.apimodelproperty.deleted.description=A Boolean indicator of whether or not a predictive scheduling rule is deleted.
v1.0.predsched.rule.apimodelproperty.effectivepredictiveschedulingrules.description=An array of effective predictive scheduling rules.

v1.0.predsched.rule.trigger.apimodelproperty.schedulechangecriteria.description=A reference to a set of schedule change criteria.
v1.0.predsched.rule.trigger.apimodelproperty.predictiveschedulingbonus.description=A reference to a predictive scheduling bonus.
v1.0.predsched.rule.trigger.apimodelproperty.id.description=The ID of a predictive scheduling rule trigger.
v1.0.predsched.rule.trigger.apimodelproperty.predictiveSchedulingPriority.description=The value of scheduling rule trigger priority

#Predictive Scheduling Override Rule

v1.0.predictiveschedulingoverridedataextension.apimodel.description=Model for the predictive scheduling override record associated with a Scheduling extension.
v1.0.predictiveschedulingoverridedataextension.apimodelproperty.predictiveruleoverride.description=The name of a predictive scheduling rule.
v1.0.predictiveschedulingoverridedataextension.apimodelproperty.predictiveruleoverrideid.description=The ID of a predictive scheduling rule.