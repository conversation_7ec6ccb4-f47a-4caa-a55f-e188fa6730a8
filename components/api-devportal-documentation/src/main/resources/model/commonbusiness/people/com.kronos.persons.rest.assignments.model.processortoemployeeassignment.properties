v1.0.processortoemployeeassignment.apimodel.description=Processor to employee assignment.
v1.0.processortoemployeeassignment.apimodelproperty.effectivedate.description=The effective date of the assignment.
v1.0.processortoemployeeassignment.apimodelproperty.expirationdate.description=The expiration date of the assignment.
v1.0.processortoemployeeassignment.apimodelproperty.originaleffectivedate.description=The original start date of the assignment.
v1.0.processortoemployeeassignment.apimodelproperty.personidentity.description=Person identity assigned to association.
v1.0.processortoemployeeassignment.apimodelproperty.processor.description=Processor assigned to association.

v1.0.processortoemployeecriteria.apimodel.description=Processor to employee criteria.
v1.0.processortoemployeecriteria.apimodelproperty.effectivedate.description=The effective date of the assignment.
v1.0.processortoemployeecriteria.apimodelproperty.personidentity.description=Person identity assigned to association.
v1.0.processortoemployeecriteria.apimodelproperty.processor.description=Processor assigned to association.
