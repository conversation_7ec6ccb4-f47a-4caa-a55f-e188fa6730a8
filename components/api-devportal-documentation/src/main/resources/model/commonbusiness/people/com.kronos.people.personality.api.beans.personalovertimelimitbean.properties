#Comments for Properties
#Thu Apr 13 09:33:40 IST 2017
v1.0.personalovertimelimitbean.apimodel.description=PersonalOvertimeLimit identifies personal overtime values that are assigned to a personal overtime rule.
v1.0.personalovertimelimitbean.apimodelproperty.personalovertimeamounttypename.description=The type that is associated with amounts.
v1.0.personalovertimelimitbean.apimodelproperty.amount.description=The amount of time before the overtime begins to accrue. This overrides the amount in the limit rule.
v1.0.personalovertimelimitbean.apimodelproperty.minimumamount.description=The amount of overtime time that must be worked before the overtime rate is applied.
