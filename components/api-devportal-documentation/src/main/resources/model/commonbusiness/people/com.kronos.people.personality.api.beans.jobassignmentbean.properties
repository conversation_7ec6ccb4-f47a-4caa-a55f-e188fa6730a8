#Comments for Properties
#Thu Apr 13 09:33:40 IST 2017
v1.0.jobassignmentbean.apimodel.description=JobAssignment specifies information about a person's job assignment. It is used to import data.
v1.0.jobassignmentbean.apimodelproperty.basewagerates.description=One or more effective-dated base wage rates.
v1.0.jobassignmentbean.apimodelproperty.haspersonalovertimeflag.description=A Boolean indicator of whether or not personal overtime assignments are associated with a person.
v1.0.jobassignmentbean.apimodelproperty.jobassignmentdetails.description=General job, employee, and scheduling information that is associated with a job assignment.
v1.0.jobassignmentbean.apimodelproperty.primarylaboraccounts.description=One or more primary labor accounts.
v1.0.jobassignmentbean.apimodelproperty.employmenttermassignments.description=Contains one or more effective-dated employmenttermassignment elements to be created, updated or deleted for a person.
v1.0.jobassignmentbean.apimodelproperty.personalovertimeassignments.description=One or more rules that override the person's assigned overtime rule; that is, specify one or more overriding personal rules.
v1.0.jobassignmentbean.apimodelproperty.usemultipleassignmentsflag.description=A Boolean indicator of whether or not to use multiple assignments.
v1.0.jobassignmentbean.apimodelproperty.schedulegroupname.description=The name of the schedule group to which the person is currently assigned.
v1.0.jobassignmentbean.apimodelproperty.predictiveschedulingeligibilitylist.description=The predictive scheduling eligibility records associated with a person.
v1.0.jobassignmentbean.apimodelproperty.predictiveschedulingoverridelist.description=The predictive scheduling override records associated with a person.
v1.0.jobassignmentbean.apimodelproperty.schedulegroupassignments.description=Contains one or more effective-dated schedulegroupassignment elements to be created, updated, or deleted for a person.
v1.0.jobassignmentbean.apimodelproperty.schedulegroupassignmentspan.description=The date range over which existing schedule group assignments will be affected in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.jobassignmentbean.apimodelproperty.employmenttermassignmentspan.description=The date range over which existing employment term assignments will be affected in ISO_LOCAL_DATE format (YYYY-MM-DD).