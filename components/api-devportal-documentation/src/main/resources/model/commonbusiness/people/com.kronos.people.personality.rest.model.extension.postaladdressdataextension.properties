#Comments for Properties
#Thu Apr 13 09:33:40 IST 2017
v1.0.postaladdressdataextension.apimodel.description=Postal address details.
v1.0.postaladdressdataextension.apimodelproperty.city.description=The city associated with a postal address.
v1.0.postaladdressdataextension.apimodelproperty.contacttype.description=The contact type associated with a postal address.
v1.0.postaladdressdataextension.apimodelproperty.contacttypeid.description=The ID of a contact type associated with a postal address.
v1.0.postaladdressdataextension.apimodelproperty.country.description=The country associated with a postal address.
v1.0.postaladdressdataextension.apimodelproperty.state.description=The state associated with a postal address.
v1.0.postaladdressdataextension.apimodelproperty.street.description=The street associated with a postal address.
v1.0.postaladdressdataextension.apimodelproperty.zipcode.description=The zip code associated with a postal address.
