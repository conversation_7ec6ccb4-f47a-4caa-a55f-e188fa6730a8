#Comments for Properties
#Thu Apr 13 09:33:40 IST 2017
v1.0.primarylaboraccountbean.apimodel.description=PrimaryLaborAccount identifies the labor account and primary organization job for a person's job assignment within a range of dates. If organizational maps are in use, the primary account is comprised of a primary job and a primary labor account. For those whose organizations do not use jobs, the primary account is comprised solely of a primary labor account.
v1.0.primarylaboraccountbean.apimodelproperty.effectivedate.description=The date when the primary labor account becomes effective.
v1.0.primarylaboraccountbean.apimodelproperty.expirationdate.description=The date when the primary labor account expires.
v1.0.primarylaboraccountbean.apimodelproperty.organizationpath.description=The primary job information.
v1.0.primarylaboraccountbean.apimodelproperty.laborcategoryname.description=The labor category name.
v1.0.primarylaboraccountbean.apimodelproperty.orgpathbycriteria.description=The business structure path (org path) assignment criteria.