v1.0.attestationprofileassignment.apimodel.description=<code>AttestationProfileAssignment</code> defines an Attestation profile assignment.
v1.0.attestationprofileassignment.apimodelproperty.profile.description=A reference to an Attestation profile object.
v1.0.attestationprofileassignment.apimodelproperty.effectiveDate.description=The effective date of an assignment in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.attestationprofileassignment.apimodelproperty.expirationDate.description=The expiration date of an assignment in ISO_LOCAL_DATE format (YYYY-MM-DD).

v1.0.attestationquestiondto.apimodel.description=The Attestation question model.
v1.0.attestationquestiondto.apimodelproperty.displaytype=The display type of an Attestation question.
v1.0.attestationquestiondto.apimodelproperty.answers.list=An array of answers associated with an Attestation question.
v1.0.attestationquestiondto.apimodelproperty.label=The label of an Attestation question.
v1.0.attestationquestiondto.apimodelproperty.modelVariable=The model variable of an Attestation question.
v1.0.attestationquestiondto.apimodelproperty.answers.assigned.list=An array of answers associated with an Attestation question.

