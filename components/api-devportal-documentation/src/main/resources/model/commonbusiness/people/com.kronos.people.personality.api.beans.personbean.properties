#Comments for Properties
#Thu Apr 13 09:33:40 IST 2017
v1.0.personbean.apimodel.description=The person model, which defines name and employment information for an employee.
v1.0.personbean.apimodelproperty.personnumber.description=A number that uniquely identifies a person. This is not an employee ID. This property is used to set, change, or display the person number assignment.
v1.0.personbean.apimodelproperty.firstname.description=The person's first name.
v1.0.personbean.apimodelproperty.lastname.description=The person's last name. A value for this column is required when adding a new person.
v1.0.personbean.apimodelproperty.middleinitial.description=The person's middle initial.
v1.0.personbean.apimodelproperty.fullname.description=The name of the person. This name is usually listed in the browser.
v1.0.personbean.apimodelproperty.shortname.description=The name by which the person wants to be known.
v1.0.personbean.apimodelproperty.romanizedfullname.description=The romanized version of the person's full name.
v1.0.personbean.apimodelproperty.phoneticfullname.description=The phonetic version of the person's full name.
v1.0.personbean.apimodelproperty.birthdate.description=The person's date of birth.
v1.0.personbean.apimodelproperty.hiredate.description=The date on which the person was hired.
v1.0.personbean.apimodelproperty.accrualprofilename.description=The accrual profile to be used for a person or group. An accrual profile can contain one or more accrual rules. Accrual profiles are assigned to similar groups of employees. For example, all full-time employees might have the same accrual profile. The accrual profile assignment to an employee is effective dated based on the optional properties <code>accrualprofileeffectivedate</code> and <code>accrualprofileenddate</code>. There can be only one accrual profile assigned to an employee on any given day. Assigning a different accrual profile over existing assignment(s) inserts the new assignment over the old assignment(s).
v1.0.personbean.apimodelproperty.accrualprofileeffectivedate.description=Specifies the date (inclusive) when an accrual profile assignment is effective. If <code>accrualprofileeffectivedate</code> is not supplied (element or value), the effective date is effective from beginning of time.
v1.0.personbean.apimodelproperty.accrualprofileenddate.description=Specifies date (inclusive) when an accrual profile assignment ends. If <code>accrualprofileenddate</code> is not supplied (tag or value), the end date extends to forever.
v1.0.personbean.apimodelproperty.fulltimepercentage.description=Sets the full-time equivalency for a part-time employee, defining the equivalency as the percentage of full-time hours that the employee is scheduled to work. The full-time equivalency is used to determine the rate at which benefits accrue for an employee who is not scheduled to work a full standard work week. You can alternatively specify the employee's full-time equivalency as a ratio of scheduled hours to standard hours. To do this, use the <code>employeestandardhours</code> and <code>fulltimestandardhours</code> properties. If you enter a value for <code>employeestandardhours</code> and <code>fulltimestandardhours</code>, do not enter a value for <code>fulltimepercentage</code>.
v1.0.personbean.apimodelproperty.fulltimestandardhours.description=The <code>employeestandardhours</code> and <code>fulltimestandardhours</code> properties define the full-time equivalency for a part-time employee, defining the equivalency as a ratio of the employee's scheduled hours to the hours in a standard work week. Full-time equivalency is used to determine the rate at which benefits accrue for an employee who is not scheduled to work a full standard work week. You can alternatively specify the employee's full-time equivalency as the percentage of full-time hours that the employee is scheduled to work. To do this, use the <code>fulltimepercentage</code> property. If you enter a value for <code>fulltimepercentage</code>, do not enter a value for <code>employeestandardhours</code> or <code>fulltimestandardhours</code>.
v1.0.personbean.apimodelproperty.employeestandardhours.description=The <code>employeestandardhours</code> and <code>fulltimestandardhours</code> properties define the full-time equivalency for a part-time employee, defining the equivalency as a ratio of the employee's scheduled hours to the hours in a standard work week. Full-time equivalency is used to determine the rate at which benefits accrue for an employee who is not scheduled to work a full standard work week. You can alternatively specify the employee's full-time equivalency as the percentage of full-time hours that the employee is scheduled to work. To do this, use the <code>fulltimepercentage</code> property. If you enter a value for <code>fulltimepercentage</code>, do not enter a value for <code>employeestandardhours</code> or <code>fulltimestandardhours</code>.
v1.0.personbean.apimodelproperty.managersignoffthrudatetime.description=The date of the last manager signoff.
v1.0.personbean.apimodelproperty.payrolllockoutthrudatetime.description=The date of the last payroll lockout.
v1.0.personbean.apimodelproperty.signoffpreparationdatetime.description=Sign-off preparation date if it is enabled.
v1.0.personbean.apimodelproperty.fingerrequiredflag.description=A Boolean indicator of whether or not a finger scan (biometric) entry is required for the person.
v1.0.personbean.apimodelproperty.haskmailnotificationdelivery.description=If a generic notification is sent to the associated person, the person receives this notification in his or her inbox. This property is optional. If it has not been specified, the current value of this person's <code>haskmailnotificationdelivery</code> remains unchanged.
v1.0.personbean.apimodelproperty.globalid.description=An ID that uniquely identifies a person. This is not an employee ID. This property is used to set, change, or display the person Global Id assignment.
v1.0.personbean.apimodelproperty.fulltimeequivalencies.description=The full time equivalencies.
v1.0.personbean.apimodelproperty.facerequiredflag.description=A Boolean indicator of whether or not a face scan is required.
v1.0.personbean.apimodelproperty.employmentlink.description=The employment link associated with a person record.
v1.0.personbean.apimodelproperty.employmentname.description=The employment name associated with a person record.