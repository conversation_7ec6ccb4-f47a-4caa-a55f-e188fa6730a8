#Comments for Properties
v1.0.punchInterpretationRule.apimodel.description=Model describing punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.id.description=The id of the punch interpretation rule that is alloted to the employee through payrule or employment term.
v1.0.punchInterpretationRule.apimodelproperty.name.description=The name of the punch interpretation rule that is alloted to the employee through payrule or employment term.
v1.0.punchInterpretationRule.apimodelproperty.punchRestrictions.description=A string value punchRestrictions which lets what kind of resriction is imposed on the user as of now full or simple.
v1.0.punchInterpretationRule.apimodelproperty.allowOverrides.description=A Boolean indicator of whether or not to allow overrides.
v1.0.punchInterpretationRule.apimodelproperty.scheduleEarlyStartMargin.description=The early start schedule margin time of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.scheduleEarlyStartRestriction.description=The early start schedule restriction time of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.scheduleLateStartRestriction.description=The late start schedule restriction time of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.scheduleLateStartMargin.description=The late start schedule margin time of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.missedInEndMargin.description=The missed end schedule margin time of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.maximumOut.description=The maximum out time of the the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.minimumMeal.description=The minimum meal time of the the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.enforceBreaks.description=A Boolean indicator of whether to enforce breaks or not.
v1.0.punchInterpretationRule.apimodelproperty.earlyStartBreakMargin.description=The early start break margin time of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.lateStartBreakMargin.description=The late start break margin time of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.lateEndBreakMargin.description=The late end break margin time of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.scheduleBeginEarlyEndRestriction.description=The early begin schedule end restriction time of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.scheduleLiftEarlyEndRestriction.description=The early lift schedule end restriction time of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.scheduleLateEndRestriction.description=The late schedule end restriction time of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.maximumShiftLength.description=The missed out time limit of the employee with punch interpretation rule.
v1.0.punchInterpretationRule.apimodelproperty.unscheduledIn.description=A Boolean indicator of whether or not to Restrict unscheduled in-punches.
v1.0.punchInterpretationRule.apimodelproperty.unscheduledShiftLength.description=The unscheduled shift length time of the employee with punch interpretation rule.