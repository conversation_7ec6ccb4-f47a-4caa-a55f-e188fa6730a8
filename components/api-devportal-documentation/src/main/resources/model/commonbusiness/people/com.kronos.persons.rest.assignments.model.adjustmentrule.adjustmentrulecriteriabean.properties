#Comments for Properties
#Sunday Sep 03 2017
v1.0.adjustmentrulecriteriabean.apimodel.value=AdjustmentRuleCriteria
v1.0.adjustmentrulecriteriabean.apimodel.description=Request criteria for employee adjustment rule.
v1.0.adjustmentrulecriteriabean.apimodelproperty.effectivedate.description=The first date that an Adjustment Rule applies to an employee.
v1.0.adjustmentrulecriteriabean.apimodelproperty.personidentity.description=The <code>PersonIdentity</code> of the employee, associated with an Adjustment Rule.
v1.0.adjustmentrulecriteriabean.apimodelproperty.processor.description=The name of an Adjustment Rule to associate with an employee.