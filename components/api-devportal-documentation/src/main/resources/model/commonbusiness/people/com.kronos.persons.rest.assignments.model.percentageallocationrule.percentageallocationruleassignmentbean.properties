v1.0.percentageallocationruleassignmentbean.apimodel.value=Employee Percentage Allocation Rule Assignment
v1.0.percentageallocationruleassignmentbean.apimodel.description=Assign Percentage Allocation Rules defined on a system to an employee. This assignment is effective dated.
v1.0.percentageallocationruleassignmentbean.apimodelproperty.effectivedate.description=The first date that a percentage allocation rule assignment applies to an employee in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.percentageallocationruleassignmentbean.apimodelproperty.expirationdate.description=The last date a percentage allocation rule assignment applies to an employee in ISO_LOCAL_DATE format (YYYY-MM-DD). To establish an expiration date which occurs before 1/1/3000 for an percentage allocation rule assignment, specify a a new percentage allocation rule (which optionally can contain no triggers if you want to end all allocations) and assign it with an effective date equal to the day you wish the first percentage allocation rule to terminate.
v1.0.percentageallocationruleassignmentbean.apimodelproperty.originaleffectivedate.description=The original effective date of the association of an extension processor and an employee in ISO_LOCAL_DATE format (YYYY-MM-DD). This property must be used with the "Update" action.
v1.0.percentageallocationruleassignmentbean.apimodelproperty.personidentity.description=The <code>PersonIdentity</code> of the employee to associate with a percentage allocation rule.
v1.0.percentageallocationruleassignmentbean.apimodelproperty.processor.description=The name of a percentage allocation rule to associate with an employee.
