#Comments for Properties
#Thu Apr 13 09:33:40 IST 2017
v1.0.employeedataextension.apimodel.description=Employee extension details.
v1.0.employeedataextension.apimodelproperty.accessprofile.description=The access profile associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.accessprofileid.description=The access profile ID associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.accountlocked.description=An account locked status associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.authenticationtype.description=The authentication type associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.birthdate.description=The birth date associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.consecutivebadlogons.description=The consecutive bad logons status associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.delegateprofile.description=The delegate profile associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.delegateprofileid.description=The delegate profile ID associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.effdatedaccountstatus.description=The effective-dated account status entry associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.effdateddataaccessentries.description=The effective-dated data access extension associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.effdatedemploymentstatus.description=The effective-dated employment status associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.effdatedprimaryjobaccountentries.description=The effective-dated primary job accounts associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.emailcontactdataentries.description=The email contacts associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.fingerrequiredflag.description=A Boolean indicator of whether or not a finger scan is required.
v1.0.employeedataextension.apimodelproperty.firstname.description=The first name associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.fullname.description=The full name associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.haskmailnotificationdelivery.description=A Boolean indicator of whether or not an employee has email notification delivery.
v1.0.employeedataextension.apimodelproperty.hiredate.description=The hire date associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.isemployee.description=A Boolean indicator of whether or not an employee is classified as an employee.
v1.0.employeedataextension.apimodelproperty.ismanager.description=A Boolean indicator of whether or not an employee is classified as a manager.
v1.0.employeedataextension.apimodelproperty.isrestricteduser.description=A Boolean indicator of whether or not an employee is classified as a restricted user.
v1.0.employeedataextension.apimodelproperty.jobassigmentid.description=The job assigment ID associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.lastname.description=The last name associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.learnpath.description=The learn path associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.licensetypelist.description=The license types associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.localeprofile.description=The locale profile associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.localeprofileid.description=The locale profile associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.lockoutresetdatetime.description=The lockout reset date time associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.logonprofile.description=The logon profile associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.logonprofileid.description=The logon profile associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.mfarequired.description=<p>A Boolean indicator of whether or not Multi-Factor Authentication (MFA) is required by an employee extension. <strong>Note:</strong> This Boolean is read-only. Multi-Factor Authentication (MFA) is disabled regardless of the value passed in this property, as MFA for basic authentication users has been removed.</p>
v1.0.employeedataextension.apimodelproperty.middlename.description=The middle name associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.notificationprofile.description=The notification profile associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.notificationprofileid.description=The notification profile ID associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.password.description=The password associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.passwordhistories.description=The password history associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.passwordupdateddatetime.description=The date and time of the last update to a password associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.passwordupdaterequired.description=A Boolean indicator of whether or not an updated password is required.
v1.0.employeedataextension.apimodelproperty.personcustomdataentries.description=The person custom data associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.persondatesentries.description=The person dates associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.phoneticfullname.description=The phonetic full name associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.postaladdressdataentries.description=The postal addresses associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.preferenceprofiledataentry.description=The preference profile data associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.preferenceprofile.description=The preference profile associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.preferenceprofileid.description=The preference profile ID associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.processemployeeprofile.description=The process employee profile associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.processmanagerprofile.description=The process manager profile associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.recordmanagerlockout.description=The record manager lockout associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.recordmanagerlockoutflag.description=A flag indicating whether or not a record manager lockout exists.
v1.0.employeedataextension.apimodelproperty.requirepasschange.description=A Boolean indicator of whether or not to require a password change.
v1.0.employeedataextension.apimodelproperty.romanizedfullname.description=The romanized full name associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.senioritydate.description=The seniority date associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.shortname.description=The short name associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.supervisorfullname.description=The supervisor full name associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.supervisorpersonid.description=The supervisor person ID associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.supervisorpersonnumber.description=The supervisor person number associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.telcontactdataentries.description=The telephone contacts associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.timezone.description=The time zone associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.timezoneid.description=The time zone ID associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.timezoneName.description=The time zone name associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.username.description=The user name associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.useraccountid.description=The user account ID associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.clockOnlyUser.description=A Boolean indicator of whether or not the user is a clock-only user.
v1.0.employeedataextension.apimodelproperty.effdatedemploymentanalyticslabortype.description=The effective-dated analytics labor type associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.approvalmethod.description=The timecard approval method associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.approvalmethodid.description=The timecard approval method ID associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.positions.description=An array of positions.
v1.0.employeedataextension.apimodelproperty.facerequiredflag.description=A Boolean indicator of whether or not a face scan is required.
v1.0.employeedataextension.apimodelproperty.accessmethodprofileid.description=The ID of an access method profile.
v1.0.employeedataextension.apimodelproperty.accessmethodprofiledisplayname.description=The display name of an access method profile.
v1.0.employeedataextension.apimodelproperty.accessmethodprofile.description=The access method profile associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.externalIdentifiers.description=The external identifiers associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.identityid.description=The identity ID.
v1.0.employeedataextension.apimodelproperty.reportstopersonnumber.description=The person number of the reporting manager.
v1.0.employeedataextension.apimodelproperty.employmentlink.description=A link representing the employment associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.employmentname.description=The name of an employment associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.hrpositioncodes.description=A reference to the position codes associated with an employee extension.
v1.0.employeedataextension.apimodelproperty.ssousername.description=The single sign-on user name associated with an employee extension.