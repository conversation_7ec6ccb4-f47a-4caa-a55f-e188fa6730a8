v1.0.schedulegroupbean.apimodel.description=Schedule Group Assignment model.
v1.0.schedulegroupbean.apimodelproperty.name.description=The name of the schedule group to assign to a person.
v1.0.schedulegroupbean.apimodelproperty.startdate.description=The date on which a schedule group assignment begins in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.schedulegroupbean.apimodelproperty.enddate.description=The date on which an schedule group assignment ends in ISO_LOCAL_DATE format (YYYY-MM-DD). The assignment is active on the date specified in this property and expires at the end of the day.
v1.0.schedulegroupbean.apimodelproperty.originalstartdate.description=The start date of an existing schedule group assignment to edit or delete in ISO_LOCAL_DATE format (YYYY-MM-DD). If the name is given as `null` string, the target assignment is deleted; otherwise, it is updated. For update, the assignment is subject to the same validation rules as when it was first created.
v1.0.schedulegroupbean.apimodelproperty.removefromothergroups.description=A Boolean indicator of whether or not to remove schedule group assignments within an assignment span.