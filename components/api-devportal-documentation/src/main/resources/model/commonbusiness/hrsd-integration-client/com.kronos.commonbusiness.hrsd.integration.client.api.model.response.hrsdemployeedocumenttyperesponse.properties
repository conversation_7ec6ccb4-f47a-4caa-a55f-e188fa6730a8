v1.0.hrsdemployeedocumenttyperesponse.apimodel.description=Entity containing necessary data to describe HRSD document type
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.label.description=Label of the Document Type
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.localizedlabels.description=Label of the Document Type in the localized version
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.folderid.description=ID of the Folder under which the Documents of this Document Type will be filed
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.filetyperestrictions.description=Restrict the upload of documents based on the file extension (comma separated)
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.updatedocumentsonemployeetransfer.description=When the Employee transfers to a different organization, describe how their documents should be updated
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.polices.description=List of the expiry policy IDs that are linked to the document type
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.employeeaccesspermissions.description=Define the Employee access to the documents if the Employee has access to their Employee Folder. Can be one of: preview, dl_original, dl_duplicate, upload
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.useraccesspermissions.description=Permissions on the document type for the specific user
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.metadata.description=Entity containing metadata information of this document
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.templates.description=List of Template IDs linked to the Document Type separated by a , . A template can then be used to generate a document
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.synchronizedcustomfields.description=List of custom fields codes used for document access restriction that should be updated to match any changes in the employee profile
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.id.description=Document type ID
v1.0.hrsdemployeedocumenttyperesponse.apimodelproperty.uploadmessage.description=Message passed by user when uploading