v1.0.hrsdcreatedocumentresponse.apimodel.description=Entity containing necessary data to describe new uploaded HRSD document
v1.0.hrsdcreatedocumentresponse.apimodelproperty.employeeid.description=ID of the Employee for whom the Document is uploaded.
v1.0.hrsdcreatedocumentresponse.apimodelproperty.employeeexternalid.description=Person number of the Employee for whom the Document is uploaded
v1.0.hrsdcreatedocumentresponse.apimodelproperty.documenttypeid.description=Document Type ID, the document belongs to
v1.0.hrsdcreatedocumentresponse.apimodelproperty.title.description=Title of the Document
v1.0.hrsdcreatedocumentresponse.apimodelproperty.date.description=Date explicitly passed at the Document upload via the API. If available, it will be used instead of the creation date to sort the Document
v1.0.hrsdcreatedocumentresponse.apimodelproperty.organizationids.description=Organization IDs, inherited from the employee, the Document is attached to
v1.0.hrsdcreatedocumentresponse.apimodelproperty.metadata.description=Entity containing metadata information of this document
v1.0.hrsdcreatedocumentresponse.apimodelproperty.externalreference.description=External document reference to help identify the Document in external tools or filing systems
v1.0.hrsdcreatedocumentresponse.apimodelproperty.id.description=ID of the Document
v1.0.hrsdcreatedocumentresponse.apimodelproperty.origin.description=Origin of the Document
v1.0.hrsdcreatedocumentresponse.apimodelproperty.origindetails.description=Depending on the origin of the document and when suitable, provides more details on its origin
v1.0.hrsdcreatedocumentresponse.apimodelproperty.senderid.description=ID of the user or employee who uploaded the document. Empty string if the user does not have an ID
v1.0.hrsdcreatedocumentresponse.apimodelproperty.name.description=Name of the Document
v1.0.hrsdcreatedocumentresponse.apimodelproperty.trashed.description=Boolean parameter which defines whether the document has been trashed or not
v1.0.hrsdcreatedocumentresponse.apimodelproperty.expired.description=Boolean parameter which defines whether the document is expired or not
v1.0.hrsdcreatedocumentresponse.apimodelproperty.expirydate.description=Expiry Date after when the Document will be marked as expired
v1.0.hrsdcreatedocumentresponse.apimodelproperty.createdat.description=The date document was created
v1.0.hrsdcreatedocumentresponse.apimodelproperty.updatedat.description=The date last time document was updated
v1.0.hrsdcreatedocumentresponse.apimodelproperty.code.description=Custom Field ID
v1.0.hrsdcreatedocumentresponse.apimodelproperty.value.description=Custom Field value
v1.0.hrsdcreatedocumentresponse.apimodelproperty.label.description=Label of the custom field value
v1.0.hrsdcreatedocumentresponse.apimodelproperty.previewuri.description=The url to redirect to HRSD employee folder for preview