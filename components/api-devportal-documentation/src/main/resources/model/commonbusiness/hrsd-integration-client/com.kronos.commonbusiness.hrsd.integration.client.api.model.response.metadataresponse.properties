v1.0.metadataresponse.apimodel.description=Entity containing necessary data to describe document metadata
v1.0.metadataresponse.apimodelproperty.code.description=Unique friendly ID of the Meta Data
v1.0.metadataresponse.apimodelproperty.type.description=Type of meta data: int, date, text, list
v1.0.metadataresponse.apimodelproperty.authorizedvalues.description=If the Meta Data type is list, the list of authorized values separated by a comma (,)
v1.0.metadataresponse.apimodelproperty.label.description=The label of the Meta Data
v1.0.metadataresponse.apimodelproperty.localizedlabel.description=The label of the Meta Data in localized version
v1.0.metadataresponse.apimodelproperty.isrequired.description=Boolean parameter defining whether is the Meta Data required or not when uploading