#Comments for Properties
#Monday Sept 06 2017
v1.0.positionorderbean.apimodel.description=The position order model.
v1.0.positionorderbean.apimodelproperty.effectivedate.description=The effective date of a position in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.positionorderbean.apimodelproperty.expirationdate.description=The expiration date of a position in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.positionorderbean.apimodelproperty.order.description=The numerical order of a position expressed as an integer.
v1.0.positionschedulegroupbean.apimodelproperty.originalstartdate.description=The original start date in ISO_LOCAL_DATE format (YYYY-MM-DD).

v1.0.positioncustomdatabean.apimodel.description=The position custom data model.
v1.0.positioncustomdatabean.apimodelproperty.customdatatypename.description=The name of a custom data type associated with a position.
v1.0.positioncustomdatabean.apimodelproperty.text.description=The custom data text associated with a position.

v1.0.positiondetailsbean.apimodel.description=The position details model.
v1.0.positiondetailsbean.apimodelproperty.supervisorname.description=The name of a position's supervisor.
v1.0.positiondetailsbean.apimodelproperty.seniorityrankdate.description=The seniority rank date in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.positiondetailsbean.apimodelproperty.hiredate.description=The hire date of a position in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.positiondetailsbean.apimodelproperty.supervisorpersonnumber.description=The person number of a position's supervisor. This unique ID is not the same as an employee ID.
v1.0.positiondetailsbean.apimodelproperty.professionalShiftCodeName.description=The name of the shift template profile that is assigned to a position.
v1.0.positiondetailsbean.apimodelproperty.cascadingProfileName.description=The name of the cascading profile that is assigned to a position.
v1.0.positiondetailsbean.apimodelproperty.positionfulltimepercentage.description=Sets the full-time equivalency for a part-time employee in a position, defining the equivalency as the percentage of full-time hours that the employee is scheduled to work. The full-time equivalency is used to determine the rate at which benefits accrue for an employee who is not scheduled to work a full standard work week. You can alternatively specify the employee's full-time equivalency as a ratio of scheduled hours to standard hours. To do this, use the <code>positionemployeestandardhours</code> and <code>positionfulltimestandardhours</code> properties. If you enter a value for <code>positionemployeestandardhours</code> and <code>positionfulltimestandardhours</code>, do not enter a value for <code>positionfulltimepercentage</code>.
v1.0.positiondetailsbean.apimodelproperty.positionfulltimestandardhours.description=The <code>positionemployeestandardhours</code> and <code>positionfulltimestandardhours</code> properties define the full-time equivalency for a part-time employee in a position, defining the equivalency as a ratio of the employee's scheduled hours to the hours in a standard work week. Full-time equivalency is used to determine the rate at which benefits accrue for an employee who is not scheduled to work a full standard work week. You can alternatively specify the employee's full-time equivalency as the percentage of full-time hours that the employee is scheduled to work. To do this, use the <code>positionfulltimepercentage</code> property. If you enter a value for <code>positionfulltimepercentage</code>, do not enter a value for <code>positionemployeestandardhours</code> or <code>positionfulltimestandardhours</code>.
v1.0.positiondetailsbean.apimodelproperty.positionemployeestandardhours.description=The <code>positionemployeestandardhours</code> and <code>positionfulltimestandardhours</code> properties define the full-time equivalency for a part-time employee in a position, defining the equivalency as a ratio of the employee's scheduled hours to the hours in a standard work week. Full-time equivalency is used to determine the rate at which benefits accrue for an employee who is not scheduled to work a full standard work week. You can alternatively specify the employee's full-time equivalency as the percentage of full-time hours that the employee is scheduled to work. To do this, use the <code>positionfulltimepercentage</code> property. If you enter a value for <code>positionfulltimepercentage</code>, do not enter a value for <code>positionemployeestandardhours</code> or <code>positionfulltimestandardhours</code>.
v1.0.positiondetailsbean.apimodelproperty.accrualprofilename.description=The name of the accrual profile.
v1.0.positiondetailsbean.apimodelproperty.accrualprofileeffectivedate.description=The effective date of the accrual profile in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.positiondetailsbean.apimodelproperty.accrualprofileenddate.description=The end date of the accrual profile in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.predictiveschedulingoverridebean.apimodelproperty.expirationdate.description=The expiration date in ISO_LOCAL_DATE format (YYYY-MM-DD).