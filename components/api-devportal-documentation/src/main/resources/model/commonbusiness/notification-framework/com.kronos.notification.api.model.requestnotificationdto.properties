# #Comments for Properties
# #Thu Apr 20 14:11:46 IST 2017
# v1.0.requestnotificationdto.apimodel.description=Model context for request notification objects.
# v1.0.requestnotificationdto.apimodelproperty.approvalstatus.description=The approval status of a request notification object.
# v1.0.requestnotificationdto.apimodelproperty.approvername.description=The approver name of a request notification object.
# v1.0.requestnotificationdto.apimodelproperty.durationdays.description=The duration in days of a request notification object.
# v1.0.requestnotificationdto.apimodelproperty.durationhours.description=The duration in hours of a request notification object.
# v1.0.requestnotificationdto.apimodelproperty.enddate.description=The end date of a request notification object.
# v1.0.requestnotificationdto.apimodelproperty.endtime.description=The end time of a request notification object.
# v1.0.requestnotificationdto.apimodelproperty.requestid.description=The request ID of a request notification object.
# v1.0.requestnotificationdto.apimodelproperty.changedatapproval.description=A Boolean indicator of whether or not a request notification was changed at approval.
# v1.0.requestnotificationdto.apimodelproperty.requestedperiods.description=The requested periods of a request notification object.
# v1.0.requestnotificationdto.apimodelproperty.startdate.description=The start date of a request notification object.
# v1.0.requestnotificationdto.apimodelproperty.starttime.description=The start time of a request notification object.
# v1.0.requestnotificationdto.apimodelproperty.submissiondate.description=The submission date of a request notification in ISO_LOCAL_DATE format (YYYY-MM-DD).
# v1.0.requestnotificationdto.apimodelproperty.subtype.description=The request subtype of a request notification object.
# v1.0.requestnotificationdto.apimodelproperty.requestername.description=The requester name of a request notification object.
