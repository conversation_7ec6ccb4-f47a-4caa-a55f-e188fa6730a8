#Comments for Properties
#Wed Feb 19 14:11:46 IST 2025
v1.0.notificationprofilepriority.apimodel.description=The Notification Profile Priority model.
v1.0.notificationprofilepriority.apimodelproperty.id.description=The unique identifier for a notification profile priority.
v1.0.notificationprofilepriority.apimodelproperty.name.description=This parameter contains the name of notification profile priority.
v1.0.notificationprofilepriority.apimodelproperty.selecteditems.description=This is the list of destinations which are part of notification profile priority.

v1.0.notificationprofilebase.apimodel.description=The Notification Profile model.
v1.0.notificationprofilebase.apimodelproperty.id.description=The unique identifier for a notification profile.
v1.0.notificationprofilebase.apimodelproperty.name.description=This parameter contains the name of notification profile.
v1.0.notificationprofilebase.apimodelproperty.description.description=This parameter contains the description of notification profile.
v1.0.notificationprofilebase.apimodelproperty.notificationpriorities.description=This is the list of priorities which are part of notification profile.

v1.0.notificationprofilegetallresponse.apimodel.description=This is the model response for getAll api of notification profiles.
v1.0.notificationprofilegetallresponse.apimodelproperty.totalelements.description=This will contain number of notification profiles present in the system.
v1.0.notificationprofilegetallresponse.apimodelproperty.records.description=This is the list of all notication profiles.

v1.0.notificationprofilemultireadrequest.apimodel.description=Represents a request DTO for retrieving multiple notification profiles based on specific criteria.
v1.0.notificationprofilemultireadrequest.apimodelproperty.where.description=Specifies the conditions used to filter the notification profiles in the request.

v1.0.notificationprofilewhere.apimodel.description=Defines the filtering criteria for retrieving notification profiles.
v1.0.notificationprofilewhere.apimodelproperty.notifications.description=Specifies the list of notification references to be used for filtering.
v1.0.notificationprofilewhere.apimodelproperty.alldetails.description=Indicates whether to retrieve detailed information for the notification profiles.

v1.0.notificationprofileavailableresponse.apimodel.description=Represents the response DTO containing details of an available notification profile.
v1.0.notificationprofileavailableresponse.apimodelproperty.id.description=The unique identifier of the notification profile.
v1.0.notificationprofileavailableresponse.apimodelproperty.name.description=The name of the notification profile.
v1.0.notificationprofileavailableresponse.apimodelproperty.description.description=A brief description of the notification profile.
v1.0.notificationprofileavailableresponse.apimodelproperty.notificationpriorities.description=A list of notification priorities associated with the notification profile.

v1.0.notificationprofileavailablepriority.apimodel.description=Represents the priority structure associated with a notification profile.
v1.0.notificationprofileavailablepriority.apimodelproperty.id.description=The unique identifier of the notification priority.
v1.0.notificationprofileavailablepriority.apimodelproperty.name.description=The name of the notification priority.
v1.0.notificationprofileavailablepriority.apimodelproperty.availableitems.description=A list of available items associated with the notification priority.

