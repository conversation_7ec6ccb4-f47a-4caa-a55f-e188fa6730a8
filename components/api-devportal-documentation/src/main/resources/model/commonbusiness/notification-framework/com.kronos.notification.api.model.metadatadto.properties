#Comments for Properties
#Thu May 2 14:11:46 IST 2022
v2.0.metadatadto.apimodel.description=The object describing metadata.
v2.0.metadatadto.apimodelproperty.records.description=The count of records.
v2.0.metadatadto.apimodelproperty.recordsOnPage.description=The count of records on each page.
v2.0.metadatadto.apimodelproperty.totalPages.description=The total number of pages.
v2.0.metadatadto.apimodelproperty.page.description=The page number within the data set that the caller wishes to fetch.
v2.0.metadatadto.apimodelproperty.startIndex.description=The starting sequence number for the query.
v2.0.metadatadto.apimodelproperty.requestTag.description=The pointer to the cached data set from the original query.



