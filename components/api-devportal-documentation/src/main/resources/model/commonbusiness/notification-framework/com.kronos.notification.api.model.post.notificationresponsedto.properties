#Comments for Properties
v1.0.notificationrequestdto.apimodel.description=The model for the parameters which are used to fetch all notifications.
v1.0.notificationrequestdto.apimodelproperty.hyperfind.description=The reference object for a Hyperfind by which to filter the results.
v1.0.notificationrequestdto.apimodelproperty.allEmployees.description=A Boolean indicator of whether or not to return all employees to which the currently logged-in user has access.
v1.0.notificationrequestdto.apimodelproperty.startDateTime.description=The startDateTime of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddTHH:MM:SS).
v1.0.notificationrequestdto.apimodelproperty.endDateTime.description=The endDateTime of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddTHH:MM:SS).
v1.0.notificationrequestdto.apimodelproperty.employees.description=The reference object for a employees by which to filter the results.
v1.0.notificationrequestdto.apimodelproperty.types.description=A comma-separated list of types by which to filter the results.
v1.0.notificationrequestdto.apimodelproperty.pageToken.description=A string that contains a token, which is a hash of the page to retrieve from cache. Cached results expire after 60 minutes.
v1.0.notificationframework.genericrefobject.apimodel.description=Model for generic object references.
v1.0.notificationframework.genericrefobject.apimodelproperty.id.description=The ID of the reference object.
v1.0.notificationframework.genericrefobject.apimodelproperty.qualifier.description=The qualifier of the reference object.
v1.0.employeenotificationrequestref.apimodel.description=Model for employee object references
v1.0.employeenotificationrequestref.apimodelproperty.ids.description=The list of person ID of a person, which is the same as employee ID and person key This is not the same as the person number.
v1.0.employeenotificationrequestref.apimodelproperty.qualifiers.description=The list of person number of a person. This is not the same as person ID, employee ID, or person key.
v1.0.employeenotificationrequestref.apimodelproperty.refs.description=A reference to a person id or person number.
v1.0.notificationresponsedto.apimodel.description= Model of response body.
v1.0.notificationresponsedto.apimodelproperty.metadata.description= The notifications meta data.
v1.0.notificationresponsedto.apimodelproperty.notifications.description= This object includes the list of notification. 
v1.0.notificationframeworkmetadata.apimodel.description=This object includes the metadata.
v1.0.notificationframeworkmetadata.apimodelproperty.page.description=The page number within the data set that the caller wishes to fetch.
v1.0.notificationframeworkmetadata.apimodelproperty.records.description=The count of records.
v1.0.notificationframeworkmetadata.apimodelproperty.recordsOnPage.description=The count of records on each page.
v1.0.notificationframeworkmetadata.apimodelproperty.totalPages.description=The total number of pages.
v1.0.notificationframeworkmetadata.apimodelproperty.currentReadToken.description=A token which identifies the current section of paginated results to return.
v1.0.notificationframeworkmetadata.apimodelproperty.nextReadToken.description=A token which identifies the next section of paginated results to return.
v1.0.notificationdto.notificationsmodel.apimodel.description=The notification model.
v1.0.notificationdto.notificationsmodel.apimodelproperty.employee.description=The notification framework employee model.

v1.0.notificationframeworkemployee.apimodel.description=The employee information model.
v1.0.notificationframeworkemployee.apimodelproperty.id.description=An employee ID, which uniquely identifies an employee. This is not a person number.
v1.0.notificationframeworkemployee.apimodelproperty.qualifier.description=A person number, which is a number that uniquely identifies a person. This is not an employee ID.