#Comments for Properties
#Thu Apr 20 14:11:46 IST 2017
v1.0.notificationdto.apimodel.description=The Notification model.
v1.0.notificationdto.apimodelproperty.uuid.description=The unique identifier for a notification message.
v1.0.notificationdto.apimodelproperty.type.description=The type of a notification message. This parameter contains the event type for generic notifications.
v1.0.notificationdto.apimodelproperty.typelabel.description=The localized type label of a notification message.
v1.0.notificationdto.apimodelproperty.eventtypeid.description=The reference ID of the event type associated with a notification message.
v1.0.notificationdto.apimodelproperty.subject.description=The localized subject of a notification message.
v1.0.notificationdto.apimodelproperty.body.description=The localized message body of a notification message.
v1.0.notificationdto.apimodelproperty.shortmessage.description=The localized SMS message body of a SMS notification.
v1.0.notificationdto.apimodelproperty.priority.description=The priority assigned to a notification message.
v1.0.notificationdto.apimodelproperty.creationdate.description=The date on which a notification message was generated.
v1.0.notificationdto.apimodelproperty.expirationdate.description=The date on which a notification message expires.
v1.0.notificationdto.apimodelproperty.urgent.description=A Boolean indicator of whether or not a notification message is urgent. The notification message is marked urgent if its priority is high.
v1.0.notificationdto.apimodelproperty.todo.description=A Boolean indicator of whether or not a notification message has an associated action item or items.
v1.0.notificationdto.apimodelproperty.reviewed.description=A Boolean indicator of whether or not a notification message has been acknowledged.
v1.0.notificationdto.apimodelproperty.employeeid.description=An ID that uniquely identifies an employee. This is not a person number.
v1.0.notificationdto.apimodelproperty.employeename.description=The name of the employee for whom a notification message was generated. It contains the name of the creator of the notification message if it was not generated for an employee.
v1.0.notificationdto.apimodelproperty.employeenumber.description=The person number of an employee which uniquely identifies a person. This is not an employee ID.
v1.0.notificationdto.apimodelproperty.employeephotoid.description=The reference ID of the photograph of the employee for whom a notification message was generated.
v1.0.notificationdto.apimodelproperty.employeejob.description=The job of the employee for whom a notification message was generated.
v1.0.notificationdto.apimodelproperty.employeelocation.description=The location of the employee for whom a notification message was generated.
v1.0.notificationdto.apimodelproperty.hideemployee.description=A Boolean indicator of whether or not to hide an employee from notifications.
v1.0.notificationdto.apimodelproperty.status.description=The status of the entity associated with a notification message at the time the notification was generated.
v1.0.notificationdto.apimodelproperty.creatorname.description=The name of the person generating a notification.
v1.0.notificationdto.apimodelproperty.itemid.description=The reference ID of the entity associated with the notification message. For example, the reference ID of the request associated with a request notification.
v1.0.notificationdto.apimodelproperty.category.description=The category name to which a notification message belongs. This parameter is associated with the category of its associated event type.
v1.0.notificationdto.apimodelproperty.iconclass.description=The name of the CSS class of the icon displayed on the Control Center.
v1.0.notificationdto.apimodelproperty.notificationname.description=The name of a generic notification.
v1.0.notificationdto.apimodelproperty.navigationurl.description=The URL of a destination page internal to the application linked from a notification.
v1.0.notificationdto.apimodelproperty.navigationkey.description=The name of a destination page linked from a notification.
v1.0.notificationdto.apimodelproperty.navigationtype.description=The navigation type of the destination page.
v1.0.notificationdto.apimodelproperty.externalurl.description=The URL of the destination page external to the application linked from a notification.
v1.0.notificationdto.apimodelproperty.externalid.description=The external ID of the destination page.
v1.0.notificationdto.apimodelproperty.labeltemplate1.description=The label of a custom field configured in the generic notification.
v1.0.notificationdto.apimodelproperty.labeltemplate2.description=The label of a custom field configured in the generic notification.
v1.0.notificationdto.apimodelproperty.labeltemplate3.description=The label of a custom field configured in the generic notification.
v1.0.notificationdto.apimodelproperty.labeltemplate4.description=The label of a custom field configured in the generic notification.
v1.0.notificationdto.apimodelproperty.labeltemplate5.description=The label of a custom field configured in the generic notification.
v1.0.notificationdto.apimodelproperty.valuetemplate1.description=The value of a custom field configured in the generic notification.
v1.0.notificationdto.apimodelproperty.valuetemplate2.description=The value of a custom field configured in the generic notification.
v1.0.notificationdto.apimodelproperty.valuetemplate3.description=The value of a custom field configured in the generic notification.
v1.0.notificationdto.apimodelproperty.valuetemplate4.description=The value of a custom field configured in the generic notification.
v1.0.notificationdto.apimodelproperty.valuetemplate5.description=The value of a custom field configured in the generic notification.
v1.0.notificationdto.apimodelproperty.isgenericnotification.description=A Boolean indicator of whether or not the notification is a generic notification.
v1.0.notificationdto.apimodelproperty.navigationcontext.description=The object containing the context passed to the destination page linked from a notification.

v1.0.notificationdto.apimodelproperty.notificationactions.description=The object containing a list of actions displayed with notifications.
v1.0.notificationdto.apimodelproperty.golddatatypeid.description=An Integer indicator of whether or not an event is a legacy event.

v2.0.notificationdto.apimodelproperty.receiverId.description=The id of the person that will receive the notification.
v2.0.notificationdto.apimodelproperty.receiverPersonNumber.description= The person number of the person that will receive the notification.
v2.0.notificationdto.apimodelproperty.sequenceNumber.description=The sequence number of the record in the WFM database.
v2.0.notificationdto.apimodelproperty.index.description=The item's position in the overall result set.
v2.0.notificationdto.apimodelproperty.createDateMS.description=the time at which the notification record was inserted into the WFD database in Unix Time format (milliseconds since 1/1/70 12:00am GMT).
