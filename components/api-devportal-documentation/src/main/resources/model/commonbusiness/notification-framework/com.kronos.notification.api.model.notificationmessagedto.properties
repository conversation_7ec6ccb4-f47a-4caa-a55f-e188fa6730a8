#Duplicate
#Comments for Properties
#Thu Apr 20 14:11:46 IST 2017
# v1.0.notificationmessagedto.apimodel.description=Notification data for sending notifications.
# v1.0.notificationmessagedto.apimodelproperty.recipients.description=The recipients of a notification.
# v1.0.notificationmessagedto.apimodelproperty.recipientsbypersonnumber.description=The list of person numbers of the recipients of a notification when the "Send To Employee" flag is passed as true in the generic notification configuration.
# v1.0.notificationmessagedto.apimodelproperty.additionalrecipients.description=The list of email addresses of the additional intended employees designated as recipients of the notification.
# v1.0.notificationmessagedto.apimodelproperty.recipientpersonnums.description=The list of person IDs of the recipients of a notification irrespective of whether or not the "Send To Employee" flag is passed in the generic notification configuration.
# v1.0.notificationmessagedto.apimodelproperty.notifydata.description=A data map used to populate the configured notification message. It contains the optional notification properties which controls the behavior of notifications along with event-specific properties which are used to replace tags in the generic notification subject and message body templates at runtime.
# v1.0.notificationmessagedto.apimodelproperty.urlparams.description=A map of URL parameter names and their corresponding values used to build the one-click navigation URL sent in email notifications.
