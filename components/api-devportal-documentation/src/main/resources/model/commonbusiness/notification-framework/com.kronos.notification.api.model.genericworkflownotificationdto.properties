#Comments for Properties
#Thu Apr 20 14:11:46 IST 2017
v1.0.genericworkflownotificationdto.apimodel.description=Generic Workflow Notification details.
v1.0.genericworkflownotificationdto.apimodelproperty.id.description=The ID of a generic notification.
v1.0.genericworkflownotificationdto.apimodelproperty.name.description=The name of a generic notification.
v1.0.genericworkflownotificationdto.apimodelproperty.description.description=The description of a generic notification.
v1.0.genericworkflownotificationdto.apimodelproperty.subjecttemplate.description=The subject template of a generic notification.
v1.0.genericworkflownotificationdto.apimodelproperty.messagetemplate.description=The message template of a generic notification.
v1.0.genericworkflownotificationdto.apimodelproperty.smsmessagetemplate.description=The SMS message template of a generic notification.
v1.0.genericworkflownotificationdto.apimodelproperty.recipientListWillBeSuppliedAtRuntime.description=A Boolean indicator of whether or not the recipient is supplied at runtime.
v1.0.genericworkflownotificationdto.apimodelproperty.sendToEmployeesAttendanceAdministrator.description=A Boolean indicator of whether or not a notification is sent to an employee's Attendance administrator.
v1.0.genericworkflownotificationdto.apimodelproperty.sendToEmployeesLeaveAdministrator.description=A Boolean indicator of whether or not a notification is sent to an employee's Leave administrator.
v1.0.genericworkflownotificationdto.apimodelproperty.sendToEmployeesManager.description=A Boolean indicator of whether or not a notification is sent to an employee's manager.
v1.0.genericworkflownotificationdto.apimodelproperty.sendToEmployee.description=A Boolean indicator of whether or not a notification is sent to the employee.
