#Comments for Properties
v1.0.orgmapgroup.apimodel.description=Model for a location set, which is also known as an org map group.
v1.0.orgmapgroup.apimodelproperty.description.description=The description of a location set.
v1.0.orgmapgroup.apimodelproperty.versioncount.description=The version count of a location set.
v1.0.orgmapgroup.apimodelproperty.includeallenabled.description=A Boolean indicator of whether or not to include all enabled locations.
v1.0.orgmapgroup.apimodelproperty.typeid.description=The type ID of a location set type. Valid values include 1, 2, and 3, which correspond to All Organizational Groups, Manager Organizational Groups, and Transfer Organizational Groups, respectively.
v1.0.orgmapgroup.apimodelproperty.updatedatetime.description=The date and time of the most recent update to a location set.
v1.0.orgmapgroup.apimodelproperty.noderefs.description=A list of node references of a location set.
v1.0.orgmapgroup.apimodelproperty.golddata.description=A Boolean indicator of whether or not an org map group is gold data. Org map groups are also known as business structure location sets.
v1.0.orgmapgroup.apimodelproperty.persistentid.description=The persistent ID of a location set.
v1.0.orgmapgroup.apimodelproperty.nodeRefsTree.description=A Boolean indicator of whether or not to return a model that provides the Business Structure as a tree instead of a flat list.
v1.0.treenode.apimodelproperty.description.description=The description of an org node.
v1.0.orgmapgroup.apimodelproperty.nodejobs.description=A list of node job references.
v1.0.orgmapgroup.apimodelproperty.node.description=A reference to a node on the Business Structure.
v1.0.orgmapgroup.apimodelproperty.jobs.description=A list of references to jobs.
v1.0.orgmapgroup.apimodelproperty.entries.description=A list of generic location entries to pass in the Org Set if the Simplified Business Structure feature is enabled.