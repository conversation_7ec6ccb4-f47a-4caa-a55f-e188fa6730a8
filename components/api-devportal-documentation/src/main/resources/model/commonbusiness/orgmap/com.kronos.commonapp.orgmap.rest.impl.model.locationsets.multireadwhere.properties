#Comments for Properties
#Tue Feb 28 14:25:08 IST 2017
v1.0.multireadwhere.apimodel.description=A multi_read where model.
v1.0.multireadwhere.apimodelproperty.query.description=The name query of the org map groups to retrieve.
v1.0.multireadwhere.apimodelproperty.types.description=A list of references to the org map group types to retrieve. Valid object references include ID: 1, qualifier: All Organizational Groups, ID: 2, qualifier: Manager Organizational Groups, and ID: 3, qualifier: Transfer Organizational Groups.
v1.0.multireadwhere.apimodelproperty.context.description=This parameter indicates whether the search context is org or forecast.
v1.0.multireadwhere.apimodelproperty.date.description=The date of a search.
v1.0.multireadwhere.apimodelproperty.locationSets.description=A list of references to business structure location set IDs, qualifiers, and persistent IDs. These entities are also known as org map groups.
v1.0.multireadwhere.apimodelproperty.alldetails.description=A Boolean indicator of whether or not the response returns all details of each org map group. When false, the response returns only the ID, name, and persistent ID of each org map group. Org map groups are also known as business structure location sets.
