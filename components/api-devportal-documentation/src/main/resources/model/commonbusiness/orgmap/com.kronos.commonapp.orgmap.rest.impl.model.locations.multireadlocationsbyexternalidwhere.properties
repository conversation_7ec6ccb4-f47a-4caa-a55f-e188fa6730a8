v1.0.multireadlocationsbyexternalidwhere.apimodel.description=Model specifying the conditions related to effective date and external IDs for effective locations search.
v1.0.multireadlocationsbyexternalidwhere.apimodelproperty.externalIds.description=The external IDs of effective location references for which to search.
v1.0.multireadlocationsbyexternalidwhere.apimodelproperty.date.description=The search date in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.multireadlocationsbyexternalidwhere.apimodelproperty.context.description=This parameter indicates whether the search context is org or forecast.
v1.0.multireadlocationsbyexternalidwhere.apimodelproperty.externalpaths.description=The external paths of effective location references for which to search.
v1.0.multireadlocationsbyexternalidwhere.apimodelproperty.enddate.description=The end date in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.multireadlocationsbyexternalidwhere.apimodelproperty.returnallmatches.description=A Boolean indicator of whether or not to return all matches.