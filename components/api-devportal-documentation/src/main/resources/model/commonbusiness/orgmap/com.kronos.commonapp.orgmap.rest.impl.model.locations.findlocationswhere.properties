#Comments for Properties
#Tue Feb 28 14:25:08 IST 2017
v1.0.findlocationswhere.apimodel.description=The Retrieve Locations multi_read where model, containing conditions related to keyword, date, location set, descendants, parent, ancestors or children, and locations in a span.
v1.0.findlocationswhere.apimodelproperty.query.description=The keyword for which to search.
v1.0.findlocationswhere.apimodelproperty.locationset.description=The location set information used in a search.
v1.0.findlocationswhere.apimodelproperty.descendantsof.description=The descendant information used in a search.
v1.0.findlocationswhere.apimodelproperty.parentof.description=The parent information used in a search.
v1.0.findlocationswhere.apimodelproperty.ancestorsof.description=The ancestor information used in a search.
v1.0.findlocationswhere.apimodelproperty.childrenof.description=The children information used in a search.
v1.0.findlocationswhere.apimodelproperty.locationrefspan.description=A reference to location date spans used in a search.
v1.0.findlocationswhere.apimodelproperty.ancestorsofduring.description=The ancestors of during information used in a search.
v1.0.findlocationswhere.apimodelproperty.descendantsofduring.description=The descendants of during information used in a search.
v1.0.findlocationswhere.apimodelproperty.childrenofduring.description=The children of during information used in a search.