#Comments for Properties
# v1.0.orgmapnode.apimodel.value=OrgMapNodeSetupModel
v1.0.orgmapnode.apimodel.description=Model for an org map location.
v1.0.orgmapnode.apimodelproperty.nodeId.description=The effective-dated node ID.
v1.0.orgmapnode.apimodelproperty.reincarnationId.description=The ID identifying the incarnation of a node.
v1.0.orgmapnode.apimodelproperty.parentNodeRef.description =The parent node ID of an effective-dated node.If null passed on update then previous value from the revision with the closest effective date will be set.
v1.0.orgmapnode.apimodelproperty.name.description=The short name of an effective-dated node.If null passed on update then previous value from the revision with the closest effective date will be set.
v1.0.orgmapnode.apimodelproperty.fullName.description=The full name of an effective-dated node.
v1.0.orgmapnode.apimodelproperty.description.description=The description of an org node.
v1.0.orgmapnode.apimodelproperty.effectiveDate.description=The effective date of the current node revision.
v1.0.orgmapnode.apimodelproperty.expirationDate.description=The expiration date of the current node revision.
v1.0.orgmapnode.apimodelproperty.versionCount.description=The revision number of an effective-dated tree node.
v1.0.orgmapnode.apimodelproperty.context.description=The context assigned to a node.
v1.0.orgmapnode.apimodelproperty.isVirtualRootNode.description=A Boolean indicator of whether or not a node is a root node.
v1.0.orgmapnode.apimodelproperty.address.description=The address associated with a node.
v1.0.orgmapnode.apimodelproperty.timezoneRef.description=The timezone associated with a node.
v1.0.orgmapnode.apimodelproperty.costCenterRef.description=The cost center associated with a node.
v1.0.orgmapnode.apimodelproperty.directWorkPercent.description=The direct work allocation associated with a node.
v1.0.orgmapnode.apimodelproperty.indirectWorkPercent.description=The indirect work allocation associated with a node.
v1.0.orgmapnode.apimodelproperty.firstRevision.description=A Boolean indicator of whether or not this is a node's first revision. 
v1.0.orgmapnode.apimodelproperty.lastRevision.description=A Boolean indicator of whether or not this is a node's last revision. If no value is passed during an update, this value will update to true.
v1.0.orgmapnode.apimodelproperty.transferable.description=A Boolean indicator of whether or not a location is transferable.
v1.0.orgmapnode.apimodelproperty.deleted.description=A Boolean indicator of whether or not a node has been deleted.
v1.0.orgmapnode.apimodelproperty.guid.description=The PersistentId of an org map node.
v1.0.orgmapnode.apimodelproperty.updateDateTime.description=The date and time of the most recent update to an org map location.
v1.0.orgmapnode.apimodelproperty.latitude.description=The latitude of a node.
v1.0.orgmapnode.apimodelproperty.longitude.description=The longitude of a node.
v1.0.orgmapnode.apimodelproperty.latitudeLongitudeUpdatedDateTime.description=The date and time of the most recent update to the latitude and longitude of an org map node.
v1.0.orgmapnode.apimodelproperty.externalId.description=The external ID of an org map node. If no value is passed during an update, the previous value remains in effect and is taken from the revision with the most recent effective date.
v1.0.orgmapnode.apimodelproperty.color.description=The color of an org map node. Only job nodes can have a color.