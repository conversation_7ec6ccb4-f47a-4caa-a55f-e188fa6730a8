#Comments for Properties
# v1.0.orgobjectrefspan.apimodel.value=OrgObjectRefSpan
v1.0.orgobjectrefspan.apimodel.description=Model that represents a common org object reference with effective and expiration date.
v1.0.orgobjectrefspan.apimodelproperty.ref.description=A reference to an org object.
v1.0.orgobjectrefspan.apimodelproperty.effectiveDate.description=The effective date of an org object reference in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.orgobjectrefspan.apimodelproperty.expirationDate.description=The expiration date of an org object reference in ISO_LOCAL_DATE format (YYYY-MM-DD).
