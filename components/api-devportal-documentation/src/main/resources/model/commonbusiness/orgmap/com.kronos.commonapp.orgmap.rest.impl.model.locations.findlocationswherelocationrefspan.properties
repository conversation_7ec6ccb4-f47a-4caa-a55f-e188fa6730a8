#Comments for Properties
#Tue Feb 28 14:25:08 IST 2017
v1.0.findlocationswherelocationrefspan.apimodel.description=The Retrieve Locations date span and location reference model.
v1.0.findlocationswherelocationrefspan.apimodelproperty.locationref.description=A reference to the location for the search.
v1.0.findlocationswherelocationrefspan.apimodelproperty.revisiondate.description=The effective date of a revision for the search in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.findlocationswherelocationrefspan.apimodelproperty.startdate.description=The effective start date of the search in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.findlocationswherelocationrefspan.apimodelproperty.enddate.description=The effective end date of the search in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.findlocationswherelocationrefspan.apimodelproperty.context.description=The context of the search. Valid values include ORG or FORECAST.
v1.0.findlocationswherelocationrefspan.apimodelproperty.locationrefs.description=The location references for which to search.