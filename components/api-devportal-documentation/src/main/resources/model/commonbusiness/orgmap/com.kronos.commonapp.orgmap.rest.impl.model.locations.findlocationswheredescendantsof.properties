#Comments for Properties
#Tue Feb 28 14:25:08 IST 2017
v1.0.findlocationswheredescendantsof.apimodel.description=The Retrieve Locations 'descendants of' model.
v1.0.findlocationswheredescendantsof.apimodelproperty.locationref.description=A reference to the location node from which to search for descendants.
v1.0.findlocationswheredescendantsof.apimodelproperty.includelocationtypes.description=A list of location type references from which to search for descendants.
v1.0.findlocationswheredescendantsof.apimodelproperty.date.description=The effective date of the search in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.findlocationswheredescendantsof.apimodelproperty.context.description=The context of the search. Valid values include ORG or FORECAST.
