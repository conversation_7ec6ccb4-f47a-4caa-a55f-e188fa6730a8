v1.0.treenode.apimodel.description=The model that stores the tree structure of an organizational map.
v1.0.treenode.apimodelproperty.id.description=The data structure for organizational map nodes presented in a tree format.
v1.0.treenode.apimodelproperty.nodeName.description=The name of a node.
v1.0.treenode.apimodelproperty.parent.description=A reference to the parent tree node.
v1.0.treenode.apimodelproperty.children.description=The children of a tree node.