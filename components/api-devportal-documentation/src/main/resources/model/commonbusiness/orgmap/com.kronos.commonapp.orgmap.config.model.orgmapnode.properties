#Comments for Properties
# v1.0.orgmapnode.apimodel.value=OrgMapNode
v1.0.orgmapnode.apimodel.description=Model for an org map location.
v1.0.orgmapnode.apimodelproperty.node.description=The node that corresponds to the node in the widget.
v1.0.orgmapnode.apimodelproperty.orgNodeTypeRef.description=A reference to the type of the node.
v1.0.orgmapnode.apimodelproperty.genericJobRef.description=A reference to the type of a generic job.
v1.0.orgmapnode.apimodelproperty.genericlocationref.description=A reference to the type of a generic location.
v1.0.orgmapnode.apimodelproperty.depth.description=The depth of the node.
v1.0.orgmapnode.apimodelproperty.isEditable.description=A Boolean indicator of whether or not a node is editable.
v1.0.orgmapnode.apimodelproperty.isRootNode.description=A Boolean indicator of whether or not a node is a root.
v1.0.orgmapnode.apimodelproperty.isLeaf.description=A Boolean indicator of whether or not a node is a leaf.
v1.0.orgmapnode.apimodelproperty.displayOrder.description=The display order of a job node.
v1.0.orgmapnode.apimodelproperty.currencyref.description=A reference to the explicit currency associated with a node.
v1.0.orgmapnode.apimodelproperty.isCurrencyAdd.description=A Boolean indicator of whether or not the currency can be assigned to the child of a node.
v1.0.orgmapnode.apimodelproperty.isCurrencyEditable.description=A Boolean indicator of whether or not the currency of the node is editable.
v1.0.orgmapnode.apimodelproperty.effectiveCurrencyRef.description=A reference to the applicable currency that is either explicitly assigned or inherited from the parent.