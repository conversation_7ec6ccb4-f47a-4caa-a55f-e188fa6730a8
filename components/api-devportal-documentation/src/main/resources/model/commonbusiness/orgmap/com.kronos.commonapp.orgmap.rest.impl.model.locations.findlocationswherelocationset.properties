#Comments for Properties
#Tue Feb 28 14:25:08 IST 2017
v1.0.findlocationswherelocationset.apimodel.description=Model containing information on the location set (group) where the search for location nodes is made.
v1.0.findlocationswherelocationset.apimodelproperty.locationsetref.description=A reference to the location set for which to search.
v1.0.findlocationswherelocationset.apimodelproperty.date.description=The effective date of a search.
v1.0.findlocationswherelocationset.apimodelproperty.context.description=This parameter indicates whether the search context is org or forecast.
v1.0.findlocationswherelocationset.apimodelproperty.expandtolocationtypes.description=The type level to which a location is expanded.
v1.0.findlocationswherelocationset.apimodelproperty.span.description=The date span during which a location is expanded.