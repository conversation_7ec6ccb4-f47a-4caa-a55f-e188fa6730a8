#Comments for Properties
v2.0.findlocationsmultireadoptions.apimodel.description=Model holding conditions related to read options to be used in an Org Map multi read request.
v2.0.findlocationsmultireadoptions.apimodelproperty.includeorgpathdetails.description=Indicate if org path details are required.
v2.0.findlocationsmultireadoptions.apimodelproperty.modifiedsince.description=Indicate if modified time details are required.
v2.0.findlocationsmultireadoptions.apimodelproperty.includeinheritedcurrency.description=Indicate if effectiveCurrency field is required.
v2.0.findlocationsmultireadoptions.apimodelproperty.count.description=Indicated the number of elements to be returned. 
v2.0.findlocationsmultireadoptions.apimodelproperty.index.description=Index the index page to be returned.