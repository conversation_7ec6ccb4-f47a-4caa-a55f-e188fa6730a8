#Comments for Properties

v1.0.targetdefinition.apimodel.description=Model describing the details of target.
v1.0.targetdefinition.apimodelproperty.type.description=The type of target.
v1.0.targetdefinition.apimodelproperty.subtype.description=The sub type of target.
v1.0.targetdefinition.apimodelproperty.functiontype.description=The function type of target.
v1.0.targetdefinition.apimodelproperty.functionid.description=The function id of target.
v1.0.targetdefinition.apimodelproperty.locreftype.description=The location reference type of target.
v1.0.targetdefinition.apimodelproperty.locrefid.description=The location reference id of target.
v1.0.targetdefinition.apimodelproperty.refsourcesymbolicname.description=The reference source symbolic name of target.
v1.0.targetdefinition.apimodelproperty.target.description=Date wise definition of the target.