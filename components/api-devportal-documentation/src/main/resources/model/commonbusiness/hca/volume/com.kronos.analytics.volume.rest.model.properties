#Raw Volume Import
v1.0.volumeimportdto.apimodel.description=Model describing the details of a raw volume import.
v1.0.volumeimportdto.apimodelproperty.updateType.description=The update type of raw volume import.
v1.0.volumeimportdto.apimodelproperty.eof.description=The end of file of raw volume import.
v1.0.volumeimportdto.apimodelproperty.loadDateTime.description=The load date time of raw volume import.
v1.0.volumeimportdto.apimodelproperty.patType.description=The patient type of raw volume import.
v1.0.volumeimportdto.apimodelproperty.source.description=The source of raw volume import.
v1.0.volumeimportdto.apimodelproperty.rawVolumes.description=List of raw volume data.

#Raw Volume
v1.0.rawvolumedto.apimodel.description=Model describing the details of a raw volume.
v1.0.rawvolumedto.apimodelproperty.quantity.description=The quantity of raw volume.
v1.0.rawvolumedto.apimodelproperty.weight.description=The weight of raw volume.
v1.0.rawvolumedto.apimodelproperty.serviceDate.description=The service date of raw volume.
v1.0.rawvolumedto.apimodelproperty.postingDate.description=The posting date of raw volume.
v1.0.rawvolumedto.apimodelproperty.patTypeValue.description=The patient type Value of raw volume.
v1.0.rawvolumedto.apimodelproperty.updateDateTime.description=The update date time of raw volume.
v1.0.rawvolumedto.apimodelproperty.volumeLabel.description=The procedure code/volume label of raw volume.
v1.0.rawvolumedto.apimodelproperty.billingDepartment.description=The billing department of raw volume.
v1.0.rawvolumedto.apimodelproperty.id.description=The ID of raw volume.

#Volume Label
v1.0.volumelabeldto.apimodel.description=Model describing the details of a procedure code/volume label.
v1.0.volumelabeldto.apimodelproperty.id.description=The ID of procedure code/volume label.
v1.0.volumelabeldto.apimodelproperty.qualifier.description=The volume label code of procedure code/volume label.
v1.0.volumelabeldto.apimodelproperty.description.description=The description of procedure code/volume label.

#Billing Department
v1.0.billingdepartment.apimodel.description=Model describing the details of a billing department.
v1.0.billingdepartment.apimodelproperty.id.description=The ID of billing department.
v1.0.billingdepartment.apimodelproperty.facilitycode.description=The facility code of billing department.
v1.0.billingdepartment.apimodelproperty.departmentcode.description=The department code of billing department.

#Billing Department and Work Unit Mapping
v1.0.billingdeptwumap.apimodel.description=Model describing the details of a billing department work unit Mapping.
v1.0.billingdeptwumap.apimodelproperty.id.description=The ID of billing department work unit Mapping.
v1.0.billingdeptwumap.apimodelproperty.workunit.description=The work unit of billing department work unit Mapping.
v1.0.billingdeptwumap.apimodelproperty.billingdepartment.description=The billing department of billing department work unit Mapping.	
v1.0.billingdeptwumap.apimodelproperty.deleteswitch.description=The delete switch of billing department work unit Mapping.

#Billing Department Work Unit
v1.0.billingdeptworkunit.apimodel.description=Model describing the details of a work unit.
v1.0.billingdeptworkunit.apimodelproperty.id.description=The ID of work unit.
v1.0.billingdeptworkunit.apimodelproperty.qualifier.description=The code of work unit.
v1.0.billingdeptworkunit.apimodelproperty.name.description=The name of work unit.

#Charge Master
v1.0.chargemasterdto.apimodel.description=Model describing the details of a charge master.
v1.0.chargemasterdto.apimodelproperty.id.description=The ID of charge master.
v1.0.chargemasterdto.apimodelproperty.volumeLabel.description=The procedure code/volume Label of charge master.
v1.0.chargemasterdto.apimodelproperty.billingDepartment.description=The billing department of charge master.
v1.0.chargemasterdto.apimodelproperty.weight.description=The weight of charge master.
v1.0.chargemasterdto.apimodelproperty.effectivedate.description=The effective date of charge master.

#Volume Copy Override
v1.0.volumecopyoverridedto.apimodel.description=Model describing the details of a volume copy override.
v1.0.volumecopyoverridedto.apimodelproperty.volumelabeldto.description=The procedure code/volume label of volume copy override.

#Volume Department Copy
v1.0.volumedeptcopydto.apimodel.description=Model describing the details of a volume department copy.
v1.0.volumedeptcopydto.apimodelproperty.id.description=The ID of volume department copy.
v1.0.volumedeptcopydto.apimodelproperty.srcbillingdepartmentdto.description=The source billing department of volume department copy.
v1.0.volumedeptcopydto.apimodelproperty.destbillingdepartmentdto.description=The destination billing department of volume department copy.
v1.0.volumedeptcopydto.apimodelproperty.weight.description=The weight of volume department copy.

#Historical Volume Recalculation
v1.0.historicalvolumerecalcdto.apimodel.description=Model describing the details of a historical volume recalculation.
v1.0.historicalvolumerecalcdto.apimodelproperty.volumes.description=The date range for historical volume recalculation.

#Volume Recalculation Data
v1.0.historicalvolumerecalcdto.apimodelproperty.volumes.description=Model describing date range of historical volume recalculation.
v1.0.volumerecalcdatadto.apimodelproperty.startDate.description=The start date of historical volume recalculation.
v1.0.volumerecalcdatadto.apimodelproperty.endDate.description= The end date of historical volume recalculation.

#Volume Process Status
v1.0.volume_process_status.apimodel.description=Model describing the details of a volume process.
v1.0.volume_process_status.apimodelproperty.startDateTime.description=The start date time of a volume process.
v1.0.volume_process_status.apimodelproperty.endDateTime.description=The end date time of a volume process.
v1.0.volume_process_status.apimodelproperty.status.allowableValues=PENDING, IN_PROGRESS_RAW, IN_PROGRESS_BATCH, PROCESSED, FAILED
v1.0.volume_process_status.apimodelproperty.status.description=The status of a volume process.
v1.0.volumeprocessstatusdto.apimodel.description= Model describing the details of volume process status.
v1.0.volumeprocessstatusdto.apimodelproperty.id.description= The ID of a volume process status.
v1.0.volumeprocessstatusdto.apimodelproperty.processname.description= The process name associated with volume process status.
v1.0.volumeprocessstatusdto.apimodelproperty.startdatetime.description= The start date and time of a volume process status in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.volumeprocessstatusdto.apimodelproperty.enddatetime.description= The end date and time of a volume process status in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.volumeprocessstatusdto.apimodelproperty.processkey.description= The key associated with a volume process.
v1.0.volumeprocessstatusdto.apimodelproperty.status.description= The status of a volume process.
v1.0.volumeprocessstatusdto.apimodelproperty.retrycount.description= The number (count) of retries associated with a volume process status.
v1.0.volumeprocessstatusdto.apimodelproperty.updatedatetime.description=  The date and time a volume process status was last updated in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.volumeprocessstatusdto.apimodelproperty.batchjobid.description= The ID of a batch job.
v1.0.volumeprocessstatusdto.apimodelproperty.comments.description= The Comments associated with a volume process status.
v1.0.volumeprocessstatusdto.apimodelproperty.createdatetime.description= The date and time a volume batch was created in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.volumeprocessstatusdto.apimodelproperty.processgroupkey.description= The key associated with a volume process group.
v1.0.volumeprocessstatusdto.apimodelproperty.ktid.description= The KTID (Kronos transaction ID) associated with a volume process status.
v1.0.volumeprocessstatusdto.apimodelproperty.hcastatus.description= The status of a volume process. 

# Volume Label Where
v1.0.volumelabelwhere.apimodel.description=Model describing the volume label where clause.
v1.0.volumelabelwhere.apimodelproperty.refs.description=The ID or qualifier that identifies an employee.
v1.0.volumelabelwhere.apimodelproperty.ids.description=A list of employee IDs. These are not person numbers.
v1.0.volumelabelwhere.apimodelproperty.qualifiers.description=A list of employee qualifiers.

v1.0.volumelabelrequest.apimodel.description=Model describing the volume label request body.
v1.0.volumelabelrequest.apimodelproperty.where.description=The where clause of a volume label request in which the actual criteria for retrieving volume labels is specified.

# Volume Label

v1.0.volumelabel.apimodel.description=Model describing a volume label object reference.
v1.0.volumelabel.apimodelproperty.qualifier.description=The qualifier of a volume label.
v1.0.volumelabel.apimodelproperty.id.description=The ID of a volume label.

# Raw Volume Error
v1.0.rawvolumeerror.apimodel.description=Model describing the details of a raw volume error object.
v1.0.rawvolumeerror.apimodelproperty.facilityCode.description=The billing facility associated with raw volume.
v1.0.rawvolumeerror.apimodelproperty.departmentCode.description=The billing department associated with raw volume.
v1.0.rawvolumeerror.apimodelproperty.volumeLabel.description=The procedure code or volume label associated with raw volume.
v1.0.rawvolumeerror.apimodelproperty.postingDate.description=The posting date in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.rawvolumeerror.apimodelproperty.serviceDate.description=The service date in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.rawvolumeerror.apimodelproperty.patType.description=A Boolean indicator of whether or not a patient type is associated with a set of raw volume data.
v1.0.rawvolumeerror.apimodelproperty.source.description=The source type value associated with the raw volume file.
v1.0.rawvolumeerror.apimodelproperty.rawVolumes.description=The list of raw volume error data.
v1.0.rawvolumeerror.apimodelproperty.quantity.description=The quantity associated with raw volume.
v1.0.rawvolumeerror.apimodelproperty.weight.description=The weight associated with raw volume.
v1.0.rawvolumeerror.apimodelproperty.volumeLabelDescription.description=The procedure code or volume label description associated with raw volume.
v1.0.rawvolumeerror.apimodelproperty.createDtm.description=The date and time a raw volume error record was created in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddTHH:MM:SS).
v1.0.rawvolumeerror.apimodelproperty.updateDtm.description=The date and time a raw volume error record was last processed in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddTHH:MM:SS).
v1.0.rawvolumeerror.apimodelproperty.patTypeValue.description=The patient type value associated with raw volume.
v1.0.rawvolumeerror.apimodelproperty.errors.description=The list of errors.
v1.0.rawvolumeerror.apimodelproperty.errorCode.description=The error code.
v1.0.rawvolumeerror.apimodelproperty.message.description=The error message.

# Raw Volume Error Response DTO
v1.0.rawvolumeerrorresponsedto.apimodel.description=Model describing the details of the raw volume error response object.
v1.0.rawvolumeerrorresponsedto.apimodelproperty.totalRecords.description=The total number of error records.
v1.0.rawvolumeerrorresponsedto.apimodelproperty.records.description=The list of errors.