#Comments for Properties

v1.0.daterangeresponse.apimodel.description=Model for the resolved date ranges.
v1.0.daterangeresponse.apimodelproperty.timeincrement.description=The time increment type. Valid values include DAY and PAYPERIOD.
v1.0.daterangeresponse.apimodelproperty.datespans.description=The start and end dates in ISO_LOCAL_DATE format (YYYY-MM-DD) along with the optional days of operation of the resolved pay periods (for a symbolic time period) or the date range supplied.
v1.0.daterangeresponse.apimodelproperty.symbolictimeperiod=A symbolic identifier that represents a timeframe or a span of time.