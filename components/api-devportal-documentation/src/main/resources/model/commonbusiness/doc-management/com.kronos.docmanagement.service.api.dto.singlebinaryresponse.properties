#Comments for Properties
#Mon Feb 27 14:48:15 IST 2017
v1.0.singlebinaryresponse.apimodel.description=Model for the Doc Management entity (Document, Template).
v1.0.singlebinaryresponse.apimodelproperty.id.description=The ID of a document management entity.
v1.0.singlebinaryresponse.apimodelproperty.creationdatetime.description=The date and time a document management entity was created.
v1.0.singlebinaryresponse.apimodelproperty.size.description=The size of a document management entity.
v1.0.singlebinaryresponse.apimodelproperty.domain.description=The domain of a document management entity.
v1.0.singlebinaryresponse.apimodelproperty.name.description=The name of a document management entity.
v1.0.singlebinaryresponse.apimodelproperty.locale.description=The locale of a document management entity.
