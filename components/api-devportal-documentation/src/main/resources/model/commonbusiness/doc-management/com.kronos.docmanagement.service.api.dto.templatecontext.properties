#Comments for Properties
#Mon Feb 27 14:48:15 IST 2017
v1.0.templatecontext.apimodel.description=Model representing data for Template creation.
v1.0.templatecontext.apimodelproperty.id.description=The ID of a template.
v1.0.templatecontext.apimodelproperty.filename.description=The file name of a template.
v1.0.templatecontext.apimodelproperty.name.description=The name of a template.
v1.0.templatecontext.apimodelproperty.description.description=The description of a template.
v1.0.templatecontext.apimodelproperty.localeid.description=The locale ID of a template.
v1.0.templatecontext.apimodelproperty.content.description=The content of a template.
v1.0.templatecontext.apimodelproperty.active.description=A Boolean indicator of whether or not a template is active.
v1.0.templatecontext.apimodelproperty.domainid.description=The domain ID of a template.
v1.0.templatecontext.apimodelproperty.ignoreunknowntagscheck.description=A Boolean indicator of whether or not validation on unknown tags should be ignored. The default value is FALSE.
v1.0.templatecontext.apimodelproperty.isupdate.description=A Boolean indicator of whether or not context data is for an update to a template.
