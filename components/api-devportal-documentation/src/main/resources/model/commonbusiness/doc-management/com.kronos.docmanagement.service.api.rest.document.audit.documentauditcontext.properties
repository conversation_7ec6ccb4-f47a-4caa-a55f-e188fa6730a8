#Comments for Properties
#Tue Apr 4 2017
v1.0.documentauditcontext.apimodel.description=Request Context for retrieving document auditing.
v1.0.documentauditcontext.apimodelproperty.domain.description=A reference to a domain object.
v1.0.documentauditcontext.apimodelproperty.startdate.description=The start date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.documentauditcontext.apimodelproperty.enddate.description=The end date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).

