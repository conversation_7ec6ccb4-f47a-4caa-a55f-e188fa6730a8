#Comments for Properties
#Mon Feb 27 14:48:15 IST 2017
v1.0.templatesdmdto.apimodel.description=Model for the Template Setup Data Manager (SDM) resource.
v1.0.templatesdmdto.apimodelproperty.name.description=The name of a template. The name must be unique.
v1.0.templatesdmdto.apimodelproperty.filename.description=The file name of a template.
v1.0.templatesdmdto.apimodelproperty.description.description=The description of a template.
v1.0.templatesdmdto.apimodelproperty.locale.description=The locale name associated with a template.
v1.0.templatesdmdto.apimodelproperty.contentencodedstr.description=The encoded template content.
v1.0.templatesdmdto.apimodelproperty.active.description=A Boolean indicator of whether or not a template is active.
v1.0.templatesdmdto.apimodelproperty.domain.description=The domain name associated with a template.
