#Comments for Properties
v1.0.commons-generic_data_access_dag_item.apimodel.description=Model containing information for a Setup Item.
v1.0.commons-generic_data_access_dag_item.apimodelproperty.id.description=The ID of a Setup Item.
v1.0.commons-generic_data_access_dag_item.apimodelproperty.name.description=The name of a Setup Item.
v1.0.commons-generic_data_access_dag_item.apimodelproperty.type.description=The type of a Setup Item.
v1.0.commons-generic_data_access_dag_item.apimodelproperty.category.description=The category of a Setup Item.
v1.0.commons-generic_data_access_dag_item.apimodelproperty.fullaccess.description=A Boolean indicator of whether or not all items in a particular category of a Setup Item are selected under a particular GDAP.
v1.0.commons-generic_data_access_dag_item.apimodelproperty.deleted.description=A Boolean indicator of whether or not a Setup Item has been deleted.
v1.0.commons-generic_data_access_dag_item.apimodelproperty.workRuleAssociated.description=A Boolean indicator of whether or not a work rule is associated with a Setup Item.