#Comments for Properties
v1.0.commons-generic_data_access_profiles_categories.apimodel.desc=The Category model for Generic Data Access Profiles.
v1.0.commons-generic_data_access_profiles_categories.apimodelproperty.id.description=The ID of the Generic Data Access Profiles Category.
v1.0.commons-generic_data_access_profiles_categories.apimodelproperty.name.description=The name of the Generic Data Access Profiles Category.

v1.0.commons-generic_data_access_profiles.gdapgetallprofilesbycategoryidresponse.apimodel.description=The Retrieve All Generic Data Access Profiles by Category ID response model.
v1.0.commons-generic_data_access_profiles.gdapgetallprofilesbycategoryidresponse.apimodelproperty.totalelements.description=The number of Generic Data Access Profiles of a category present in the system.
v1.0.commons-generic_data_access_profiles.gdapgetallprofilesbycategoryidresponse.apimodelproperty.records.description=This is the list of all Generic Data Access Profiles of a category.

v1.0.commons-generic_data_access_profiles.gdapitemsresp.apimodel.description=The response model for retrieving a list of Generic Data Access Profiles.
v1.0.commons-generic_data_access_profiles.gdapitemsresp.apimodelproperty.id.description=The ID of the Generic Data Access Profiles Item.
v1.0.commons-generic_data_access_profiles.gdapitemsresp.apimodelproperty.name.description=The name of the Generic Data Access Profiles Item.
v1.0.commons-generic_data_access_profiles.gdapitemsresp.apimodelproperty.description.description=The description of the Generic Data Access Profiles Item.
v1.0.commons-generic_data_access_profiles.gdapitemsresp.apimodelproperty.availablesetupitems.description=A list of available setup items for the Generic Data Access Profiles Item.
v1.0.commons-generic_data_access_profiles.gdapitemsresp.apimodelproperty.setupitems.description=A list of selected setup items for the Generic Data Access Profiles Item.

v1.0.commons-generic_data_access_profiles.dagsetupitems.apimodel.description=The Retrieve Generic Data Access Profile by ID With Available Items response model.
v1.0.commons-generic_data_access_profiles.dagsetupitems.apimodelproperty.id.description=The ID of an available item associated with Generic Data Access Profiles.
v1.0.commons-generic_data_access_profiles.dagsetupitems.apimodelproperty.name.description=The name of an available item associated with Generic Data Access Profiles.
v1.0.commons-generic_data_access_profiles.dagsetupitems.apimodelproperty.type.description=The type of an available item associated with Generic Data Access Profiles.
v1.0.commons-generic_data_access_profiles.dagsetupitems.apimodelproperty.category.description=The category of an available item associated with Generic Data Access Profiles.
