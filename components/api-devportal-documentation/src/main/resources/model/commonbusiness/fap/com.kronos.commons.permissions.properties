v1.0.PermissionsTypes.apimodel.description=Permissions Types Profile model.
v1.0.PermissionsTypes.apimodelproperty.controlpointkey.description=The controlpointkey of a permissions type.
v1.0.PermissionsTypes.apimodelproperty.scopename.description=The scope name of a permissions type.
v1.0.PermissionsTypes.apimodelproperty.actionname.description=The action name of a permissions type.
v1.0.PermissionsTypes.apimodelproperty.scopename.allowableValues=ALL,ALL_BUT_SELF,ONLY_SELF,NONE.
v1.0.PermissionsTypes.apimodelproperty.actionname.allowableValues=ADD,EDIT,DELETE,VIEW,ALLOWED.
