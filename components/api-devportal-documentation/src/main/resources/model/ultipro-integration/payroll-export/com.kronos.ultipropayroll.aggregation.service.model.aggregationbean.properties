v1.0.ultiproaggregationbean.apimodel.description=Model for Ultipro payroll aggregation request.
v1.0.ultiproaggregationbean.apimodelproperty.aggregationwherecriteria.description=A reference to the aggregationWhereCriteria object.
v1.0.ultiproaggregationbean.apimodelproperty.daterangecriteria.description=A reference to the dateRangeCriteria object.
v1.0.ultiproaggregationbean.apimodelproperty.ignoresignoff.description=A Boolean indicator of whether or not to ignore signoff.
v1.0.ultiproaggregationbean.apimodelproperty.paygroup.description=The paygroup associated with Ultipro payroll aggregation request.
v1.0.ultiproaggregationbean.apimodelproperty.passpayrate.description=The pass pay rate associated with Ultipro payroll aggregation request.
v1.0.ultiproaggregationbean.apimodelproperty.aggregationparameters.description=The aggregation parameters associated with Ultipro payroll aggregation request.
v1.0.ultiproaggregationbean.apimodelproperty.includepaycodes.description=A list of include paycodes.
v1.0.ultiproaggregationbean.apimodelproperty.excludepaycodes.description=A list of exclude paycodes.
v1.0.ultiproaggregationbean.apimodelproperty.hyperfind.description=The Hyperfind associated with Ultipro payroll aggregation request.
v1.0.ultiproaggregationbean.apimodelproperty.enableOrgRelationship.description=A Boolean indicator of whether or not to exclude Org Relationship parameters if added in configuration page.


