v1.0.ultipropayrollearningsbean.apimodel.description=Model for payroll earnings.
v1.0.ultipropayrollearningsbean.apimodelproperty.earning.description=A reference to the payroll earnings object.
v1.0.ultipropayrollearningsbean.apimodelproperty.hours.description=The hours associated with payroll earnings.
v1.0.ultipropayrollearningsbean.apimodelproperty.payrate.description=The pay rate associated with payroll earnings.
v1.0.ultipropayrollbean.apimodel.description=Model for payroll.
v1.0.ultipropayrollbean.apimodelproperty.chargedate.description=The charge date associated with payroll earnings.
v1.0.ultipropayrollbean.apimodelproperty.sourceGroupId.description=The day-basis created source group ID associated with payroll earnings.
v1.0.ultipropayrollbean.apimodelproperty.earningType.description=The earning type associated with payroll earnings.
v1.0.ultipropayrollbean.apimodelproperty.adjustmentDate.description=The adjustment date associated with payroll earnings.
v1.0.ultipropayrollbean.apimodelproperty.amount.description=The amount associated with payroll earnings.
v1.0.ultipropayrollbean.apimodelproperty.orghierarchy.description=A list of references to Business Structure nodes (org hierarchy) associated with payroll earnings.
v1.0.ultipropayrollbean.apimodelproperty.laborcategories.description=A list of labor categories.
v1.0.ultipropayrollbean.apimodelproperty.genericjobref.description=A reference to a generic job associated with payroll earnings.
v1.0.ultipropayrollbean.apimodelproperty.shiftcode.description=The shift code associated with payroll earnings.