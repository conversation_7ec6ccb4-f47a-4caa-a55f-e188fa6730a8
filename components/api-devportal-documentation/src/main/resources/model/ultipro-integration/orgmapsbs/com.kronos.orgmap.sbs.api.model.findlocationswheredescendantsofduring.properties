# The following keys are duplicated in the newer file com.kronos.commonapp.orgmap.rest.impl.model.locations.findlocationswheredescendantsofduring.properties

# v1.0.FindLocationsWhereDescendantsOfDuring.apimodel.description=Model containing information of the node where the search for descendants is to be made during span.
# v1.0.FindLocationsWhereDescendantsOfDuring.apimodelproperty.OrgObjectRefWithGuid.description=The location node reference to search for descendants.
# v1.0.FindLocationsWhereDescendantsOfDuring.apimodelproperty.OrgObjectRefWithGuidList.description=List of location type references to search for.
# v1.0.FindLocationsWhereDescendantsOfDuring.apimodelproperty.MapContextTypeRest.description=Indicate if it is ORG or FORECAST context.
# v1.0.FindLocationsWhereDescendantsOfDuring.apimodelproperty.StartDate.description=The start date for the search.
# v1.0.FindLocationsWhereDescendantsOfDuring.apimodelproperty.EndDate.description=The end date for the search.
# v1.0.FindLocationsWhereDescendantsOfDuring.apimodelproperty.StopTraversal.description=Flag to stop tree traversal on non included types.