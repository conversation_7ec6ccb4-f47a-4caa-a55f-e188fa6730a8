v1.0.orgmapmodewithguid.apimodel.description=Model for an org map location.
v1.0.orgmapnodewithguid.apimodelproperty.nodeid.description=The effective-dated node ID.
v1.0.orgmapnodewithguid.apimodelproperty.name.description=The short name of an effective-dated node.If null passed on update then previous value from the revision with the closest effective date will be set.
v1.0.orgmapnodewithguid.apimodelproperty.fullname.description=The full name of an effective-dated node.
v1.0.orgmapnodewithguid.apimodelproperty.description.description=The description of an org node.
v1.0.orgmapnodewithguid.apimodelproperty.effectiveDate.description=The effective date of the current node revision.
v1.0.orgmapnodewithguid.apimodelproperty.expirationdate.description=The expiration date of the current node revision.
v1.0.orgmapnodewithguid.apimodelproperty.versioncount.description=The revision number of an effective-dated tree node.
v1.0.orgmapnodewithguid.apimodelproperty.reincarnationid.description=The ID identifying the incarnation of a node.
v1.0.orgmapnodewithguid.apimodelproperty.context.description=The context assigned to a node.
v1.0.orgmapnodewithguid.apimodelproperty.isvirtualrootnode.description=A Boolean indicator of whether or not a node is a root node.
v1.0.orgmapnodewithguid.apimodelproperty.address.description=The address associated with a node.
v1.0.orgmapnodewithguid.apimodelproperty.timezoneref.description=The timezone associated with a node.
v1.0.orgmapnodewithguid.apimodelproperty.currencyref.description=The currency ref associated with a node.
v1.0.orgmapnodewithguidwithguid.apimodelproperty.costcenterref.description=The cost center associated with a node.
v1.0.orgmapnodewithguid.apimodelproperty.directworkpercent.description=The direct work allocation associated with a node.
v1.0.orgmapnodewithguid.apimodelproperty.indirectworkpercent.description=The indirect work allocation associated with a node.
v1.0.orgmapnodewithguid.apimodelproperty.firstrevision.description=A Boolean indicator of whether or not this is a node's first revision. 
v1.0.orgmapnodewithguid.apimodelproperty.lastrevision.description=A Boolean indicator of whether or not this is a node's last revision. If no value is passed during an update, this value will update to true.
v1.0.orgmapnodewithguid.apimodelproperty.transferable.description=A Boolean indicator of whether or not a location is transferable.
v1.0.orgmapnodewithguid.apimodelproperty.externalid.description=The external ID of an org map node. If no value is passed during an update, the previous value remains in effect and is taken from the revision with the most recent effective date.
v1.0.orgmapnodewithguid.apimodelproperty.color.description=The color of an org map node. Only job nodes can have a color.
v1.0.orgmapnodewithguid.apimodelproperty.deleted.description=A Boolean indicator of whether or not a node has been deleted.
v1.0.orgmapnodewithguid.apimodelproperty.updatedatetime.description=The date and time of the most recent update to an org map location.
v1.0.orgmapnodewithguid.apimodelproperty.guid.description=The PersistentId of an org map node.
v1.0.orgmapmodewithguid.apimodelproperty.orgnodetyperef.description=The org node type ref of an org node.
v1.0.orgmapmodewithguid.apimodelproperty.genericjobref.description=The generic job ref of an org node.
v1.0.orgmapmodewithguid.apimodelproperty.parentnoderef.description=The parent node ID of an effective-dated node.If null passed on update then previous value from the revision with the closest effective date will be set.