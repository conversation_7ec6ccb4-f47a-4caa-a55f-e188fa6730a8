# v1.0.FindLocationsWhereChildrenOf.apimodel.description=Model holding conditions related to children of, Locations in a span to be used in an Org Map master root node read request.
# v1.0.findlocationswherechildrenof.apimodelproperty.locationref.description=The info to be used for searching location reference.
# v1.0.findlocationswherechildrenof.apimodelproperty.date.description=The info to be used for the date field.
# v1.0.findlocationswherechildrenof.apimodelproperty.context.description=The info is used for the context.
# v1.0.findlocationswherechildrenof.apimodelproperty.locationrefs.description=The info is used for the loaction references.
# v1.0.findlocationswherechildrenof.apimodelproperty.includelocationtypes.description= This field will include the location types.