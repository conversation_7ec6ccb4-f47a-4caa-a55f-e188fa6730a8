# Replaced by duplicate strings in the newer file com.kronos.commonapp.orgmap.rest.impl.model.locations.findlocationsmultireadoptions.properties
# v1.0.FindLocationsMultiReadOptions.apimodel.description=Model holding conditions related to read options to be used in an Org Map multi read request.
# v1.0.findlocationsmultireadoptions.apimodelproperty.includeOrgPathDetails.description=Indicate if org path details are required.
# v1.0.findlocationsmultireadoptions.apimodelproperty.modifiedSince.description=Indicate if modified time details are required.