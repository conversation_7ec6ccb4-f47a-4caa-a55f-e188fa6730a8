v1.0.genericjobwithguid.apimodel.description=Model for Generic Job.
v1.0.genericjobwithguid.apimodelproperty.id.description=The ID of a generic job.
v1.0.genericjobwithguid.apimodelproperty.effectiveid.description=The revision ID of a generic job.
v1.0.genericjobwithguid.apimodelproperty.name.description=The name of a generic job.
v1.0.genericjobwithguid.apimodelproperty.fullname.description=The full name of a generic job.
v1.0.genericjobwithguid.apimodelproperty.description.description=The description of a generic job.Despite location behavior if null passed on update then empty string will be set.
v1.0.genericjobwithguid.apimodelproperty.effectivedate.description=The effective date of a generic job
v1.0.genericjobwithguid.apimodelproperty.expirationdate.description=The expiration date of a generic job.
v1.0.genericjobwithguid.apimodelproperty.displayorder.description=The display order of a generic job.
v1.0.genericjobwithguid.apimodelproperty.color.description=The color of a generic job.
v1.0.genericjobwithguid.apimodelproperty.code.description=The code of a generic job.
v1.0.genericjobwithguid.apimodelproperty.lastrevision.description=A Boolean indicator of whether or not this is the generic job's last revision.
v1.0.genericjobwithguid.apimodelproperty.firstrevision.description=A Boolean indicator of whether or not this is the generic job's first revision.
v1.0.genericjobwithguid.apimodelproperty.guid.description=The persistent ID of a generic job.
v1.0.genericjobwithguid.apimodelproperty.externalId.description=The external ID of a generic job. If no value is passed during an update, the previous value remains in effect and is taken from the revision with the most recent effective date.
v1.0.genericjobwithguid.apimodelproperty.updatedatetime.description=The date and time of the most recent update to a generic job.
