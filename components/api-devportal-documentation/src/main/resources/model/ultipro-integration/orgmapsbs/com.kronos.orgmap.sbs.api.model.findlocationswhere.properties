v1.0.findlocationswhere.apimodel.description=The Retrieve Locations multi_read where model, containing conditions related to keyword, date, location set, descendants, parent, ancestors or children, and locations in a span.
v1.0.findlocationswhere.apimodelproperty.FindLocationsWhereLocationRefSpan.description=The info to be used for searching using a date span.
v1.0.findlocationswhere.apimodelproperty.FindLocationsWhereDescendantsOfDuring.description=The descendents info to be used for searching during span.
v1.0.findlocationswhere.apimodelproperty.FindLocationsWhereChildrenOf.description=The children info to be used in a searching.