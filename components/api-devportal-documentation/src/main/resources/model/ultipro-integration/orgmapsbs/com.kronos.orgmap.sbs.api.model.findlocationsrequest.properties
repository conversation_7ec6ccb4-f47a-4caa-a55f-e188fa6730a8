# Replaced by duplicate strings in the newer file com.kronos.commonapp.orgmap.rest.impl.model.locations.findlocationsrequest.properties

# v1.0.findlocationsrequest.apimodel.description=Model for an Org Map Find Locations multi read request.
# v1.0.findlocationsrequest.apimodelproperty.findlocationswhere.description=Criterias definition of an Org Map Find Locations request. Only 1 criteria can be specified per request.
# v1.0.findlocationsrequest.apimodelproperty.findlocationsmultireadoptions.description=Criterias definition of an Org Map Find Locations multi read options