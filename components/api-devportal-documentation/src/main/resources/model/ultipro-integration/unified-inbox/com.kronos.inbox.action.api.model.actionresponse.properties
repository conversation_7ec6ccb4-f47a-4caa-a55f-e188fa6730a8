v1.0.actionresponse.apimodel.description=Model for Execute Action Response.
v1.0.actionresponse.apimodelproperty.success.description=Boolean indicating success or failure of execution.
v1.0.actionresponse.apimodelproperty.updatedproperties.description=Update status indicating whether the notification was archived/deleted if execution was success or todo in case of failure.
v1.0.actionresponse.apimodelproperty.responseString.description=The Domain API response in case the include_response parameter was received as true in request.
v1.0.actionresponse.apimodelproperty.statusCode.description=Execute Action API response code.