#Comments for Properties
#Thu Aug 03 17:09:46 IST 2017
v1.0.hoursworkauditforrest.apimodel.description=Base entity containing hours worked audit data.
v1.0.hoursworkauditforrest.apimodelproperty.hoursWorkedId.description=The ID of a specific set of hours worked.
v1.0.hoursworkauditforrest.apimodelproperty.durationInSeconds.description=The duration in seconds of this pay code edit.
v1.0.hoursworkauditforrest.apimodelproperty.endDateTime.description=The end date in iso_local_date_time.
v1.0.hoursworkauditforrest.apimodelproperty.endTimezone.description=The end timezone where the worked shift segment was entered. Normally, this is the default or home timezone for the employee, but the worked shift segment can include a different timezone as necessary.
v1.0.hoursworkauditforrest.apimodelproperty.laborAccountId.description=The ID of a labor account transfer.
v1.0.hoursworkauditforrest.apimodelproperty.orgJobId.description=The ID of an organizational job.
v1.0.hoursworkauditforrest.apimodelproperty.primaryLaborAccountId.description=The ID of primary labor accounts.
v1.0.hoursworkauditforrest.apimodelproperty.primaryOrgJobId.description=The ID of reference to the default/home job for the employee.  
v1.0.hoursworkauditforrest.apimodelproperty.primaryWorkRuleId.description=The ID of reference to the default/home work rule for the employee. 
v1.0.hoursworkauditforrest.apimodelproperty.isProjected.description=A Boolean indicator of whether or not the worked shift is projected (planned to occur in the future). 
v1.0.hoursworkauditforrest.apimodelproperty.scheduleAmountTypeId.description=The ID of a schedule amount type.
v1.0.hoursworkauditforrest.apimodelproperty.startDateTime.description=The start date in iso_local_date_time.
v1.0.hoursworkauditforrest.apimodelproperty.startTimezone.description=The start timezone where the hours worked was entered.
v1.0.hoursworkauditforrest.apimodelproperty.timeEntitySegmentTypeId.description=The ID of a time entity segment type.
v1.0.hoursworkauditforrest.apimodelproperty.isTransferLaborAccount.description=A Boolean indicator of whether labor account is a transfer.
v1.0.hoursworkauditforrest.apimodelproperty.isTransferOrgJob.description=A Boolean indicator of whether the job is a transfer.
v1.0.hoursworkauditforrest.apimodelproperty.isTransferWorkRule.description=A Boolean indicator of whether or not the work rule is a transfer.
v1.0.hoursworkauditforrest.apimodelproperty.isUserEnteredLaborAccount.description=A Boolean indicator of whether or not a labor category was entered by the user.
v1.0.hoursworkauditforrest.apimodelproperty.userEnteredLaborAccountId.description=The ID of a labor account entered by the user.
v1.0.hoursworkauditforrest.apimodelproperty.isUserEnteredOrgJob.description=A Boolean indicator of whether or not a business structure job was entered by the user.
v1.0.hoursworkauditforrest.apimodelproperty.isUserEnteredWorkRule.description=A Boolean indicator of whether or not the punch is generated from a user-entered work rule. 
v1.0.hoursworkauditforrest.apimodelproperty.workRuleId.description=The ID of a work rule.
v1.0.hoursworkauditforrest.apimodelproperty.workItemId.description=The ID of a work item.
v1.0.hoursworkauditforrest.apimodelproperty.isCommentsAvailable.description=A Boolean indicator of whether or not a Comment is associated with a hours worked.
v1.0.hoursworkauditforrest.apimodelproperty.datasourceId.description=The ID of the data source, if one exists. Normally, this indicates that the context object came from a different source, such as a clock, device, or external data source, import, or interface.
