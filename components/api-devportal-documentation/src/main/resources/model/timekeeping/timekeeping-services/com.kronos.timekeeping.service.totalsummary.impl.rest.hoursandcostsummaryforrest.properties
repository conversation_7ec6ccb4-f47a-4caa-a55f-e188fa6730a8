#Comments for Properties
#Thu Mar 23 17:09:46 IST 2017
v1.0.hoursandcostsummaryforrest.apimodel.description=An entity encapsulating hours and wages from a shift.
v1.0.hoursandcostsummaryforrest.apimodelproperty.id.description=The ID of the hours and cost summary item.
v1.0.hoursandcostsummaryforrest.apimodelproperty.durationinhours.description=The duration of the shift in hours.
v1.0.hoursandcostsummaryforrest.apimodelproperty.wages.description=The wage amount.
v1.0.hoursandcostsummaryforrest.apimodelproperty.timeitemid.description=The ID of the shift.
v1.0.hoursandcostsummaryforrest.apimodelproperty.isfromshift.description=A Boolean indicator of whether or not this data is from a shift.
v1.0.hoursandcostsummaryforrest.apimodelproperty.amounttype.description=An enumeration of the type of the amount.