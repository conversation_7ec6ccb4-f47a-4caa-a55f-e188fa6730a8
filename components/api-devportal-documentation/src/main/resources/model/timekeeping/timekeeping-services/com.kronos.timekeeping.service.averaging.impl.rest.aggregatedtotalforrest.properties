#Comments for Properties
#May 4, 2017
v1.0.aggregatedtotalforrest.apimodel.description=A list of aggregated totals objects.
v1.0.aggregatedtotalforrest.apimodelproperty.employee.description=A reference to the employee object. 
v1.0.aggregatedtotalforrest.apimodelproperty.datepatterntype.description=The date pattern type in string form.
v1.0.aggregatedtotalforrest.apimodelproperty.startdate.description=The start date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.aggregatedtotalforrest.apimodelproperty.enddate.description=The end date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.aggregatedtotalforrest.apimodelproperty.avgreportingperiod.description=The average reporting period in string form.
v1.0.aggregatedtotalforrest.apimodelproperty.amountfrom.description=The amount from value in string form.
v1.0.aggregatedtotalforrest.apimodelproperty.amount.description=The amount of time of a worked shift.
v1.0.aggregatedtotalforrest.apimodelproperty.averagingactualamount.description=The number of hours worked so far during the averaging reporting period in BigDecimal form.
v1.0.aggregatedtotalforrest.apimodelproperty.averagingtargetamount.description=The target number of hours to be worked in the averaging reporting period in BigDecimal form.
v1.0.aggregatedtotalforrest.apimodelproperty.averagingprojectedhours.description=The number of projected hours worked for the averaging reporting period in BigDecimal form. This value is the sum of actual hours worked to date plus scheduled hours in the future.
v1.0.aggregatedtotalforrest.apimodelproperty.averagingvariance.description=The difference between the target number of hours and the actual hours worked during the reporting period in BigDecimal form.
v1.0.aggregatedtotalforrest.apimodelproperty.actualthroughselecteddayhours.description=The actual totals through a selected date and hour (rather than through the end of the averaging period) in BigDecimal form.
v1.0.aggregatedtotalforrest.apimodelproperty.targetthroughselecteddayhours.description=The target value and the cumulative value through the day selected in BigDecimal form. This value is the sum of the target amounts for each day from the start of the period through the selected date. This metric requires that employee work hour definitions be defined either from Contract or from Schedule and not by using a Specified Amount.
v1.0.aggregatedtotalforrest.apimodelproperty.actualthroughselecteddayhoursminusaveragingtargethours.description=The reporting periodâs target amount minus the actual amount worked through a selected date in BigDecimal form.
v1.0.aggregatedtotalforrest.apimodelproperty.averagingtargethoursminusactualthroughselecteddayhours.description=The target number of hours in the averaging period minus the actual number of hours worked through a user-specified date (rather than through the entire period) in BigDecimal form.
v1.0.aggregatedtotalforrest.apimodelproperty.varianceactualhoursminustargetthroughselecteddayhours.description=The difference between the Actual hours worked and Target hours worked through a user-specified date in BigDecimal form.
v1.0.aggregatedtotalforrest.apimodelproperty.varianceprojectedhoursminustargethours.description=The difference between the Projected and Target hours worked during the reporting period in BigDecimal form.
v1.0.aggregatedtotalforrest.apimodelproperty.variancetargethoursminusactualthroughselecteddayhours.description=The difference between the Target hours and Actual hours worked through a user-specified date in BigDecimal form.
v1.0.aggregatedtotalforrest.apimodelproperty.isfromcorrection.description=A Boolean indicator of whether or not an aggregated total is the result of a correction.
v1.0.aggregatedtotalforrest.apimodelproperty.position.description=A reference to the position object.