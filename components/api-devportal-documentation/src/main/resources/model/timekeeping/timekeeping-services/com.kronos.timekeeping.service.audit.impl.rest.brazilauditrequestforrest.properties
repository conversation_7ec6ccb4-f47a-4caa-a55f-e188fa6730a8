#Comments for Properties
v1.0.brazilauditrequestforrest.apimodel.description=Brazil audit request for REST information.
v1.0.brazilauditrequestforrest.apimodelproperty.employees.description=A list of references for the employees object.
v1.0.brazilauditrequestforrest.apimodelproperty.startdatetime.description=The start date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.brazilauditrequestforrest.apimodelproperty.enddatetime.description=The end date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.brazilauditrequestforrest.apimodelproperty.audittype.description=The type of the audit entities to be retrieved.
v1.0.brazilauditrequestforrest.apimodelproperty.formatdatasource.description=A Boolean indicator of whether or not to format the data source as a readable display value. For example, when set to true, the output <code>datasource</code> qualifier of a Dataview, such as TIMECARD_EDITOR, is replaced by "Timecard Editor" when the Dataview is built.
