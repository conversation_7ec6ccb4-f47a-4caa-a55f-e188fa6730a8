v1.0.baseattestationdto.apimodel.description=The base Attestation entity model.
v1.0.baseattestationdto.apimodelproperty.name.description=The name of an Attestation entity.
v1.0.baseattestationdto.apimodelproperty.deleted.description=A Boolean indicator of whether or not this Attestation entity has been deleted.

v1.0.attestationworkflowdto.apimodel.description=The Attestation Workflow model.

v1.0.attestationconditiondto.apimodel.description=The Attestation condition model.
v1.0.attestationconditiondto.apimodelproperty.type.description=The type of an Attestation condition. Possible types: <ul><li>1 - Always</li><li>2 - Minutes Worked</li><li>3 - Minutes Since Last Punch</li><li>4 - Punch Status</li><li>5 - Unapproved Timecard</li><li>6 - Missing Punches</li><li>7 - Combined Conditions</li></ul>
v1.0.attestationconditiondto.apimodelproperty.operator.description=The operator of an Attestation condition.
v1.0.attestationconditiondto.apimodelproperty.rangeValueStart.description=The range start value of an Attestation condition.
v1.0.attestationconditiondto.apimodelproperty.rangeValueEnd.description=The range end value of an Attestation condition.
v1.0.attestationconditiondto.apimodelproperty.singleValue.description=The value of an Attestation condition.
v1.0.attestationconditiondto.apimodelproperty.trueOnNoStartPunch.description=A Boolean indicator of whether or not no shift start punch is found. When false, a start punch was found.
v1.0.attestationconditiondto.apimodelproperty.excludeBreaksFromMinutesWorked.description=A Boolean indicator of whether or not to exclude breaks from minutes worked.
v1.0.attestationconditiondto.apimodelproperty.condition.list=The list of Attestation combined conditions of an Attestation condition.

v1.0.attestationcomplexconditiondto.apimodel.description=The Attestation combined condition model.
v1.0.attestationcomplexconditiondto.apimodelproperty.ordernumber.description=The order number of an Attestation combined condition.
v1.0.attestationcomplexconditiondto.apimodelproperty.logicaloperator.description=The logical operator of an Attestation combined condition.
v1.0.attestationcomplexconditiondto.apimodelproperty.isnegativeoperator.description=A Boolean indicator is negative operator of an Attestation combined condition.
v1.0.attestationcomplexconditiondto.apimodelproperty.attestationcondition.description=The Attestation condition of an Attestation combined condition.
v1.0.attestationcomplexconditiondto.apimodelproperty.attestationconditionsdmkey.description=The Attestation condition sdm key of an Attestation combined condition.

v1.0.abstractattestationdto.apimodel.description=The abstract Attestation model.
v1.0.abstractattestationdto.apimodelproperty.id.description=The ID of an Abstract Attestation entity.
v1.0.abstractattestationdto.apimodelproperty.versioncount.description=The version count of an Abstract Attestation entity.

v1.0.attestationassignmentdto.apimodel.description=The Attestation assignment model.
v1.0.attestationassignmentdto.apimodelproperty.attestationButton=The Attestation button of an Attestation assignment.
v1.0.attestationassignmentdto.apimodelproperty.attestationCondition=The Attestation condition of an Attestation assignment.
v1.0.attestationassignmentdto.apimodelproperty.attestationWorkflows=The Punch Button Entry workflow of an Attestation assignment.
v1.0.attestationassignmentdto.apimodelproperty.manualTimeEntryWorkflow=The Manual Time Entry workflow of an Attestation assignment.
v1.0.attestationassignmentdto.apimodelproperty.punchButtonTemplateMapping=The Punch Button template mapping of an Attestation assignment.
v1.0.attestationassignmentdto.apimodelproperty.manualTimeEntryTemplateMapping=The Manual Time Entry template mapping of an Attestation assignment.

v1.0.attestationanswerdto.apimodel.description=The Attestation Answer model.
v1.0.attestationanswerdto.apimodelproperty.modelVariable=A free text field that matches the model configured in the WFL server.
v1.0.attestationanswerdto.apimodelproperty.text=The text of an Attestation Answer.
v1.0.attestationanswerdto.apimodelproperty.actions.list=The list of Attestation Answer Actions.
v1.0.attestationanswerdto.apimodelproperty.offlineAttestation=The offline Attestation.

v1.0.attestationanswerdto.actiontype.apimodel.description=The Attestation Answer Action model.
v1.0.attestationanswerdto.actiontype.apimodelproperty.type=The action type associated with an Attestation answer. Valid values include ADD_PAYCODE, SEND_WORKFLOW_NOTIFICATION, CANCEL_PUNCH, CANCEL_DEDUCTION, and ADD_COMMENT.
v1.0.attestationanswerdto.actiontype.apimodelproperty.modelVariable=A free text field that matches the model configured in the WFL server.
v1.0.attestationanswerdto.actiontype.apimodelproperty.name=The name of a specific action type, such as paycode name, notification name, deduction name, or comment name. This property must be empty for CANCEL_PUNCH.
v1.0.attestationanswerdto.actiontype.apimodelproperty.amount=The amount associated with the specified paycode. This property is only applicable with the ADD_PAYCODE action type.
v1.0.attestationanswerdto.actiontype.apimodelproperty.comment=The comment associated with the specified paycode. This property is only applicable with the ADD_PAYCODE action type.

v1.0.attestationanswerrequest.apimodel.description=The Attestation Answer request model.
v1.0.attestationanswerrequest.apimodelproperty.where.description=The where criteria of an Attestation Answer request.
v1.0.attestationanswerwhere.apimodel.description=The Attestation Answer where model.

v1.0.attestationquestionrequest.apimodel.description=The Attestation Question request model.
v1.0.attestationquestionrequest.apimodelproperty.where.description=The where criteria of an Attestation Question request.
v1.0.attestationquestionwhere.apimodel.description=The Attestation Question where model.

v1.0.attestationtemplatemappingrequest.apimodel.description=The Attestation Template Mapping request model.
v1.0.attestationtemplatemappingrequest.apimodelproperty.where.description=The where criteria of an Attestation Template Mapping request.
v1.0.attestationtemplatemappingwhere.apimodel.description=The Attestation Template Mapping where model.

v1.0.modelref.apimodel.description=The Model Reference model.
v1.0.modelref.apimodelproperty.uuid.description=The UUID of a Model.
v1.0.modelref.apimodelproperty.id.description=The id of a Model.
v1.0.modelref.apimodelproperty.name.description=The name of a Model.

v1.0.modelvariable.apimodel.description=The Model Variable model.
v1.0.modelvariable.apimodelproperty.name.description=The name of a variable.
v1.0.modelvariable.apimodelproperty.value.description=The value of a variable.

v1.0.attestationtemplatemappingdto.apimodel.description=The Attestation Template Mapping model.
v1.0.attestationtemplatemappingdto.apimodelproperty.description.name=The name of an Attestation template mapping.
v1.0.attestationtemplatemappingdto.apimodelproperty.description.description=The description of an Attestation template mapping.
v1.0.attestationtemplatemappingdto.apimodelproperty.model=The Model of an Attestation template mapping.
v1.0.attestationtemplatemappingdto.apimodelproperty.questions.list=The list of Attestation questions of an Attestation template mapping.
v1.0.attestationtemplatemappingdto.apimodelproperty.attributes.set=The set of Model Variable attributes of an Attestation template mapping.

v1.0.attestationbuttondto.apimodel.description=The Attestation button model.
v1.0.attestationbuttondto.apimodelproperty.buttonType.description=<p>The type of an Attestation button. Possible types include:</p><ul><li>1 - Punch</li><li>2 - Punch In</li><li>3 - Punch Out</li><li>4 - New Shift</li><li>5 - Break In</li><li>6 - Workflow Only</li><li>7 - Timecard Action</li></ul>
v1.0.attestationbuttondto.apimodelproperty.attestationButtonSubType.description=<p>The subtype of an Attestation button. You can only specify a subtype when the Attestation button type is passed as 7 - Timecard Action.</p><p>Possible subtypes include:</p><ul><li>1 - Approve</li><li>2 - Save</li></ul>
v1.0.attestationbuttondto.apimodelproperty.attestationButtonDisplayType.description=The display type of an Attestation button. Possible display types:  <ul><li>0 - Web and Clock</li><li>1 - Web</li><li>2 - Clock</li></ul>
v1.0.attestationbuttondto.apimodelproperty.collectBeforeAttestation.description=A Boolean indicator of whether or not to collect before Attestation for an Attestation button.
v1.0.attestationbuttondto.apimodelproperty.deviceTransferAllowed.description=A Boolean indicator of whether or not a transfer is allowed at a device.
v1.0.attestationbuttondto.apimodelproperty.devicePositionSelectionAllowed.description=A Boolean indicator of whether or not Assignments selection is allowed at a device.
v1.0.attestationbuttondto.apimodelproperty.description.description=The description of an Attestation button.
v1.0.attestationbuttondto.apimodelproperty.attestationOfflineWorkflow=Attestation offline workflow.
v1.0.attestationbuttondto.apimodelproperty.timecardApprovalTimeframe=The timeframe used to approve a timecard when an Attestation button initiates a timecard Attestation workflow.
v1.0.attestationbuttondto.apimodelproperty.offlinequestion=The offline question associated with an Attestation button.

v1.0.attestationprofiledto.apimodel.description=Entity containing necessary data to describe an Attestation profile
v1.0.attestationprofiledto.apimodelproperty.attestationAssignment.list=The list of Attestation assignments of an Attestation profile.
v1.0.attestationprofiledto.apimodelproperty.description.description=The description of an Attestation profile.
v1.0.attestationprofiledto.apimodelproperty.attestationassignment.assigned.list=The list of Attestation assignment components of an Attestation profile.

v1.0.attestationdataresponse.apimodel.description=The Attestation daily detail list model.
v1.0.attestationdataresponse.apimodelproperty.attestationDailyDetail.description=The Attestation daily detail object of Attestation Data Response.

v1.0.attestationdatarequest.apimodel.description=The Attestation daily detail list request model.
v1.0.attestationdatarequest.apimodelproperty.select.description=A list of Attestation details to select. Valid values include attestationDailyDetail.
v1.0.attestationdatarequest.apimodelproperty.where.description=The where criteria for the requested Attestation data.
v1.0.attestationdatawhere.apimodel.description=The where criteria for the requested Attestation data.
v1.0.attestationdatawhere.apimodelproperty.employees.description=A list of references to employees.
v1.0.attestationdatawhere.apimodelproperty.dateRange.description=The date range, provided as start date and end date or as a reference to a symbolic period.
v1.0.attestationdaterange.apimodelproperty.startDate.description=The start date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.attestationdaterange.apimodelproperty.endDate.description=The end date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.attestationdaterange.apimodelproperty.symbolicPeriod.description=A symbolic identifier that represents a timeframe or a span of time.
v1.0.attestationdaterange.apimodel.description=The Attestation date range object model.

v1.0.attestationdailydetail.apimodel.description=The Attestation question and answer model.
v1.0.attestationdailydetail.apimodelproperty.id.description=The ID of an Attestation daily detail.
v1.0.attestationdailydetail.apimodelproperty.job.description=The job associated with a submitted punch.
v1.0.attestationdailydetail.apimodelproperty.laborCategory.description=The labor category associated with a submitted punch.
v1.0.attestationdailydetail.apimodelproperty.employee.description=The employee that started an Attestation process.
v1.0.attestationdailydetail.apimodelproperty.punchOverride.description=The override type of the submitted punch.
v1.0.attestationdailydetail.apimodelproperty.isPunchSubmitted.description=A Boolean indicator of whether or not the punch is submitted.
v1.0.attestationdailydetail.apimodelproperty.punchDate.description=The date of the submitted punch in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.attestationdailydetail.apimodelproperty.punchTime.description=The time of the submitted punch in ISO_LOCAL_TIME format (HH:mm:ss.SSS).
v1.0.attestationdailydetail.apimodelproperty.punchTransactionStartTime.description=The start time of the Attestation process in ISO_LOCAL_TIME format (HH:mm:ss.SSS).
v1.0.attestationdailydetail.apimodelproperty.punchTransactionEndTime.description=The end time of the Attestation process in ISO_LOCAL_TIME format (HH:mm:ss.SSS).
v1.0.attestationdailydetail.apimodelproperty.punchDtm.description=The date and time of the submitted punch in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.attestationdailydetail.apimodelproperty.punchTransactionStartDtm.description=The start date and time of the Attestation process in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.attestationdailydetail.apimodelproperty.punchTransactionEndDtm.description=The end date and time of the Attestation process in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.attestationdailydetail.apimodelproperty.punchDuration.description=The time employee spent accomplishing Attestation process.
v1.0.attestationdailydetail.apimodelproperty.questionDuration.description=The time employee spent answering the question.
v1.0.attestationdailydetail.apimodelproperty.questionAnsweredDtm.description=The date and time when the employee submitted an answer for a question in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.attestationdailydetail.apimodelproperty.question.description=The question presented to the employee.
v1.0.attestationdailydetail.apimodelproperty.answer.description=The answer submitted by the employee for the presented question.
v1.0.attestationdailydetail.apimodelproperty.deviceId.description=The device an employee used to punch within the Attestation process.
v1.0.attestationdailydetail.apimodelproperty.manualtimeentryworkflow.description=A Boolean indicator of whether or not a Manual Time Entry Workflow (MTE) is executed and returned. When false, a Punch Button Time Entry (PBTE) workflow is executed and returned.
v1.0.attestationdailydetail.apimodelproperty.manualtimeentrystatus.description=The punch status of Manual Time Entry Workflow.
v1.0.attestationdailydetail.apimodelproperty.punchbutton.description=The button associated with Punch Button or Manual Time Entry Workflow.
v1.0.attestationdailydetail.apimodelproperty.position.description=The position from the workflow attributes of an attestation process or from the attestation_manual_time_entry_process table in Manual Time Entry Attestation case.
v1.0.attestationdailydetail.apimodelproperty.userEnteredPosition.description=A Boolean indicator of whether or not a position is a user-entered position. This property is from the workflow attributes of an attestation process or from the attestation_manual_time_entry_process table in Manual Time Entry Attestation case.
v1.0.attestationdailydetail.apimodelproperty.shortquestion.description=The short Attestation question.
v1.0.attestationdailydetail.apimodelproperty.uniqueid.description=The unique ID of an Attestation daily detail.
v1.0.attestationdailydetail.apimodelproperty.createdByUser.description=The employee who answered the presented question.

v1.0.attestationprofileassignmentdto.apimodel.description=The Attestation Profile Assignment model.
v1.0.attestationprofileassignmentdto.apimodelproperty.effectiveDate.description=The date an assigned Attestation profile becomes effective for a person.
v1.0.attestationprofileassignmentdto.apimodelproperty.expirationDate.description=The date an Attestation profile assignment expires.
v1.0.attestationprofileassignmentdto.apimodelproperty.id.description=The ID of an Attestation profile assignment.
v1.0.attestationprofileassignmentdto.apimodelproperty.personId.description=The ID of a person with an Attestation profile assignment.
v1.0.attestationprofileassignmentdto.apimodelproperty.updatedon.description=The date of the most recent update to an Attestation profile assignment.
v1.0.attestationprofileassignmentdto.apimodelproperty.updateduserid.description=The ID of the user who performed the most recent updated to an Attestation profile assignment.
v1.0.attestationprofileassignmentdto.apimodelproperty.versioncount.description=The version count of an Attestation profile assignment.
v1.0.attestationprofileassignmentdto.apimodelproperty.assignToManagerRole.description=The flag of a manager role assignment.

v1.0.attestationworkflowattributedto.apimodel.description=The Attestation Workflow Attribute model.
v1.0.attestationworkflowattributedto.apimodelproperty.name.description=The name of the Attestation Workflow Attribute.
v1.0.attestationworkflowattributedto.apimodelproperty.value.description=The value of the Attestation Workflow Attribute.

v1.0.attestationprocessrequestdto.apimodel.description=The Attestation process request model.
v1.0.attestationprocessrequestdto.apimodelproperty.employee.description=The employee that requests an Attestation process.
v1.0.attestationprocessrequestdto.apimodelproperty.button.description=The button that is associated with the Attestation process.
v1.0.attestationprocessrequestdto.apimodelproperty.attestationTime.description=The start time of the Attestation process in ISO_LOCAL_TIME format (HH:mm:ss.SSS).
v1.0.attestationprocessrequestdto.apimodelproperty.transferString.description=An ordered, semi-colon separated list of Labor Category Entries and Cost Center.
v1.0.attestationprocessrequestdto.apimodelproperty.device.description=The device an employee used to punch within the Attestation process.
v1.0.attestationprocessrequestdto.apimodelproperty.source.description=Source of the attestation request.
v1.0.attestationprocessrequestdto.apimodelproperty.position.description=A reference to the position assigned to the punch.
v1.0.attestationprocessrequestdto.apimodelproperty.userenteredposition.description=A Boolean indicator of whether or not a position was set by a user. When false, the position was set by the system.
v1.0.attestationprocessrequestdto.apimodelproperty.source.allowablevalues=WFD, UDM
v1.0.attestationprocessrequestdto.apimodelproperty.mode.description=The mode of an Attestation process.
v1.0.attestationprocessrequestdto.apimodelproperty.additionalparams.description=The additional parameters associated with an Attestation process.
v1.0.attestationprocessrequestdto.apimodelproperty.managerattestation.description=A Boolean indicator of whether or not a process is part of a Manager Attestation.

v1.0.attestationprocessresponsedto.apimodel.description=The Attestation process response model.
v1.0.attestationprocessresponsedto.apimodelproperty.attestationProcess.description=The Attestation process object.
v1.0.attestationprocessresponsedto.apimodelproperty.flows.description=A list of Attestation workflows.
v1.0.attestationprocessresponsedto.apimodelproperty.params.description=The map of parameters for an Attestation process.
v1.0.attestationprocessresponsedto.apimodelproperty.manualtimeentryattestationconfigured.description=A Boolean indicator of whether or not Attestation Assignments are configured for a Manual Time Entry workflow.

v1.0.attestationprocesscompleterequestdto.apimodel.description=The Attestation process complete request model.
v1.0.attestationprocesscompleterequestdto.apimodelproperty.attestationprocessid.description=The ID of an Attestation process.

v1.0.attestationofflinerequestdto.apimodel.description=The offline Attestation process request model.
v1.0.attestationofflinerequestdto.apimodelproperty.employee.description=The employee that requests an offline Attestation process.
v1.0.attestationofflinerequestdto.apimodelproperty.button.description=The button that is associated with an offline Attestation process.
v1.0.attestationofflinerequestdto.apimodelproperty.attestationTime.description=The start time of an offline Attestation process in ISO_LOCAL_TIME format (HH:mm:ss.SSS).
v1.0.attestationofflinerequestdto.apimodelproperty.formValueString.description=The request form of an offline Attestation process.
v1.0.attestationofflinerequestdto.apimodelproperty.devicetimezone.description=A reference to a device's timezone.
v1.0.attestationofflinerequestdto.apimodelproperty.device.description=The device an employee used to request an offline Attestation process.
v1.0.attestationofflinerequestdto.apimodelproperty.source.description=The source of an offline Attestation process.
v1.0.attestationofflinerequestdto.apimodelproperty.source.allowablevalues=WFD, UDM

v1.0.attestationtimecardresponsedto.apimodel.description=The Attestation Timecard response model.
v1.0.attestationtimecardresponsedto.apimodelproperty.attestationTimecardResponse.formatedData=The Attestation timecard response formatted data.
v1.0.attestationtimecardresponsedto.apimodelproperty.attestationTimecardResponse.punches=The Attestation timecard response punches.
v1.0.attestationtimecardresponsedto.apimodelproperty.attestationTimecardResponse.paycodes=The Attestation timecard response paycodes.
v1.0.attestationtimecardresponsedto.apimodelproperty.attestationTimecardResponse.estimatedPunchDTM=The Attestation timecard response estimated punch date and time.
v1.0.attestationtimecardresponsedto.apimodelproperty.attestationTimecardResponse.hoursWorked=The Attestation timecard response hours worked.

v1.0.attestationconditiondto.apimodelproperty.periodicConditionMinuteType.description=A Boolean indicator of whether or not to display the periodic condition day type in minutes. If false, the day type is displayed in days.
v1.0.attestationconditiondto.apimodelproperty.periodicConditionValue.description=The value of an Attestation periodic condition.
v1.0.attestationconditiondto.apimodelproperty.completedTransaction.description=A Boolean indicator of whether or not to ignore incomplete or timed-out transactions.
v1.0.attestationconditiondto.apimodelproperty.periodicQuestion.description=The question text that is displayed on the associated form.
v1.0.attestationconditiondto.apimodelproperty.periodicPositiveValue.description=The positive answer of the question posed. This value should represent the full name of the question exactly as it appears on the form.

v1.0.attestationconditiondto.apimodelproperty.minutesBeforePunch.description=The margin before punch in minutes.
v1.0.attestationconditiondto.apimodelproperty.minutesAfterPunch.description=The margin after punch in minutes.
v1.0.attestationconditiondto.apimodelproperty.minutesBeforeScheduledShiftStart.description=The number of minutes to look for a punch before scheduled shift start.
v1.0.attestationconditiondto.apimodelproperty.minutesAfterScheduledShiftStart.description=The number of minutes to look for a punch after scheduled shift start.
v1.0.attestationconditiondto.apimodelproperty.minutesBeforeScheduledShiftEnd.description=The number of minutes to look for a punch before scheduled shift end.
v1.0.attestationconditiondto.apimodelproperty.minutesAfterScheduledShiftEnd.description=The number of minutes to look for a punch after scheduled shift end.
v1.0.attestationconditiondto.apimodelproperty.conditionNotEvaluated.description=A Boolean value that used for attestation evaluation when geolocation data not transferred in request or Known Place is no longer exist.
v1.0.attestationconditiondto.apimodelproperty.knownPlace.description=The Known Place of attestation condition of Known Places type.
v1.0.attestationconditiondto.apimodelproperty.extendedRadius.description=The additional radius value to check that passed geolocation coordinated are within Known Place associated with attestation condition.
v1.0.attestationconditiondto.apimodelproperty.startOfRange.description=The minutes after shift start to define the start of time-frame from shift start to look for punches.
v1.0.attestationconditiondto.apimodelproperty.endOfRange.description=The minutes after shift start to define the end of time-frame from shift start to look for punches.
v1.0.attestationconditiondto.apimodelproperty.comment.description=The comment of attestation condition of type Shift Punch.
v1.0.attestationconditiondto.apimodelproperty.maxShiftLength.description=The maximum shift length in minutes for attestation condition of type Scheduled Shift.
v1.0.attestationconditiondto.apimodelproperty.minShiftLength.description=The minimum shift length in minutes for attestation condition of type Scheduled Shift.
v1.0.attestationconditiondto.apimodelproperty.noShiftInProgress.description=A Boolean indicator of whether or not to evaluate both not in progress and in progress shifts or only in progress shifts for attestation condition of Scheduled Shift type and Punches against scheduled shift.
v1.0.attestationconditiondto.apimodelproperty.noScheduledShiftFound.description=A Boolean value to evaluate attestation when attestation condition of type Scheduled Shift and Shift Length operator and no scheduled shift found for work shift.
v1.0.attestationconditiondto.apimodelproperty.includeCompletedShifts.description=A Boolean indicator of whether or not to take completed shifts into consideration during minutes worked attestation condition calculation.


v1.0.abstractattestationformdto.apimodel.description=The Abstract Attestation Form model.
v1.0.abstractattestationformdto.apimodelproperty.attestationprocessid.description=The ID of an Attestation Process.
v1.0.abstractattestationformdto.apimodelproperty.processinstanceid.description=The ID of a Process Instance.
v1.0.abstractattestationformdto.apimodelproperty.processdefinition.description=The definition of a Process.

v1.0.attestationdisplayformdto.apimodel.description=The Attestation Display Form model.
v1.0.attestationdisplayformdto.apimodelproperty.attestationdata.description=The list of Attestation display data.
v1.0.attestationdisplayformdto.apimodelproperty.displaydatetime.description=The date and time of the Attestation Display Form in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss).

v1.0.attestationformtextdto.apimodel.description=The Attestation Form Text model.
v1.0.attestationformtextdto.apimodelproperty.id.description=The ID of an Attestation Form Text.
v1.0.attestationformtextdto.apimodelproperty.text.description=The text of an Attestation Form Text.

v1.0.attestationdisplaydatadto.apimodel.description=The Attestation Display Data model.
v1.0.attestationdisplaydatadto.apimodelproperty.question.description=The Attestation Form Text with question ID and text.

v1.0.attestationsubmitdatadto.apimodel.description=The Attestation Submit Data model.
v1.0.attestationsubmitdatadto.apimodelproperty.response.description=The Attestation Form Text with response ID and text.

v1.0.attestationsubmitformdto.apimodel.description=The Attestation Submit Form model.
v1.0.attestationsubmitformdto.apimodelproperty.attestationdata.description=The list of Attestation Submit Data.
v1.0.attestationsubmitformdto.apimodelproperty.responsedatetime.description=The date and time of the Attestation Submit Form in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss).

v1.0.attestationmanualentryprocessrequestdto.apimodel.description=The Retrieve Manual Time Entry Workflows request payload.
v1.0.attestationmanualentryprocessrequestdto.apimodelproperty.employee.description=A reference to the employee object.
v1.0.attestationmanualentryprocessrequestdto.apimodelproperty.attestationprocessid.description=The ID of an Attestation process.
v1.0.attestationmanualentryprocessrequestdto.apimodelproperty.daterange.description=The date range, provided as start date and end date or as a reference to a symbolic period.
v1.0.attestationmanualentryprocessrequestdto.apimodelproperty.punchselections.description=A list of Attestation Punch Selection objects.

v1.0.attestationmanualentryprocessresponsedto.apimodel.description=The Retrieve Manual Time Entry Workflows response model.
v1.0.attestationmanualentryprocessresponsedto.apimodelproperty.punchdtm.description=The date and time of a punch in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.attestationmanualentryprocessresponsedto.apimodelproperty.timezone.description=The timezone of a punch.
v1.0.attestationmanualentryprocessresponsedto.apimodelproperty.attestationworkflows.description=A list of Attestation workflows.
v1.0.attestationmanualentryprocessresponsedto.apimodelproperty.params.description=The map of parameters to execute Manual Time Entry workflows.

v1.0.attestationpunchselectiondto.apimodel.description=The Attestation Punch model.
v1.0.attestationpunchselectiondto.apimodelproperty.attestationbutton.description=The Attestation button that is assigned to an employeeâs profile.
v1.0.attestationpunchselectiondto.apimodelproperty.punchdtm.description=The date and time of a punch in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.attestationpunchselectiondto.apimodelproperty.timezone.description=The timezone of a punch.
v1.0.attestationpunchselectiondto.apimodelproperty.typeoverride.description=A reference to the override type of a punch. Possible override types include the following `qualifier` (id) pairs: `Break In` (1), `In Punch` (2), `New Shift` (3), and `Out Punch` (4). 

v1.0.attestationpunchselectiondto.apimodelproperty.position.description=A reference to the position assigned to the punch.
v1.0.attestationpunchselectiondto.apimodelproperty.userenteredposition.description=A Boolean indicator of whether or not a position was set by a user. When false, the position was set by the system.
v1.0.attestationpunchselectiondto.apimodelproperty.transferstring.description=An ordered, semi-colon separated list of Labor Category Entries and Cost Center.
