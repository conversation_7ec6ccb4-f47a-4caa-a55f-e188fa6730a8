v1.0.countstowardsovertime.apimodel.overtime.description=The Overtime Rule advanced model.
v1.0.countstowardsovertime.overtimepreprocessing.apimodelproperty.overtimerules.description=A Boolean indicator of whether or not overtime preprocessing occurs.
v1.0.countstowardsovertime.overtimeallocation.apimodelproperty.overtimerules.description=The overtime allocation object.
v1.0.countstowardsovertime.limitchargeordertype.apimodelproperty.overtimerules.description=The overtime allocation charge order type.
v1.0.countstowardsovertime.paycodesconfiguredtocounttowardsot.apimodelproperty.overtimerules.description=A Boolean indicator of whether or not paycodes are configured to count towards overtime (OT).
v1.0.countstowardsovertime.selectedpaycodes.apimodelproperty.overtimerules.description=An array of selected paycodes.
v1.0.countstowardsovertime.usecontributingpaycodes.apimodelproperty.overtimerules.description=A Boolean indicator of whether or not to use contributing paycodes.
v1.0.countstowardsovertime.useselectedpaycodes.apimodelproperty.overtimerules.description=A Boolean indicator of whether or not to use selected paycodes.
v1.0.countstowardsovertime.contributingpaycodename.apimodelproperty.overtimerules.description=A reference to the contributing paycode.