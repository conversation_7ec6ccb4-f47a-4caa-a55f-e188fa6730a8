#Comments for Properties
#Thu Apr 20 16:18:22 IST 2017
v1.0.adjustmentruleversion.apimodel.description=Adjustment Rule Version information.
v1.0.adjustmentruleversion.apimodelproperty.versionId.description=The unique ID of an adjustment rule version.
v1.0.adjustmentruleversion.apimodelproperty.description.description=A simple description of the adjustment rule version (revision).
v1.0.adjustmentruleversion.apimodelproperty.expirationdate.description=The end date of the version (revision).
v1.0.adjustmentruleversion.apimodelproperty.effectivedate.description=The date on which the version (revision) begins.
v1.0.adjustmentruleversion.apimodelproperty.triggers.description=Triggers in a version (revision) are used to compare against a total. If the trigger matches, the total is used to allocate a specific pay code amount.
