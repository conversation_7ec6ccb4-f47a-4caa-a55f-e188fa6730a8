v1.0.effectiveaccrualpolicies.apimodel.description=Entity containing necessary data to describe effective accrual policies.
v1.0.effectiveaccrualpolicies.apimodelproperty.effectivedate.description=The effective date of an accrual policy in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.effectiveaccrualpolicies.apimodelproperty.expirationdate.description=The expiration date of an accrual policy in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.effectiveaccrualpolicies.apimodelproperty.adjstbalanceonftechange.description=A Boolean indicator of whether or not to adjust an accrual balance when full time employee (FTE) status changes.
v1.0.effectiveaccrualpolicies.apimodelproperty.overdrafts.description=The past or present overdrafts of an accrual policy.
v1.0.effectiveaccrualpolicies.apimodelproperty.takings.description=The takings of an accrual policy.
v1.0.effectiveaccrualpolicies.apimodelproperty.grants.description=The grants of an accrual policy.
v1.0.effectiveaccrualpolicies.apimodelproperty.limits.description=The limits of an accrual policy.
v1.0.effectiveaccrualpolicies.apimodelproperty.probationperiod.description=The probation period of an accrual policy.
v1.0.effectiveaccrualpolicies.apimodelproperty.accrualcode.description=The accrual code of an accrual policy.
v1.0.effectiveaccrualpolicies.apimodelproperty.lengthofservicereference.description=A reference to the length of service of an accrual policy.
v1.0.effectiveaccrualpolicies.apimodelproperty.accrualforoverflow.description=The accrual for overflow of an accrual policy.
