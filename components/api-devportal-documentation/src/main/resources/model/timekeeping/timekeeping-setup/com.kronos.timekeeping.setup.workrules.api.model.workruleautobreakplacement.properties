v1.0.workruleautobreakplacement.apimodel.workrules.description=The work rule auto break placement object.
v1.0.workrule.apimodelproperty.workruleautobreakplacement.copyfromexistingworkrule.description=A Boolean indicator of whether or not to copy from an existing work rule.
v1.0.workrule.apimodelproperty.workruleautobreakplacement.minimumtimebetweenstartofshiftandfirstbreak.description=The minimum time between start of shift and first break.
v1.0.workrule.apimodelproperty.workruleautobreakplacement.minimumtimebetweenendofbreakandshiftend.description=The minimum time between first break and end of shift.
v1.0.workrule.apimodelproperty.workruleautobreakplacement.minimumtimebetweenbreaks.description=The minimum time between breaks.
v1.0.workrule.apimodelproperty.workruleautobreakplacement.shiftlenghtstoexclude.description=The shift lengths to exclude.
v1.0.workrule.apimodelproperty.workruleautobreakplacement.newshiftlength.description=The new shift length.