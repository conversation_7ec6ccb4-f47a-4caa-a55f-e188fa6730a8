v1.0.general.apimodel.workrules.description=The work rule general object.
v1.0.workrule.apimodelproperty.general.breaks.description=The breaks associated with a work rule.
v1.0.workrule.apimodelproperty.general.deducts.description=The deductions associated with a work rule.
v1.0.workrule.apimodelproperty.general.corehours.description=The core hours for the general section.
v1.0.workrule.apimodelproperty.general.roundrule.description=The round rule for the general section.
v1.0.workrule.apimodelproperty.general.shiftguarantee.description=The shift guarantee for the general section.
v1.0.workrule.apimodelproperty.general.autoresolvedexceptionrule.description=The auto-resolved exception rule for the general section.
v1.0.workrule.apimodelproperty.general.exception.description=The exception for the general section.
v1.0.workrule.apimodelproperty.general.callinrule.description=The call-in rule for the general section.
v1.0.workrule.apimodelproperty.general.daydivideoverride.description=The day divide override for the general section.
v1.0.workrule.apimodelproperty.general.unapprovedpaycodeid.description=The unapproved pay code ID for the general section.
v1.0.workrule.apimodelproperty.general.deniedpaycodeid.description=The denied pay code ID for the general section.
v1.0.workrule.apimodelproperty.general.combinedwagespaycode.description=The combined wages pay code for weighted average adjustment for the general section.
v1.0.workrule.apimodelproperty.general.combinedhourspaycode.description=The combined hours pay code for weighted average adjustment for the general section.
v1.0.workrule.apimodelproperty.general.includeminimumwagesadjustment.description=A Boolean indicator of whether or not to include the minimum wage adjustment for the weighted average adjustment for the general section.
