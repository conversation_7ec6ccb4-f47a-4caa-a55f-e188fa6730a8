v1.0.key.apimodel.description=Key with ID and name for the Metadata Driven User Interface (MDUI).
v1.0.key.apimodelproperty.id.description=Internal ID of the entity.
v1.0.key.apimodelproperty.name.description=Displayed name of the entity.
v1.0.key.apimodelproperty.key.description=The name of the key entity.
v1.0.sdmkey.apimodel.description=An object which serves as a unique identifier using two elements: sdmKey and name. This object is used by the SDM API. 
v1.0.sdmkey.apimodelproperty.key.description=A unique random ID for entity that is used for SDM integrations.
v1.0.sdmkey.apimodelproperty.name.description=Displayed name of the entity, used in conjunction with the unique random sdmKey ID to uniquely identify objects across a broader context.
v1.0.sdmtimecardsetting.apimodel.description=Model for the Timecard Settings SDM API resource.
v1.0.sdmtimecardsetting.apimodelproperty.sdmkey.description=A unique random ID of the SDMTimecardSetting object, which is a wrapper around TimecardSetting that adds SDM properties.
v1.0.sdmtimecardsetting.apimodelproperty.name.description=The name of the SDM TimecardSetting object.
v1.0.sdmtimecardsetting.apimodelproperty.timecardsetting.description=The original TimecardSetting object from SDM.
v1.0.timecardsetting.apimodel.description=Object container for all properties related to timecard configuration.
v1.0.timecardsetting.apimodelproperty.id.description=The ID of the Timecard Setting.
v1.0.timecardsetting.apimodelproperty.name.description=Displayed name of the Timecard Setting.
v1.0.timecardsetting.apimodelproperty.label.description=A short description of the Timecard Setting.
v1.0.timecardsetting.apimodelproperty.description.description=A full description of the Timecard Setting.
v1.0.timecardsetting.apimodelproperty.timecardType.description=The type of the timecard to which the Timecard Setting applies.
v1.0.timecardsetting.apimodelproperty.canEditFutureTimecard.description=A Boolean indicator of whether or not users can edit their future timecards.
v1.0.timecardsetting.apimodelproperty.numberOfFutureDays.description=The number of days into the future (from today) that users can edit their timecards.
v1.0.timecardsetting.apimodelproperty.includeOvertimes.description=A Boolean indicator of whether or not to display an overtime icon on a timecard.
v1.0.timecardsetting.apimodelproperty.fourPunchRow.description=A Boolean indicator of whether or not to display an additional pair of in and out punch columns on a timecard.
v1.0.timecardsetting.apimodelproperty.defaultPayCodes.description=The IDs of predefined paycodes that are shown on a timecard by default.
v1.0.timecardsetting.apimodelproperty.columnsOrder.description=An ordered collection with IDs of columns to display on a timecard.
v1.0.timecardsetting.apimodelproperty.includeActivityEvent.description=A Boolean indicator of whether or not to display the Activity name and duration on a timecard.
v1.0.timecardsetting.apimodelproperty.includeActivityAllocation.description=A Boolean indicator of whether or not to display the Activity Allocation on a timecard.
v1.0.timecardsetting.apimodelproperty.includeActivityTotalActual.description=A Boolean indicator of whether or not to display the Activity Total Actual on a timecard.
v1.0.timecardsetting.apimodelproperty.includeActivityTotalAllocated.description=A Boolean indicator of whether or not to display the Activity Total Allocated on a timecard.
v1.0.timecardsetting.apimodelproperty.includeActivityTotalVariance.description=A Boolean indicator of whether or not to display the Activity Total Variance on a timecard.
v1.0.timecardsetting.apimodelproperty.startDayOfWeek.description=The starting day of a week for an hourly timecard.
v1.0.timecardsetting.apimodelproperty.includeSchedule.description=A Boolean indicator of whether or not to include the Schedule column for an hourly timecard.
v1.0.timecardsetting.apimodelproperty.includeTransfer.description=A Boolean indicator of whether or not to include the Transfer column for an hourly timecard.
v1.0.timecardsetting.apimodelproperty.includeDailyTotals.description=A Boolean indicator of whether or not to include the Daily Totals column for an hourly timecard.
v1.0.timecardsetting.apimodelproperty.includeShiftTotals.description=A Boolean indicator of whether or not to include the Shift Totals column for an hourly timecard.
v1.0.timecardsetting.apimodelproperty.includePeriodTotals.description=A Boolean indicator of whether or not to include the Period Totals column for a project timecard.
v1.0.timecardsetting.apimodelproperty.projectViewStartDay.description=The starting day of a week for a project timecard.
v1.0.timecardsetting.apimodelproperty.includeScheduleRow.description=A Boolean indicator of whether or not to include the Schedule row for a project timecard.
v1.0.timecardsetting.apimodelproperty.includeDailyTotalsRow.description=A Boolean indicator of whether or not to include the Daily Totals row for a project timecard.
v1.0.timecardsetting.apimodelproperty.includeTransferColumn.description=A Boolean indicator of whether or not to include the Transfer column for a project timecard.
v1.0.timecardsetting.apimodelproperty.includeRollupTotalsColumn.description=A Boolean indicator of whether or not to include the Week Totals column for an hourly timecard.
v1.0.timecardsetting.apimodelproperty.includeTimeframeTotalsColumn.description=A Boolean indicator of whether or not to include the Timeframe Totals column for a project timecard.
v1.0.timecardsetting.apimodelproperty.rowsOrder.description=An ordered collection providing the IDs of rows to display on a project timecard.
v1.0.timecardsetting.apimodelproperty.payCodeExceptionsShowIn.description=The IDs of exceptions to display on a project timecard.
v1.0.timecardsetting.apimodelproperty.version.description=Version of the Timecard Setting used for optimistic locking.
v1.0.timecardsetting.apimodelproperty.sdmKey.description=SDM key string representation of the Timecard Setting.
v1.0.timecardsetting.apimodelproperty.includeActivityDailyActual.description=A Boolean indicator of whether or not to display the Activity Daily Actual on a timecard.
v1.0.timecardsetting.apimodelproperty.includeActivityDailyAllocated.description=A Boolean indicator of whether or not to display the Activity Daily Allocated on a timecard.
v1.0.timecardsetting.apimodelproperty.includeActivityDailyVariance.description=A Boolean indicator of whether or not to display the Activity Daily Variance on a timecard.
v1.0.timecardsetting.apimodelproperty.orphanAnomalyIndicator.description=A Boolean indicator of whether or not to display the Orphan Anomaly Indicator on a timecard.
v1.0.timecardsetting.apimodelproperty.idleAnomalyIndicator.description=A Boolean indicator of whether or not to display the Idle Anomaly Indicator on a timecard.
v1.0.timecardsetting.apimodelproperty.overAllocationAnomalyIndicator.description=A Boolean indicator of whether or not to display the Allocation Anomaly Indicator on a timecard.
v1.0.timecardsetting.apimodelproperty.concurrencyAnomalyIndicator.description=A Boolean indicator of whether or not to display the Concurrency Anomaly Indicator on a timecard.
v1.0.timecardsetting.apimodelproperty.missingResultsAnomalyIndicator.description=A Boolean indicator of whether or not to display the Missing Results Anomaly Indicator on a timecard.
v1.0.timecardsetting.apimodelproperty.mismatchResultsAnomalyIndicator.description=A Boolean indicator of whether or not to display the Mismatch Results Anomaly Indicator on a timecard.
v1.0.timecardsetting.apimodelproperty.addOnOrder.description=An ordered collection providing the IDs of add-ons to display on a project timecard.
v1.0.timecardsetting.apimodelproperty.autoResolveAnomalyIndicator.description=A Boolean indicator of whether or not to display the Auto-resolve Anomaly Indicator on a timecard.
v1.0.timecardsetting.apimodelproperty.includeShiftDetails.description=A Boolean indicator of whether or not to display shift details ("purple punches") on a timecard.
v1.0.timecardsetting.apimodelproperty.includeShowShiftDetailsButton.description=A Boolean indicator of whether or not to display the Show Shift Details button on a timecard.
v1.0.timecardsetting.apimodelproperty.accrualsAddOnProfile.description=A reference to an accruals add-on profile.
v1.0.timecardsetting.apimodelproperty.workAddOnProfile.description=A reference to a work add-on profile.
v1.0.timecardsetting.apimodelproperty.totalsAddOnProfile.description=A reference to a totals add-on profile.
v1.0.timecardsetting.apimodelproperty.auditsAddOnProfile.description=A reference to an audits add-on profile.
v1.0.timecardsetting.apimodelproperty.historicalCorrectionsAddOnProfile.description=A reference to a historical corrections add-on profile.
v1.0.timecardsetting.apimodelproperty.targetHoursAddOnProfile.description=A reference to a target hours add-on profile.
v1.0.timecardsetting.apimodelproperty.punchset.description=The MDUI punch set related to a Timecard setting.
v1.0.timecardsetting.apimodelproperty.enableEnhancedMultiApprovalShading.description=A Boolean indicator of whether or not to display the Include Enhanced Multi Approval Shading on a timecard.
v1.0.timecardsetting.apimodelproperty.allowroundedpunchtoggle.description=A Boolean indicator of whether or not to allow rounded punches.
v1.0.timecardsetting.apimodelproperty.showinactiveassignmentsintimecard.description=A Boolean indicator of whether or not to show inactive assignments in the timecard.
# v1.0.timecardsetting.apimodelproperty.includePhantomPunches.description=A Boolean indicator of whether or not to break up a worked shift at calendar day midnight when the shift crosses from one calendar day to another.

v1.0.timecardsetting.apimodelproperty.paidCorrectionFilterEnabled.description=A Boolean indicator of whether or not a Paid Corrections filter control is displayed on the Historical Corrections tab. The default value is true.
v1.0.timecardsetting.apimodelproperty.hidePaidCorrection.description=A Boolean indicator of whether or not the default state of the filter enabled by <code>paidCorrectionFilterEnabled</code> is set to hide paid corrections. The default value is false, which reveals both paid and unpaid corrections. 
v1.0.timecardsetting.apimodelproperty.includePhantomPunches.description=A Boolean indicator of whether or not to include phantom punches.

v1.0.timecardsetting.apimodelproperty.idleanomalyindicator.description=A Boolean indicator of whether or not to utilize the idle anomaly indicator.
v1.0.timecardsetting.apimodelproperty.mismatchresultsanomalyindicator.description=A Boolean indicator of whether or not to utilize the mismatch results anomaly indicator.
v1.0.timecardsetting.apimodelproperty.missingresultsanomalyindicator.description=A Boolean indicator of whether or not to utilize the missing results anomaly indicator.
v1.0.timecardsetting.apimodelproperty.overallocationanomalyindicator.description=A Boolean indicator of whether or not to utilize the over-allocation anomaly indicator.
v1.0.timecardsetting.apimodelproperty.autoresolveanomalyindicator.description=A Boolean indicator of whether or not to utilize the auto-resolve anomaly indicator.
v1.0.timecardsetting.apimodelproperty.concurrencyanomalyindicator.description=A Boolean indicator of whether or not to utilize the concurrency anomaly indicator.
v1.0.timecardsetting.apimodelproperty.orphananomalyindicator.description=A Boolean indicator of whether or not to utilize the orphan anomaly indicator.
v1.0.timecardsetting.apimodelproperty.includeactivitydailyactual.description=A Boolean indicator of whether or not to include the Activity Daily Actual column.
v1.0.timecardsetting.apimodelproperty.includeactivitydailyvariance.description=A Boolean indicator of whether or not to include the Activity Daily Variance column.
v1.0.timecardsetting.apimodelproperty.includeactivitydailyallocated.description=A Boolean indicator of whether or not to include the Activity Daily Allocated column.
v1.0.timecardsetting.apimodelproperty.includeactivitysummary.description=A Boolean indicator of whether or not to include the Activity Summary column.
