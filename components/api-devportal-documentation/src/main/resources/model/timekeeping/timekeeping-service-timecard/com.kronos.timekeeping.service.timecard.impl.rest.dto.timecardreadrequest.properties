v1.0.timecardreadrequest.apimodel.description=Entity describing the necessary data for a timecard request.
v1.0.timecardreadrequest.apimodelproperty.select.description=Determines the objects returned in the timecard request response. If a unique resource is not specified in the request, all resources are returned. Possible values include: PROCESSED_SEGMENTS, PROJECTS, PUNCHES, WORKED_SHIFTS, PAYCODE_EDITS, MOVE_AMOUNTS, HOURS_WORKED, HOLIDAY_CREDITS, EXCEPTIONS, EXTENDED_EXCEPTIONS, TOTAL_SUMMARY, DAILY_TOTAL_SUMMARY, ITEM_TOTAL_SUMMARY, TOTALS, SIG<PERSON>OFF,REMOVE_SIGNOFF,APPROVAL,REMOVE_APPROVAL,TOTALIZE,SAVE, HISTORICAL_CORRECTIONS, OVERTIME_SUMMARY, OVERTIME_DETAILS, TIMECARD_LAST_UPDATED_BY_SOMEELSE, OTA, HAS_PENDING_CHANGES, HAS_MOVE_AMOUNTS, <PERSON>SSINGTIME_GROUP, <PERSON><PERSON><PERSON><PERSON><PERSON>ATION_STATUS,  ACTIVITY_SEGMENTS, ACTIVITY_SHIFTS, ACTIVITY_TOTALS
v1.0.timecardreadrequest.apimodelproperty.selectoptions.description=Defines any associated select options requested in the select attribute.
v1.0.timecardreadrequest.apimodelproperty.where.description=Criteria definition of a timecard request. Only one criteria can be specified per request.