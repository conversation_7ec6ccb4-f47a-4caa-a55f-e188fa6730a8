v1.0.activityresultsegment.apimodel.description=Entity describing an Activity Result Segment for an Activity Segment.
v1.0.activityresultsegment.apimodelproperty.id.description=The ID of an Activity Result Segment.
v1.0.activityresultsegment.apimodelproperty.activitysegmentid.description=The ID of an Activity Segment.
v1.0.activityresultsegment.apimodelproperty.fielddefinition.description=A reference to the Field Definition.
v1.0.activityresultsegment.apimodelproperty.resultcode.description=A reference to the Result Code.
v1.0.activityresultsegment.apimodelproperty.textresult.description=The text result for the Activity Result Segment.
v1.0.activityresultsegment.apimodelproperty.numericresult.description=The numeric result for the Activity Result Segment.
v1.0.activityresultsegment.apimodelproperty.decimalresult.description=The decimal result for the Activity Result Segment.
v1.0.activityresultsegment.apimodelproperty.datetimeresult.description=The DateTime result for the Activity Result Segment.
v1.0.activityresultsegment.apimodelproperty.timeresult.description=The time result for the Activity Result Segment.
v1.0.activityresultsegment.apimodelproperty.createddatetime.description=The date and time this record was created in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.activityresultsegment.apimodelproperty.updateddatetime.description=The date and time this record was last updated in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.activityresultsegment.apimodelproperty.deleted.description=A Boolean indicator of whether or not the Activity Result Segment is deleted.
v1.0.activityresultsegment.apimodelproperty.datasourceid.description=The ID of the Data Source.
v1.0.activityresultsegment.apimodelproperty.effectivedatetime.description=The effective date and time associated with an Activity Result Segment in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.activityresultsegment.apimodelproperty.childresultsegments.description=A reference to a list of child Activity Result Segments.