v1.0.note.apimodel.description=Entity describing a Note.
v1.0.note.apimodelproperty.text.description=The plaintext entered for a Note.
v1.0.note.apimodelproperty.timestamp.description=The timestamp associated with a Note.
v1.0.note.apimodelproperty.datasourcedisplayname.description=A string identifier that describes the data source, if one exists. Normally, this indicates that the context object came from a different source, such as a clock or device or an external data source import or interface.
v1.0.note.apimodelproperty.datasourceid.description=The ID of the data source, if one exists. Normally, this indicates that the context object came from a different source, such as a clock, device, or an external data source such as an import or interface.