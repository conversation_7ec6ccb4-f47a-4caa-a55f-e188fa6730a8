v1.0.aggregatedtotal.apimodel.description=Entity containing the necessary data for an aggregated total.
v1.0.aggregatedtotal.apimodelproperty.employee.description=A reference to the employee object. Represents the employee to whom the punch applies. 
v1.0.aggregatedtotal.apimodelproperty.location.description=A reference to the GPS location object which applies to this punch. This indicates where the punch was actually made and is tied to Geofencing operations from mobile devices.
v1.0.aggregatedtotal.apimodelproperty.job.description=A reference to the job object, which is a groupByType.
v1.0.aggregatedtotal.apimodelproperty.costcenter.description=Provides totals based on the cost center groupByType.
v1.0.aggregatedtotal.apimodelproperty.laborcategory.description=A reference to the labor category object, which is a groupByType.
v1.0.aggregatedtotal.apimodelproperty.paycode.description=A reference to the payCode object, which is a groupByType.
v1.0.aggregatedtotal.apimodelproperty.amount.description=The amount of time of a worked shift.
v1.0.aggregatedtotal.apimodelproperty.wages.description=The money amount calculated in totals as wages.
v1.0.aggregatedtotal.apimodelproperty.rate.description=The rate of the correlation between wage and amount.
v1.0.aggregatedtotal.apimodelproperty.jobtransfer.description=A Boolean indicator of whether or not the totals object contains a job transfer. 
v1.0.aggregatedtotal.apimodelproperty.laborcategorytransfer.description=A Boolean indicator of whether or not the totals object contains a labor category. 
v1.0.aggregatedtotal.apimodelproperty.timeitemid.description=The ID of the time item.
v1.0.aggregatedtotal.apimodelproperty.applydate.description=The date to which the totals are applied when totals are derived from more than one day.
v1.0.aggregatedtotal.apimodelproperty.workWeekApplyDateTime.description=The date and time for which the totals are applied in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss). This property is returned when the <code>forWorkWeek</code> request payload Boolean is 'true'.
v1.0.aggregatedtotal.apimodelproperty.payperiodweek.description=A number representing a pay period week.
v1.0.aggregatedtotal.apimodelproperty.payperiodnumber.description=A number representing a pay period.
v1.0.aggregatedtotal.apimodelproperty.timeitemtype.description=A reference to the timeItemType object, which is a rollup type.
v1.0.aggregatedtotal.apimodelproperty.orgjob.description=Reference to the orgjob object.
v1.0.aggregatedtotal.apimodelproperty.amounttype.description=Reference to the AmountType object.
v1.0.aggregatedtotal.apimodelproperty.activity.description=A reference to the activity object.
v1.0.aggregatedtotal.apimodelproperty.amountcurrency.description=A reference to the currency object that contains a currency code along with an amount.
v1.0.aggregatedtotal.apimodelproperty.wagescurrency.description=A reference to the currency object that contains a currency code along with a wages amount.
v1.0.aggregatedtotal.apimodelproperty.originalmoneyamountcurrency.description=A reference to the currency object that contains a currency code along with an original money amount.
v1.0.aggregatedtotal.apimodelproperty.isApprovableByManager.description=A Boolean indicator of whether or not the aggregated total is approvable by the logged-in manager.
v1.0.aggregatedtotal.apimodelproperty.managerApprovalNeeded.description= A Boolean indicator of whether or not there is a total that has not been approved by a manager.
v1.0.aggregatedtotal.apimodelproperty.position.description=A reference to the position associated with an aggregated total.
v1.0.aggregatedtotal.apimodelproperty.originalmoneyamount.description=A number representing the original money amount.
v1.0.aggregatedtotal.apimodelproperty.originaldurationinhours.description=A number representing the original duration in hours.
v1.0.aggregatedtotal.apimodelproperty.id.description=The ID of an aggregated total.
v1.0.aggregatedtotal.apimodelproperty.originaldurationindays.description=A number representing the original duration in days.
v1.0.aggregatedtotal.apimodelproperty.enddatetime.description=The end date and time in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.aggregatedtotal.apimodelproperty.startdatetime.description=The start date and time in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.aggregatedtotal.apimodelproperty.originaldate.description=The original date in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.aggregatedtotal.apimodelproperty.workrule.description=A reference to the work rule associated with an aggregated total.