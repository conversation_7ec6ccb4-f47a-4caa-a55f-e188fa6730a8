v1.0.itemtotalsummary.apimodel.description=Entity containing information regarding an item total summary object.
v1.0.itemtotalsummary.apimodelproperty.employee.description=A reference to the employee object. Represents the employee to whom the punch applies.
v1.0.itemtotalsummary.apimodelproperty.itemid.description=The ID of the time item.
v1.0.itemtotalsummary.apimodelproperty.startapplydate.description=Starting apply date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.itemtotalsummary.apimodelproperty.endapplydate.description=Ending apply date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.itemtotalsummary.apimodelproperty.itemhours.description=The total number of hours for this item within a date range.
v1.0.itemtotalsummary.apimodelproperty.hoursandcostsummaries.description=The hours and cost summaries associated with this item total summary.
v1.0.itemtotalsummary.apimodelproperty.position.description=A reference to the position associated with an item total summary.