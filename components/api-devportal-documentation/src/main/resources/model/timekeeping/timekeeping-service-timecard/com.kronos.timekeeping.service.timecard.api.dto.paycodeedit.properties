v1.0.paycodeedit.apimodel.description=Contextual object describing a pay code edit.
v1.0.paycodeedit.apimodelproperty.employee.description=A reference to the employee assigned to a pay code edit. Null if the pay code edit is open (unassigned).
v1.0.paycodeedit.apimodelproperty.paycode.description=A reference to the pay code associated with a pay code edit.
v1.0.paycodeedit.apimodelproperty.id.description=The ID of a pay code edit.
v1.0.paycodeedit.apimodelproperty.startdatetime.description=The start date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.paycodeedit.apimodelproperty.enddatetime.description=The end date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.paycodeedit.apimodelproperty.durationinhours.description=The duration (in hours) of the pay code edit.
v1.0.paycodeedit.apimodelproperty.durationindays.description=The duration (in days) of the pay code edit.
v1.0.paycodeedit.apimodelproperty.moneyamount.description=The amount (as a decimal value representing money) of the pay code edit.
v1.0.paycodeedit.apimodelproperty.moneyamountcurrency.description=A reference to the currency object that contains a currency code along with a money amount.
v1.0.paycodeedit.apimodelproperty.amounttype.description=The name of the symbolic value used to create this pay code edit. 
v1.0.paycodeedit.apimodelproperty.scheduleamounttype.description=An enumeration of the schedule amount.
v1.0.paycodeedit.apimodelproperty.systemgenerated.description=A Boolean indicator of whether or not the pay code edit was generated by the system.
v1.0.paycodeedit.apimodelproperty.editable.description=A Boolean indicator of whether or not a pay code edit is editable.
v1.0.paycodeedit.apimodelproperty.transfer.description=Provides the organization job object to which the punch associated with the pay code edit applies. Normally, this is only used when the punch is charged against a job other than the default or home job for the employee.  
v1.0.paycodeedit.apimodelproperty.exceptions.description=A list of exception objects associated with a pay code edit.
v1.0.paycodeedit.apimodelproperty.commentsavailable.description=A Boolean indicator of whether or not a Comment is associated with a pay code edit.
v1.0.paycodeedit.apimodelproperty.commentsnotes.description=A reference to a list of Comments. This object can have multiple Comments with multiple Notes for each Comment.
v1.0.paycodeedit.apimodelproperty.itemid.description=The ID of the time item.
v1.0.paycodeedit.apimodelproperty.applydate.description=The date to which a pay code edit applies.
v1.0.paycodeedit.apimodelproperty.laborcategories.description=A reference to the LaborCategories object which contains a list of labor category entries and each entry's associated labor category.
v1.0.paycodeedit.apimodelproperty.costcenter.description=A reference to the cost center of a pay code edit. The <code>userenteredcostcenter</code> property determines whether or not the cost center was entered by the user.
v1.0.paycodeedit.apimodelproperty.editbytype.description=A reference to the type of the user who made the change. Indicates whether the change was made by the employee or by someone else.
v1.0.paycodeedit.apimodelproperty.phantomspans.description=A list of two 2 PhantomSpans for a duration pay code in a day divide. When a duration pay code spans across two days, the shift is split into two phantom spans. The first goes from shift start time to the end of the day. The second goes from start of day to end of shift.
v1.0.paycodeedit.apimodelproperty.overrideAcrrualAmount.description=The amount of override accrual days. Must be a value between -1 to 1.
v1.0.paycodeedit.apimodelproperty.orgjob.description=Provides the organization job object to which the pay code edit applies. Normally, this is the default or home job for the employee.
v1.0.paycodeedit.apimodelproperty.scheduledShiftIds.description=A list of scheduled shift IDs assiciated with pay code edits.
v1.0.paycodeedit.apimodelproperty.datasource.description=A reference to the data source, if one exists. Normally, this indicates that the pay code edit came from a different source, such as a clock, device, or an external data source such as an import or interface.
v1.0.historicalcorrection.apimodelproperty.moneyamountcurrency.description=A reference to the currency object that contains a currency code along with a money amount.
v1.0.paycodeedit.apimodelproperty.isApprovableByManager.description=A Boolean indicator of whether or not the pay code edit item is approvable by the logged-in manager.
v1.0.paycodeedit.apimodelproperty.position.description=A reference to the position assigned to this pay code edit.
v1.0.paycodeedit.apimodelproperty.userEnteredPosition.description=A Boolean indicator of whether or not a position was set by a user. When false, the position was set by the system.
v1.0.timecardchangereportrestdto.apimodelproperty.managercommentnotes.description=A reference to a list of Manager Comments. This object can have multiple Manager Comments with multiple Notes for each Comment.