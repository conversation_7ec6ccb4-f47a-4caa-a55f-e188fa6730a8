v1.0.workedshiftsegment.apimodel.description=Timecard Worked Shift Segment information.
v1.0.workedshiftsegment.apimodelproperty.id.description=The ID of a single segment of a worked shift.
v1.0.workedshiftsegment.apimodelproperty.startdatetime.description=The start date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.workedshiftsegment.apimodelproperty.enddatetime.description=The end date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.workedshiftsegment.apimodelproperty.starttimezoneid.description=The ID of the start timezone where the worked shift segment was entered. Normally, this is the default or home timezone for the employee, but the worked shift segment can include a different timezone as necessary.
v1.0.workedshiftsegment.apimodelproperty.endtimezoneid.description=The ID of the end timezone where the worked shift segment was entered. Normally, this is the default or home timezone for the employee, but the worked shift segment can include a different timezone as necessary.
v1.0.workedshiftsegment.apimodelproperty.startpunch.description=The in punch of a worked shift segment object.
v1.0.workedshiftsegment.apimodelproperty.endpunch.description=The out punch of a worked shift segment object.
v1.0.workedshiftsegment.apimodelproperty.shifttotal.description=Total number of hours for a worked shift segment.