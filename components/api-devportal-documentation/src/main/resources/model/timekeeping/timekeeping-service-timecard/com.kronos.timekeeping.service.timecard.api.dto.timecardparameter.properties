v1.0.timecardparameter.apimodel.description=Timecard Parameter information.
v1.0.timecardparameter.apimodelproperty.employeeid.description=An ID that uniquely identifies an employee. This is not a person number. 
v1.0.timecardparameter.apimodelproperty.employees.description=A list of references for the employees object.
v1.0.timecardparameter.apimodelproperty.startdate.description=The start date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.timecardparameter.apimodelproperty.enddate.description=The end date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.timecardparameter.apimodelproperty.timeframeid.description=A symbolic identifier that represents a timeframe or a span of time. Common values are based on pay or schedule periods such as current pay period or previous schedule period. The format of the string is an enumeration from a discreet list of supported options, such as CURRENT_PAY_PERIOD.
v1.0.timecardparameter.apimodelproperty.employeeids.description=A list of employee IDs from the request.
v1.0.timecardparameter.apimodelproperty.includephantompunches.description=A Boolean indicator of whether or not to include phantom punches.
v1.0.timecardparameter.apimodelproperty.ismanager.description=A Boolean indicator of whether or not the currently logged-in user has the manager role.
v1.0.timecardparameter.apimodelproperty.isreloadcontainer.description=A Boolean indicator of whether or not to reload the container.
v1.0.timecardparameter.apimodelproperty.isoverrideaccrualwarnings.description=A Boolean indicator of whether or not there are override accrual warnings.
v1.0.timecardparameter.apimodelproperty.forWorkWeek.description=A Boolean indicator of whether or not totals (actual, scheduled, or projected) are retrieved by work week. A work week can be Previous Work Week, Current Work Week, or Next Work Week.