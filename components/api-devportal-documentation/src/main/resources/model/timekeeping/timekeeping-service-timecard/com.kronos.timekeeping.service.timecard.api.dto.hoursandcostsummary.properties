v1.0.hoursandcostsummary.apimodel.description=Entity containing information regarding an hours and cost summary object.
v1.0.hoursandcostsummary.apimodelproperty.id.description=The ID of the hours and cost summary object.
v1.0.hoursandcostsummary.apimodelproperty.durationinhours.description=The duration (in hours) of the hours and cost summary.
v1.0.hoursandcostsummary.apimodelproperty.wages.description=The money amount calculated in totals as wages.
v1.0.hoursandcostsummary.apimodelproperty.timeitemid.description=The ID of the time item.
v1.0.hoursandcostsummary.apimodelproperty.isfromshift.description=A Boolean indicator of whether or not the punch is generated from a shift.
v1.0.hoursandcostsummary.apimodelproperty.amounttype.description=An enumeration of the type of the amount. 
v1.0.hoursandcostsummary.apimodelproperty.employee.description=A reference to the employee object. Represents the employee to whom the punch applies.
v1.0.hoursandcostsummary.apimodelproperty.startdatetime.description=The start date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.hoursandcostsummary.apimodelproperty.enddatetime.description=The end date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.hoursandcostsummary.apimodelproperty.applydate.description=The date to which the hours and cost summary applies.
v1.0.hoursandcostsummary.apimodelproperty.durationindays.description=The duration (in days) of the hours and cost summary.
v1.0.hoursandcostsummary.apimodelproperty.wagescurrency.description=A reference to the currency object that contains a currency code along with a wages amount.