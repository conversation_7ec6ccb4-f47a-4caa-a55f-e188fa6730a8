v1.0.workedspan.apimodel.description=Entity describing a span of worked hours--projected, scheduled, and actual.
v1.0.workedspan.apimodelproperty.id.description=The ID of the parent worked shift.
v1.0.workedspan.apimodelproperty.itemid.description=The ID of the parent worked shift.
v1.0.workedspan.apimodelproperty.spanid.description=The ID of the worked span.
v1.0.workedspan.apimodelproperty.startdatetime.description=The start date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.workedspan.apimodelproperty.enddatetime.description=The end date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.workedspan.apimodelproperty.roundedstartdatetime.description=The rounded start date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.workedspan.apimodelproperty.roundedenddatetime.description=The rounded end date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.workedspan.apimodelproperty.starttimezone.description=Start timezone where the worked span was entered. Normally, this is the default or home timezone for the employee, but the worked span can include a different timezone as necessary.
v1.0.workedspan.apimodelproperty.endtimezone.description=The end timezone where the worked span was entered. Normally, this is the default or home timezone for the employee, but the worked span can include a different timezone as necessary.
v1.0.workedspan.apimodelproperty.orgjob.description=Provides the organization job object to which the worked span applies. Normally, this is the default or home job for the employee.
v1.0.workedspan.apimodelproperty.primaryorgjob.description=A reference to the default or home job for the employee.
v1.0.workedspan.apimodelproperty.workrule.description=A reference to the work rule to be applied during calculation of totals resulting from the worked span.
v1.0.workedspan.apimodelproperty.primaryworkrule.description=A reference to the default or home work rule for the employee.
v1.0.workedspan.apimodelproperty.primarylaborcategory.description=A reference to the PrimaryLaborCategory object which contains the primary labor category entries.
v1.0.workedspan.apimodelproperty.isscheduledorgjob.description=A Boolean indicator of whether or not the worked span is generated from a scheduled organizational job.
v1.0.workedspan.apimodelproperty.isscheduledworkrule.description=A Boolean indicator of whether or not the worked span is generated from a scheduled work rule.
v1.0.workedspan.apimodelproperty.isuserenteredorgjob.description=A Boolean indicator of whether or not the worked span is generated from a user-entered organizational job.
v1.0.workedspan.apimodelproperty.istransferorgjob.description=A Boolean indicator of whether or not the worked span is generated from a transfer organizational job.
v1.0.workedspan.apimodelproperty.isuserenteredworkrule.description=A Boolean indicator of whether or not the worked span is generated from a user-entered work rule.
v1.0.workedspan.apimodelproperty.istransferworkrule.description=A Boolean indicator of whether or not the worked span is generated from a transfer work rule.
v1.0.workedspan.apimodelproperty.ordernumber.description=An order number associated with processing work orders that can be configured in a pay rule.
v1.0.workedspan.apimodelproperty.startpunch.description=The in punch of a worked span object.
v1.0.workedspan.apimodelproperty.endpunch.description=The out punch of a worked span object.
v1.0.workedspan.apimodelproperty.isprojected.description=A Boolean indicator of whether or not the punch associated with a worked span is projected (planned to occur in the future).
v1.0.workedspan.apimodelproperty.shifttotal.description=Total number of hours for a worked span.
v1.0.workedspan.apimodelproperty.laboraccountid.description=The labor account ID for a worked span.
v1.0.workedspan.apimodelproperty.laborcategories.description=A reference to the LaborCategories object which contains a list of labor category entries and each entry's associated labor category.
v1.0.workedspan.apimodelproperty.costcenter.description=A reference to the CostCenter object which contains the cost center information and labor account ID for each worked span.
v1.0.workedspan.apimodelproperty.isApprovableByManager.description=A Boolean indicator of whether or not the worked span item is approvable by the logged-in manager.