#Comments for Properties
v1.0.timecardmultireadoptions.apimodel.description=Entity defining all possible criteria by which timecard request results can be filtered.
v1.0.timecardmultireadoptions.apimodelproperty.exceptionoptions.description=An object which provides exception filtering options.
v1.0.timecardmultireadoptions.apimodelproperty.includeKindOfTimeSegments.description=A Boolean indicator of whether or not to include the Actual Kind Of Time in the timecard.
v1.0.timecardmultireadoptions.apimodelproperty.breakSpanAtMidnight.description=A Boolean indicator of whether or not to break a worked shift at calendar day midnight when the shift crosses from one calendar day to another.
v1.0.timecardmultireadoptions.apimodelproperty.showTotalsWithHiddenPaycodes.description=A Boolean indicator of whether or not totals should be shown when they are for for paycodes that have been configured to be hidden. The default value is true.
v1.0.timecardmultireadoptions.apimodelproperty.includePhantomPunches.description=A Boolean indicator of whether or not to include phantom punches. The default value is false.
v1.0.timecardmultireadoptions.apimodelproperty.consolidateScheduleSegments.description=A Boolean indicator of whether or not to consolidate scheduled segments of a worked shift.
v1.0.timecardmultireadoptions.apimodelproperty.forWorkWeek.description=A Boolean indicator of whether or not totals (actual, scheduled, or projected) are retrieved by work week. A work week can be Previous Work Week, Current Work Week, or Next Work Week.
v1.0.timecardmultireadoptions.apimodelproperty.totalsincludecombinedpaycodes.description=A Boolean indicator of whether or not totals include combined paycodes.
v1.0.timecardmultireadoptions.apimodelproperty.combineworkshiftatmidnight.description=A Boolean indicator of whether or not to combine worked shifts at midnight.
v1.0.timecardmultireadoptions.apimodelproperty.totalsRollupBy.description=The criteria by which totals are rolled up. Valid values include ALL, SHIFT, TIMEITEM, DAILY, PERIOD_TO_DATE, ACTIVITY_EVENT, and RAW.
v1.0.timecardmultireadoptions.apimodelproperty.contextDate.description=The date for which or until which totals are calculated in ISO_LOCAL_DATE format (YYYY-MM-DD). Must be within the given start_date and end_date or the time frame represented by a provided symbolic timeframe ID.
v1.0.timecardmultireadoptions.apimodelproperty.retrieveFullLaborCategories.description=A Boolean indicator of whether or not totals include full labor categories. This property only applies to totals and scheduled totals.
v1.0.timecardmultireadoptions.apimodelproperty.returnTrueCostCenterId.description=A Boolean indicator of whether or not the cost center returns a correct ID. This property only applies to totals and scheduled totals.
v1.0.timecardmultireadoptions.apimodelproperty.includeHiddenPayCodeEdits.description=A Boolean indicator of whether or not to include paycode edits for hidden paycodes.
v1.0.timecardmultireadoptions.apimodelproperty.includeUnprocessedPaycodeEdits.description=A Boolean indicator of whether or not to include paycode edits that have not yet been totalized. The default value is false.
v1.0.timecardmultireadoptions.apimodelproperty.hideFlankingDays.description=A Boolean indicator of whether or not to hide flanking days. The default value is false.