v1.0.workholidayedit.apimodel.description=Entity containing information regarding a work holiday edit.
v1.0.workholidayedit.apimodelproperty.employee.description=A reference to the employee object. Represents the employee to whom the work holiday edit applies. 
v1.0.workholidayedit.apimodelproperty.paycode.description=A reference to the pay code associated with a work holiday edit.
v1.0.workholidayedit.apimodelproperty.id.description=The ID of the work holiday edit transaction.
v1.0.workholidayedit.apimodelproperty.startdatetime.description=The start date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.workholidayedit.apimodelproperty.enddatetime.description=The end date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.workholidayedit.apimodelproperty.applydate.description=The date to which a work holiday edit applies.
v1.0.workholidayedit.apimodelproperty.durationinhours.description=The duration (in hours) of the work holiday edit.
v1.0.workholidayedit.apimodelproperty.durationindays.description=The duration (in days) of the work holiday edit.
v1.0.workholidayedit.apimodelproperty.moneyamount.description=The amount (as a decimal value representing money) of the work holiday edit.
v1.0.workholidayedit.apimodelproperty.amounttype.description=An enumeration of the type of the amount.
v1.0.workholidayedit.apimodelproperty.scheduleamounttype.description=An enumeration of the schedule amount.
v1.0.workholidayedit.apimodelproperty.systemgenerated.description=A Boolean indicator of whether or not the work holiday edit was generated by the system.
v1.0.workholidayedit.apimodelproperty.editable.description=A Boolean indicator of whether or not a work holiday edit is editable.
v1.0.workholidayedit.apimodelproperty.transfer.description=Provides the organization job object to which the punch associated with the work holiday edit applies. Normally, this is only used when the punch is charged against a job other than the default or home job for the employee.  
v1.0.workholidayedit.apimodelproperty.exceptions.description=A list of exception objects associated with a work holiday edit.
v1.0.workholidayedit.apimodelproperty.holidaydisplayname.description=A string identifier that describes the holiday.
v1.0.workholidayedit.apimodelproperty.commentsnotes.description=A reference to a list of Comments. This object can have multiple Comments with multiple Notes for each Comment.
v1.0.workholidayedit.apimodelproperty.costcenter.description=A reference to the CostCenter object which contains the cost center information and labor account ID for each work holiday edit.
v1.0.workholidayedit.apimodelproperty.laborcategories.description=A reference to the LaborCategories object which contains a list of labor category entries and each entry's associated labor category.
v1.0.workholidayedit.apimodelproperty.moneyamountcurrency.description=A reference to the currency object that contains a currency code along with a money amount.
v1.0.workholidayedit.apimodelproperty.position.description=A reference to the position assigned to this work holiday edit.
v1.0.workholidayedit.apimodelproperty.userenteredposition.description=A Boolean indicator of whether or not a position was set by a user. When false, the position was set by the system.