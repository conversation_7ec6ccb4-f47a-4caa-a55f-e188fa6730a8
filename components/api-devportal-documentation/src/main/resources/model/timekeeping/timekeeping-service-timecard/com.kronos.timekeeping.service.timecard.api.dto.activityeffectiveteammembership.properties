v1.0.activityeffectiveteammembership.apimodel.description=Entity describing an activity effective team membership.
v1.0.activityeffectiveteammembership.apimodelproperty.id.description=The ID of the activity effective team membership.
v1.0.activityeffectiveteammembership.apimodelproperty.workitemid.description=The Work Item ID associated with this activity effective team membership.
v1.0.activityeffectiveteammembership.apimodelproperty.team.description=The ID of the activity team definition.
v1.0.activityeffectiveteammembership.apimodelproperty.employee.description=A reference to the employee object. Represents the employee to whom an activity effective team membership applies.
v1.0.activityeffectiveteammembership.apimodelproperty.membershipid.description=The ID of activity team membership which associated with this activity effective team membership.
v1.0.activityeffectiveteammembership.apimodelproperty.startdatetime.description=The date and time of the start of the activity effective team membership in ISO_LOCAL_DATE_TIME format (yyyy-MM-dd HH:mm:ss.SSS).
v1.0.activityeffectiveteammembership.apimodelproperty.enddatetime.description=The date and time of the end of the activity effective team membership in ISO_LOCAL_DATE_TIME format (yyyy-MM-dd HH:mm:ss.SSS).
v1.0.activityeffectiveteammembership.apimodelproperty.starttimezone.description=The time zone of the start of the activity effective team membership.
v1.0.activityeffectiveteammembership.apimodelproperty.endtimezone.description=The time zone of the end of the activity effective team membership.