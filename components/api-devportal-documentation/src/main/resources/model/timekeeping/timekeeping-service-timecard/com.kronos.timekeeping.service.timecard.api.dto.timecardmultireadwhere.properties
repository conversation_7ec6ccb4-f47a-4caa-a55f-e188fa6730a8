v1.0.timecardmultireadwhere.apimodel.description=Entity defining the timecard where object which stores employee and Hyperfind data.
v1.0.timecardmultireadwhere.apimodelproperty.employees.description=A list of references for the employees object.
v1.0.timecardmultireadwhere.apimodelproperty.hyperfind.description=The Hyperfind data used for searching.
v1.0.timecardmultireadwhere.apimodelproperty.activity.description=A reference to the desired activity for retrieval of activity data types.
v1.0.timecardmultireadwhere.apimodelproperty.shiftid.description=The ID of the desired shift for retrieval of activity data types.
v1.0.timecardmultireadwhere.apimodelproperty.daterange.description=The date range used for searching.