v1.0.approvalresponseforrest.apimodel.description=Entity representing timecard approval for a single employee and date
v1.0.approvalresponseforrest.apimodelproperty.isemployeeapproved.description=Boolean indicator of whether or not this is an employee approval
v1.0.managerapprovalrequestforrest.apimodelproperty.employee.description=A reference to the employee object. Represents the employee to whom the approval applies.
v1.0.approvalresponseforrest.apimodelproperty.approvingmanager.description=If applicable, an ObjectRef representing the manager who has made this approval
v1.0.approvalresponseforrest.apimodelproperty.approvaldate.description=The date to which this timecard approval applies
v1.0.approvalresponseforrest.apimodelproperty.enteredondtm.description=A LocalDateTime representing the date and time this approval was made
v1.0.approvalresponseforrest.apimodelproperty.employee.description=An ObjectRef representing the subject employee