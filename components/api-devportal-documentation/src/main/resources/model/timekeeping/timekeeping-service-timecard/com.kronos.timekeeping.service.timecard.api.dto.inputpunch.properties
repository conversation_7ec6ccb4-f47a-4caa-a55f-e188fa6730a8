v1.0.inputpunch.apimodel.description=Entity describing an extended punch object for the timecard resource.
v1.0.inputpunch.apimodelproperty.enforcerestriction.description=<p>A Boolean indicator of whether or not to enforce restriction validation regardless of timestamp. Defaults to FALSE.</p><p><strong>Note:</strong> This property has no effect when used with the <strong>Update Timecard as Manager</strong> (<code>POST /v1/timekeeping/timecard</code>) API operation.</p>
