v1.0.calculatetotalsrequest.apimodel.description=Entity containing necessary data for the calculation of totals with respect to historical corrections.
v1.0.calculatetotalsrequest.apimodelproperty.employee.description=A reference to the employee object. Represents the employee to whom the historical correction applies. 
v1.0.calculatetotalsrequest.apimodelproperty.processingdate.description=Processing date in ISO_LOCAL_DATE format (YYYY-MM-DD). This is an optional date used for the calculation of totals.
v1.0.calculatetotalsrequest.apimodelproperty.correctionOverride.description=Specifies historical correction attributes that override default behaviors.
v1.0.calculatetotalsrequest.apimodelproperty.commentsnotes.description=A reference to a list of Comments. This object can have multiple Comments with multiple Notes for each Comment.