v1.0.processedsegment.apimodel.description=Entity describing a Processed Segment object for the timecard resource.
v1.0.processedsegment.apimodelproperty.id.description=The ID of the Processed Segment.
v1.0.processedsegment.apimodelproperty.itemId.description=The Item ID of the Processed Segment.
v1.0.processedsegment.apimodelproperty.employee.description=A reference to the employee object. Represents the employee to whom the Processed Segment applies.
v1.0.processedsegment.apimodelproperty.inProgress.description=A Boolean indicator that shows whether or not a Processed Segment is in progress.
v1.0.processedsegment.apimodelproperty.projected.description=A Boolean indicator that shows whether or not a Processed Segment is projected.
v1.0.processedsegment.apimodelproperty.durationInSeconds.description=Processed Segment duration in seconds.
v1.0.processedsegment.apimodelproperty.moneyAmount.description=Processed Segment money amount.
v1.0.processedsegment.apimodelproperty.durationInDays.description=Processed Segment duration in days.
v1.0.processedsegment.apimodelproperty.startDateTime.description=Processed Segment start date and time.
v1.0.processedsegment.apimodelproperty.endDateTime.description=Processed Segment end date and time.
v1.0.processedsegment.apimodelproperty.roundedStartDateTime.description=Processed Segment rounded start date and time.
v1.0.processedsegment.apimodelproperty.roundedEndDateTime.description=Processed Segment rounded end date and time.
v1.0.processedsegment.apimodelproperty.applyDate.description=Processed Segment apply date.
v1.0.processedsegment.apimodelproperty.paycode.description=The Pay Code of the Processed Segment.
v1.0.processedsegment.apimodelproperty.transfer.description=The Transfer of the Processed Segment.
v1.0.processedsegment.apimodelproperty.orgJob.description=The Organisation Job of the Processed Segment.
v1.0.processedsegment.apimodelproperty.workrule.description=The Work Rule of the Processed Segment.
v1.0.processedsegment.apimodelproperty.laborAccountId.description=The Labor Account Id of the Processed Segment.
v1.0.processedsegment.apimodelproperty.laborCategories.description=The Labor Categories of the Processed Segment.
v1.0.processedsegment.apimodelproperty.costCenter.description=The Cost Center of the Processed Segment.
v1.0.processedsegment.apimodelproperty.orderNumber.description=The Order number of the Processed Segment.
v1.0.processedsegment.apimodelproperty.segmentTypeId.description=The ID of the time entity segment type.
