v1.0.ratrequestcontext.apimodel.description=The request context consumed and processed by Rule Analysis Tool (RAT) service.
v1.0.ratrequestcontext.apimodelproperty.employee.description=A reference to the employee object.
v1.0.ratrequestcontext.apimodelproperty.startdate.description=The start date of the date range used for Rule Analysis Tool (RAT) data selection.
v1.0.ratrequestcontext.apimodelproperty.enddate.description=The end date of the date range used for Rule Analysis Tool (RAT) data selection.
v1.0.ratrequestcontext.apimodelproperty.timeframeid.description=A symbolic identifier that represents a timeframe or a span of time. Common values are based on pay or schedule periods such as current pay period or previous schedule period. The format of the string is an enumeration from a discreet list of supported options, such as CURRENT_PAY_PERIOD.
v1.0.ratrequestcontext.apimodelproperty.ischronologicalorder.description=A Boolean indicator of whether or not sorting is performed in chronological order.

# v1.0.daterange.apimodel.description=Range of dates between Start date and End date