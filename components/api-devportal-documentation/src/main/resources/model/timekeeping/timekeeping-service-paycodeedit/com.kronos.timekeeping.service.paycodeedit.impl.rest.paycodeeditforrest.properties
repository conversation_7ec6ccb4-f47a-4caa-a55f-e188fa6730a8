#Comments for Properties
#Sun Apr 09 21:13:32 IST 2017
v1.0.paycodeeditforrest.apimodel.description=Entity containing returned pay code edit information.
v1.0.paycodeeditforrest.apimodelproperty.employee.description=A reference to the employee object. Represents the employee to whom the pay code edit applies. 
v1.0.paycodeeditforrest.apimodelproperty.paycode.description=A reference to the pay code associated with a pay code edit.
v1.0.paycodeeditforrest.apimodelproperty.id.description=The ID of the pay code edit.
v1.0.paycodeeditforrest.apimodelproperty.startdatetime.description=The start date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.paycodeeditforrest.apimodelproperty.durationinhours.description=The duration (in hours) of the pay code edit.
v1.0.paycodeeditforrest.apimodelproperty.durationindays.description=The duration (in days) of the pay code edit.
v1.0.paycodeeditforrest.apimodelproperty.moneyamount.description=The amount (as a decimal value representing money) of the pay code edit.
v1.0.paycodeeditforrest.apimodelproperty.amounttype.description=The name of the symbolic value used to create this pay code edit.
v1.0.paycodeeditforrest.apimodelproperty.scheduleamounttype.description=An enumeration of the schedule amount. Valid values include AMOUNT_NOT_FROM_SCHEDULE, FULL_DAY_AMOUNT_FROM_SCHEDULE, HALF_DAY_AMOUNT_FROM_SCHEDULE, FULL_DAY_AMOUNT_FROM_CONTRACT, HALF_DAY_AMOUNT_FROM_CONTRACT, FIRST_HALF_DAY_AMOUNT_FROM_CONTRACT, SECOND_HALF_DAY_AMOUNT_FROM_CONTRACT, FULL_DAY_AMOUNT_FROM_PATTERN, FROM_EMPLOYMENT_TERM, FIRST_HALF_DAY_AMOUNT_FROM_SCHEDULE, SECOND_HALF_DAY_AMOUNT_FROM_SCHEDULE, HALF_DAY_AMOUNT_FROM_PATTERN, FIRST_HALF_DAY_AMOUNT_FROM_PATTERN, and SECOND_HALF_DAY_AMOUNT_FROM_PATTERN.
v1.0.paycodeeditforrest.apimodelproperty.systemgenerated.description=A Boolean indicator of whether or not the pay code edit was generated by the system.
v1.0.paycodeeditforrest.apimodelproperty.editable.description=A Boolean indicator of whether or not a pay code edit is editable.
v1.0.paycodeeditforrest.apimodelproperty.transfer.description=Provides the organization job object to which the punch associated with the pay code edit applies. Normally, this is only used when the punch is charged against a job other than the default or home job for the employee.  
v1.0.paycodeeditforrest.apimodelproperty.exceptions.description=A list of exception objects associated with a pay code edit.
v1.0.paycodeeditforrest.apimodelproperty.commentsavailable.description=A Boolean indicator of whether or not a Comment is associated with a pay code edit.
v1.0.paycodeeditforrest.apimodelproperty.commentsnotes.description=A reference to a list of Comments. This object can have multiple Comments with multiple Notes for each Comment. Notes cannot exceed 250 characters in length.
v1.0.paycodeeditforrest.apimodelproperty.itemid.description=The ID of the time item.
v1.0.paycodeeditforrest.apimodelproperty.applydate.description=The date to which a pay code edit applies in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.paycodeeditforrest.apimodelproperty.position.description=A reference to the position assigned to this pay code edit.
v1.0.paycodeeditforrest.apimodelproperty.userEnteredPosition.description=A Boolean indicator of whether or not a position was set by a user. When false, the position was set by the system.