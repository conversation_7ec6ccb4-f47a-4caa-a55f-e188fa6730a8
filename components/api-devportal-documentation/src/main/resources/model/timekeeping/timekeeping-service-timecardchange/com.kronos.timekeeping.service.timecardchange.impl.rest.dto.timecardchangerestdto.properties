v1.0.timecardchangerestdto.apimodel.description=The timecard change model.
v1.0.timecardchangerestdto.apimodelproperty.id.description=The ID of a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.currentstatus.description=The status of a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.elementtype.description=The type of an element of a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.changetype.description=The type of an action of a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.changedatetime.description=The date and time when a timecard change was made in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.timecardchangerestdto.apimodelproperty.transactiondatetime.description=The date and time to which a timecard change applies in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.timecardchangerestdto.apimodelproperty.timezone.description=The time zone of a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.employee.description=A reference to the employee who made a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.punchid.description=The ID of the punch to which a timecard change applies.
v1.0.timecardchangerestdto.apimodelproperty.paycode.description=A reference to a pay code object associated with the pay code edit to which a timecard change applies.
v1.0.timecardchangerestdto.apimodelproperty.startdatetime.description=The start date and time of the pay code edit to which a timecard change applies in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.timecardchangerestdto.apimodelproperty.enddatetime.description=The end date and time of the pay code edit to which a timecard change applies in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.timecardchangerestdto.apimodelproperty.durationinhours.description=The duration (in hours) of the pay code edit to which a timecard change applies.
v1.0.timecardchangerestdto.apimodelproperty.durationindays.description=The duration (in days) of the pay code edit to which a timecard change applies.
v1.0.timecardchangerestdto.apimodelproperty.startdatetimelocalized.description=The start date and time of the activity event.
v1.0.timecardchangerestdto.apimodelproperty.stopdatetimelocalized.description=The stop date and time of the activity event.
v1.0.timecardchangerestdto.apimodelproperty.durationdatetimelocalized.description=The duration (in hours) of the activity event.
v1.0.timecardchangerestdto.apimodelproperty.activitycombinedname.description=The activity combined name of the activity event.
v1.0.timecardchangerestdto.apimodelproperty.ordernum.description=The order number of the activity event.
v1.0.timecardchangerestdto.apimodelproperty.moneyamount.description=The amount (as a decimal value representing money) of the pay code edit to which a timecard change applies.
v1.0.timecardchangerestdto.apimodelproperty.overrideaccrualamount.description=The number of accrual days overridden by the pay code edit to which a timecard change applies.
v1.0.timecardchangerestdto.apimodelproperty.scheduleamounttype.description=The schedule amount type of the pay code edit to which a timecard change applies. Valid values include AMOUNT_NOT_FROM_SCHEDULE, FULL_DAY_AMOUNT_FROM_SCHEDULE, HALF_DAY_AMOUNT_FROM_SCHEDULE, FULL_DAY_AMOUNT_FROM_CONTRACT, HALF_DAY_AMOUNT_FROM_CONTRACT, FIRST_HALF_DAY_AMOUNT_FROM_CONTRACT, SECOND_HALF_DAY_AMOUNT_FROM_CONTRACT, FULL_DAY_AMOUNT_FROM_PATTERN, FROM_EMPLOYMENT_TERM, FIRST_HALF_DAY_AMOUNT_FROM_SCHEDULE, SECOND_HALF_DAY_AMOUNT_FROM_SCHEDULE, HALF_DAY_AMOUNT_FROM_PATTERN, FIRST_HALF_DAY_AMOUNT_FROM_PATTERN, and SECOND_HALF_DAY_AMOUNT_FROM_PATTERN.
v1.0.timecardchangerestdto.apimodelproperty.orgjob.description=The organization job applied to a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.workrule.description=The work rule applied to a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.transferstring.description=An ordered, semi-colon separated list of Labor Category Entries and Cost Center.
v1.0.timecardchangerestdto.apimodelproperty.activitytransfer.description=The transfer of an activity segment associated with a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.overridetype.description=The override type of the punch to which a timecard change applies.
v1.0.timecardchangerestdto.apimodelproperty.punchdtm.description=The date and time of the punch to which a timecard change applies in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.timecardchangerestdto.apimodelproperty.enteredondatetime.description=The "entered on" date and time of the punch to which a timecard change applies in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.timecardchangerestdto.apimodelproperty.iscanceldeduct.description=A Boolean indicator of whether or not to cancel a meal deduction associated with the punch to which a timecard change applies.
v1.0.timecardchangerestdto.apimodelproperty.isexceptionresolved.description=A Boolean indicator of whether or not an exception is resolved for a punch to which a timecard change applies.
v1.0.timecardchangerestdto.apimodelproperty.breakrule.description=The break rule applied to the punch to which a timecard change applies.
v1.0.timecardchangerestdto.apimodelproperty.deductrule.description=The deduct rule applied to the punch to which a timecard change applies.
v1.0.timecardchangerestdto.apimodelproperty.hascomments.description=A Boolean indicator of whether or not a Comment is associated with a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.commentsnotes.description=A reference to a list of Comments associated with a timecard change. This object can have multiple Comments with multiple Notes for each Comment.
v1.0.timecardchangerestdto.apimodelproperty.editbytype.description=A reference to the type of the user who made a timecard change. Indicates whether the change was made by the employee or by someone else.
v1.0.timecardchangerestdto.apimodelproperty.datasource.description=A reference to the data source of the timecard change, if one exists. Normally, this indicates that the context object came from a different source, such as a clock, device, or an external data source such as an import or interface.
v1.0.timecardchangerestdto.apimodelproperty.updatedatetime.description=The date and time of an update to the punch or pay code edit to which a timecard change applies in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.timecardchangerestdto.apimodelproperty.requeststatuschanges.description=The list of status changes of a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.activitysegmentid.description=The ID of an activity segment associated with a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.durationlocalized.description=The duration of the activity event.
v1.0.timecardchangerestdto.apimodelproperty.activityname.description=The name of an activity.
v1.0.timecardchangerestdto.apimodelproperty.resultsegmentscount.description=The number of result segments expressed as an integer.
v1.0.timecardchangerestdto.apimodelproperty.accepted.description=A Boolean indicator of whether or not a Timecard change is accepted.
v1.0.timecardchangerestdto.apimodelproperty.activityTeam.description=A reference to an activity team associated with a timecard change.
v1.0.timecardchangerestdto.apimodelproperty.leavecasecodename.description=The code name of a Leave case.