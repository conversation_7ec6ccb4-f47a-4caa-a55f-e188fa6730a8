#Comments for Properties
v1.0.overtime.approvalsegment.apimodel.description=ApprovalSegment is overtime Review object containing approval/denial of overtime.
v1.0.overtime.approvalsegment.apimodelproperty.applydate.description=ApprovalSegment is counted towards this date.
v1.0.overtime.approvalsegment.apimodelproperty.startdatetime.description=The start date and time for the ApprovalSegment.
v1.0.overtime.approvalsegment.apimodelproperty.enddatetime.description=The end date and time for the ApprovalSegment.
v1.0.overtime.approvalsegment.apimodelproperty.action.description=Overtime review action (approve/deny) for the ApprovalSegment.
v1.0.overtime.approvalsegment.apimodelproperty.employee.description=The employee information associated with an ApprovalSegment.
v1.0.overtime.approvalsegment.apimodelproperty.amount.description=The total duration in seconds from start to end of the ApprovalSegment.
v1.0.overtime.approvalsegment.apimodelproperty.commentsavailable.description=A Boolean indicator of whether or not Comments were included in a created ApprovalSegment.
v1.0.overtime.approvalsegment.apimodelproperty.commentsnotes.description=A reference to a list of Comments included during creation of an ApprovalSegment. This object can have multiple Comments with multiple Notes for each Comment.
v1.0.overtime.approvalsegment.apimodelproperty.reviewerName.description=The user who took the overtime review action (approve/deny).
v1.0.overtime.approvalsegment.apimodelproperty.reviewedDate.description=The date when the overtime review action (approve/deny) occured.