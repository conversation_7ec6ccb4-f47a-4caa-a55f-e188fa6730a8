#Comments for Properties
v1.0.overtime.overtimedetailsegment.apimodel.description=OvertimeDetailSegment has information about a single overtime time window.
v1.0.overtime.overtimedetailsegment.apimodelproperty.startdatetime.value=The start date and time for the overtime event in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.overtime.overtimedetailsegment.apimodelproperty.enddatetime.value=The end date and time for the overtime event in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.overtime.overtimedetailsegment.apimodelproperty.amount.value=The total duration in seconds from start to end of an overtime event.
v1.0.overtime.overtimedetailsegment.apimodelproperty.ruleassignments.value=A list of rule type values towards which overtime is accounted.