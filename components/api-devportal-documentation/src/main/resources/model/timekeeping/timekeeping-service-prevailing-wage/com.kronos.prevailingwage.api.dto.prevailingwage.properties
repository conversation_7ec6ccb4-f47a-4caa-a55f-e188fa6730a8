v1.0.prevailingwage.prevailingwage.apimodel.description=Entity containing necessary data to fetch and update a prevailing wage for prevailing wage setup.
v1.0.prevailingwage.prevailingwage.apimodel.value=The value of a prevailing wage.
v1.0.prevailingwage.prevailingwage.apimodelproperty.job.description=Job details.
v1.0.prevailingwage.prevailingwage.apimodelproperty.costcenter.description=Cost center details.
v1.0.prevailingwage.prevailingwage.apimodelproperty.laborentries.description=labor entries details.
v1.0.prevailingwage.prevailingwage.apimodelproperty.laborcategories.description=labor category details.
v1.0.prevailingwage.prevailingwage.apimodelproperty.rate.description=Rate details.
v1.0.prevailingwage.prevailingwage.apimodelproperty.effectivedate.description=Effective date.
v1.0.prevailingwage.prevailingwage.apimodelproperty.expirationdate.description=Expiration date.
