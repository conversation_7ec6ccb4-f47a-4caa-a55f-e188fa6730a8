v1.0.accrualupdateforrestbulk.apimodel.description=Accrual Update REST model.
v1.0.accrualupdateforrestbulk.apimodelproperty.employee.description=A reference to the employee object.
v1.0.accrualupdateforrestbulk.apimodelproperty.position.description=A reference to the position to which the accrual action is applied.
v1.0.accrualupdateforrestbulk.apimodelproperty.accrualcode.description=A reference to the accrual code object.
v1.0.accrualupdateforrestbulk.apimodelproperty.amounttype.description=An enumeration of the type of the amount. 
v1.0.accrualupdateforrestbulk.apimodelproperty.amount.description=Provides the amount of the accrual update transaction request. This number updates the duration of time of an accrual balance for a given accrual code. Typically this number represent hours or days. This property is logically associated with the AccrualUpdateforRest `amountType`. Normally, the amount is 0 or a positive number, but can be a negative number.
v1.0.accrualupdateforrestbulk.apimodelproperty.effectivedate.description=The effective date of an update in ISO_LOCAL_DATE format (YYYY-MM-DD).
