v1.0.timekeeping-accruals-transactions.name=Accrual Transactions
v1.0.timekeeping-accruals-transactions.parent=root.wtk.default
v1.0.timekeeping-accruals-transactions.description=This API resource provides the ability to access accrual transactions.
v1.0.timekeeping-accruals-transactions.apioperation.nickname=Retrieve Accrual Transactions
v1.0.timekeeping-accruals-transactions.name=AccrualTransactions
v1.0.timekeeping-accruals-transactions.post.apioperation.value=Returns a list of accrual transactions based on a list of employees.
v1.0.timekeeping-accruals-transactions.post.apiresponse.200.message=Successfully retrieved list of accrual transaction for specified employees.
v1.0.timekeeping-accruals-transactions.post.apiresponse.400.message=Bad Request
v1.0.timekeeping-accruals-transactions.post.apiparam.value=Request context