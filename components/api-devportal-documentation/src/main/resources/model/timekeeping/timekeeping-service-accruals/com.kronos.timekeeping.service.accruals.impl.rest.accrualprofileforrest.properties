#Comments for Properties
#Sun Apr 09 19:03:33 IST 2017
v1.0.accrualprofileforrest.apimodel.description=Accrual Profile REST entity.
v1.0.accrualprofileforrest.apimodelproperty.accrualprofile.description=A reference to the accrual profile object.
v1.0.accrualprofileforrest.apimodelproperty.accrualcodelist.description=A list of references to accrual codes.
v1.0.accrualprofileforrest.apimodelproperty.effectivedate.description=The effective date of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.accrualprofileforrest.apimodelproperty.expirationdate.description=The expiration date of an accrual profile in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.accrualprofileforrest.apimodelproperty.userassignedcurrency.description=The currency code of the currency policy assigned to a user.