#Comments for Properties
#Wed Apr 05 17:09:33 IST 2017
v1.0.timecardannotationforrest.apimodel.description=Timecard Annotation context model.
v1.0.timecardannotationforrest.apimodelproperty.id.description=The ID of the work annotation.
v1.0.timecardannotationforrest.apimodelproperty.employee.description=A reference to the employee object.
v1.0.timecardannotationforrest.apimodelproperty.annotationtype.description=A reference to the annotation type object.
v1.0.timecardannotationforrest.apimodelproperty.startdatetime.description=The start date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.timecardannotationforrest.apimodelproperty.enddatetime.description=The end date and time of a date range in ISO_LOCAL_DATE_TIME format (yyyy-mm-ddThh:mm:ss.sss).
v1.0.timecardannotationforrest.apimodelproperty.duration.description=The duration in seconds of the timecard annotation.
v1.0.timecardannotationforrest.apimodelproperty.subtype.description=A reference to the annotation subtype object for the annotation.