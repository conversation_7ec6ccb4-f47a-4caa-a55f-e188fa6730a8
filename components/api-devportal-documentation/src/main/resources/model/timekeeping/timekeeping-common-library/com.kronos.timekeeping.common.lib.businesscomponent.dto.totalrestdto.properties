v1.0.totalrestdto.apimodel.description=The totals model.
v1.0.totalrestdto.apimodelproperty.alreadypaid.description=A Boolean indicator of whether or not a total is already paid.
v1.0.totalrestdto.apimodelproperty.unapproved.description=A Boolean indicator of whether or not a total is unapproved.
v1.0.totalrestdto.apimodelproperty.accountapprovalnumber.description=The account approval number.
v1.0.totalrestdto.apimodelproperty.inprogress.description=A Boolean indicator of whether or not a returned total segment is currently in progress.
v1.0.totalrestdto.apimodelproperty.projected.description=A Boolean indicator of whether or not the total segment is projected (planned to occur in the future).
v1.0.totalrestdto.apimodelproperty.correctedapplydate.description=The corrected apply date in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.totalrestdto.apimodelproperty.correcteddurationinseconds.description=The corrected duration in seconds.
v1.0.totalrestdto.apimodelproperty.correctedmoneyamount.description=The corrected money amount.
v1.0.totalrestdto.apimodelproperty.correcteddaysamount.description=The corrected days amount.
v1.0.totalrestdto.apimodelproperty.workweekapplydatetime.description=The date and time on which a work week was applied in ISO_LOCAL_DATE_TIME format (YYYY-MM-DDThh:mm:ss.SSS).
v1.0.totalrestdto.apimodelproperty.employeeid.description=A reference to the employee object.
v1.0.totalrestdto.apimodelproperty.applydate.description=The date to which a time applies in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.totalrestdto.apimodelproperty.paycodeid.description=The ID of a pay code.
v1.0.totalrestdto.apimodelproperty.durationinseconds.description=The duration in seconds of the processed segment.
v1.0.totalrestdto.apimodelproperty.moneyamount.description=The amount (as a decimal value representing money) of the processed segment.
v1.0.totalrestdto.apimodelproperty.daysamount.description=The amount of days for the processed segment.
v1.0.totalrestdto.apimodelproperty.elementtype.description=An enumeration of all time item types.
v1.0.totalrestdto.apimodelproperty.startdatetime.description=The date and time of the start of the activity in ISO_LOCAL_DATE_TIME format (YYYY-MM-DDThh:mm:ss.SSS).
v1.0.totalrestdto.apimodelproperty.starttimezoneid.description=The start timezone ID.
v1.0.totalrestdto.apimodelproperty.enddatetime.description=The date and time of the end of the activity in ISO_LOCAL_DATE_TIME format (YYYY-MM-DDThh:mm:ss.SSS).
v1.0.totalrestdto.apimodelproperty.endtimezoneid.description=The end timezone ID.
v1.0.totalrestdto.apimodelproperty.laborcategoryid.description=The ID of a labor category.
v1.0.totalrestdto.apimodelproperty.orgjobid.description=The ID of the organizational job associated with a total segment.
v1.0.totalrestdto.apimodelproperty.workruleid.description=The ID of a work rule.
v1.0.totalrestdto.apimodelproperty.transferlaborcategory.description=The transfer labor category.
v1.0.totalrestdto.apimodelproperty.transferorgjob.description=A Boolean indicator of whether or not the total segment is a transfer.
v1.0.totalrestdto.apimodelproperty.transferworkrule.description=A Boolean indicator of whether or not the total segment is generated from a transfer work rule.
v1.0.totalrestdto.apimodelproperty.segmenttype.description=The type of segment for the schedule tag segment template.
v1.0.totalrestdto.apimodelproperty.id.description=The ID of the total segment.
v1.0.totalrestdto.apimodelproperty.itemid.description=The ID of the total item.
