#
# COMPANY MODEL 
#
v1.0.companydto.apimodel.description=The Company model.
v1.0.companydto.apimodelproperty.id.description=The ID of a company.
v1.0.companydto.apimodelproperty.identifier.description=The employer identifier of a company.
v1.0.companydto.apimodelproperty.identityType.description=The identity type of a company.
v1.0.companydto.apimodelproperty.cei.description=The cei of company.
v1.0.companydto.apimodelproperty.name.description=The name of a company.
v1.0.companydto.apimodelproperty.location.description=The location of a company.
v1.0.companydto.apimodelproperty.deviceGroup.description=The device group of a company.
v1.0.companydto.apimodelproperty.caepf.description=The caepf of a company.
v1.0.companydto.apimodelproperty.cno.description=The cno of a company.
#
# IDENTITY TYPES MODEL 
#
v1.0.identitytypesdto.apimodel.description=The Identity Type model.
v1.0.identitytypesdto.apimodelproperty.id.description=The ID of an identity type.
v1.0.identitytypesdto.apimodelproperty.qualifier.description=The name of an identity type.
#
# PAYCODE ATTRIBUTE DEFINITION MODEL 
#
v1.0.paycodeattributedefinitiondto.apimodel.description=The Paycode Attribute Definition model.
v1.0.paycodeattributedefinitiondto.apimodelproperty.id.description=The ID of a paycode attribute definition.
v1.0.paycodeattributedefinitiondto.apimodelproperty.name.description=The name of a paycode attribute definition.
v1.0.paycodeattributedefinitiondto.apimodelproperty.description.description=The description of a paycode attribute definition.
v1.0.paycodeattributedefinitiondto.apimodelproperty.default.description=A Boolean indicator or whether or not a paycode attribute definition is the default.
v1.0.paycodeattributedefinitiondto.apimodelproperty.attributemappings.description=The collection of attribute mappings.
#
# PAYCODE ATTRIBUTE KEY MODEL 
#
v1.0.paycodeattributekeydto.apimodel.description=The Paycode Attribute Key model.
v1.0.paycodeattributekeydto.apimodelproperty.id.description=The ID of a paycode attribute key.
v1.0.paycodeattributekeydto.apimodelproperty.name.description=The name of a paycode attribute key.
v1.0.paycodeattributekeydto.apimodelproperty.extensible.description=A Boolean indicator of whether or not a paycode attribute key is extensible.
v1.0.paycodeattributekeydto.apimodelproperty.editable.description=A Boolean indicator of whether or not a paycode attribute key is editable.
v1.0.paycodeattributekeydto.apimodelproperty.defaultValue.description=The default value of a paycode attribute key.
v1.0.paycodeattributekeydto.apimodelproperty.keyOrder.description=The key order of a paycode attribute key.
#
# PAYCODE ATTRIBUTE DEFINITION CUSTOM MAPPING MODEL 
#
v1.0.paycodeattributedefinitioncustommappingdto.apimodel.description=The Paycode Attribute Definition Custom Mapping model.
v1.0.paycodeattributedefinitioncustommappingdto.apimodelproperty.paycodeAttributeKey.description=The paycode attribute key associated with Paycode Attribute Mapping.
v1.0.paycodeattributedefinitioncustommappingdto.apimodelproperty.paycode.description=The paycode associated with Paycode Attribute Mapping.
v1.0.paycodeattributedefinitioncustommappingdto.apimodelproperty.customValue.description=The custom value associated with Paycode Attribute Mapping.
v1.0.paycodeattributedefinitioncustommappingdto.apimodelproperty.keyOrder.description=The Key order associated with Paycode Attribute Mapping.
#
# Device Group MODEL
#
v1.0.devicegroupdto.apimodel.description=The Device Group model.
v1.0.devicegroupdto.apimodelproperty.id.description=The ID of a device group.
v1.0.devicegroupdto.apimodelproperty.name.description=The name of a device group.
v1.0.devicegroupdto.apimodelproperty.description.description=The description of a device group.
v1.0.devicegroupdto.apimodelproperty.devices.description=The collection of devices that make up a device group.
#
# Devices MODEL
#
v1.0.devicedto.apimodel.description=The device model.
v1.0.devicedto.apimodelproperty.id.description=The ID of a device.
v1.0.devicedto.apimodelproperty.name.description=The name of a device.
v1.0.devicedto.apimodelproperty.ipaddress.description=The IP address of a device.
v1.0.devicedto.apimodelproperty.location.description=The location of a device.
v1.0.devicedto.apimodelproperty.activesw.description=A Boolean indicator of whether or not a device is active.
v1.0.devicedto.apimodelproperty.timezoneid.description=The timezone ID associated with a device.
v1.0.devicedto.apimodelproperty.certified.description=A Boolean indicator of whether or not a device is certified.
v1.0.devicedto.apimodelproperty.certifiedsw.description=A Boolean indicator of whether or not a device is certified.
v1.0.devicedto.apimodelproperty.timezone.description=A reference to the timezone associated with a device.
#
# Audit Report MODEL
#
v1.0.auditreportdto.apimodel.description=The brazil audit report model.
v1.0.auditreportdto.apimodelproperty.auditType.description=The audit type for brazil audit report.
v1.0.auditreportdto.apimodelproperty.auditBeginDate.description=The audit begin date for brazil audit report.
v1.0.auditreportdto.apimodelproperty.auditEndDate.description=The audit end date for brazil audit report.
v1.0.auditreportdto.apimodelproperty.index.description=The index for brazil audit report.
v1.0.auditreportdto.apimodelproperty.count.description=The count for brazil audit report.
v1.0.auditreportdto.apimodelproperty.companies.description=The companies object for brazil audit report.
v1.0.auditreportdto.apimodelproperty.employees.description=The employees object for brazil audit report.