#Comments for Properties
#Wed Mar 15 09:57:15 IST 2017

v1.0.leave-setup-leave_reason.name=Leave Reasons
v1.0.leave-reason.description=This resource allows you to retrieve reasons for a Leave case. Leave reasons are the list of acceptable uses for leave and should include all types of family, medical, or other leave that are allowed through your organization or that are legislated as available to employees.
v1.0.leave-reason.parent=root.wfl.default

#get by id
v1.0.leave-setup-leave_reason.get.{id}.nickname=Retrieve Leave Reason by ID
v1.0.leave-setup-leave_reason.get.{id}.notes=This operation returns a Leave reason by ID.
v1.0.leave-setup-leave_reason.get.{id}.summary=Returns a Leave reason.
v1.0.leave-setup-leave_reason.get.{id}.response.200.message=Successfully retrieved a reason.
v1.0.leave-setup-leave_reason.get.{id}.response.400.message=<ul><li>WFL-100004 - The following leave case reason does not exist: {id}.</li></ul>
v1.0.leave-setup-leave_reason.get.{id}.response.404.message=Following reason could not be found.
v1.0.leave-setup-leave_reason.get.{id}.response.500.message=Internal Server Error.
v1.0.leave-setup-leave_reason.get.{id}.pathparam.setup_reason_id.value=The ID of a Leave reason.

#get by all or by name
v1.0.leave-setup-leave_reason.get.currentpath.nickname=Retrieve All Leave Reasons or by Name
v1.0.leave-setup-leave_reason.get.currentpath.notes=This operation returns a list of all Leave reasons or returns a single Leave reason by name.
v1.0.leave-setup-leave_reason.get.currentpath.summary=Returns all Leave reasons or one by name.
v1.0.leave-setup-leave_reason.get.currentpath.response.200.message=Successfully retrieved a reason or retrieved all reasons.
v1.0.leave-setup-leave_reason.get.currentpath.response.400.message=Invalid Request Input.
v1.0.leave-setup-leave_reason.get.currentpath.response.404.message=<ul><li>WFL-100004 - The following leave case reason does not exist: {name}.</li></ul>
v1.0.leave-setup-leave_reason.get.currentpath.response.500.message=Internal Server Error.
v1.0.leave-setup-leave_reason.get.currentpath.queryparam.name.value=The name of a Leave reason.

#multi-read
v1.0.leave-setup-leave_reason.post.multi_read.nickname=Retrieve Leave Reasons
v1.0.leave-setup-leave_reason.post.multi_read.notes=This operation returns a list of Leave reasons by object references.
v1.0.leave-setup-leave_reason.post.multi_read.summary=Returns a list of Leave reasons.
v1.0.leave-setup-leave_reason.post.multi_read.response.200.message=Successfully retrieved a list of reasons.
v1.0.leave-setup-leave_reason.post.multi_read.response.207.message=<ul><li>WFL-100004 - The following leave case reason does not exist: {id}.</li></ul>
v1.0.leave-setup-leave_reason.post.multi_read.response.400.message=Invalid Request Input.
v1.0.leave-setup-leave_reason.post.multi_read.response.404.message=<ul><li>WFL-100318 - Some Unknown Error Occurred. Error Details Not Available.</li></ul>
v1.0.leave-setup-leave_reason.post.multi_read.response.500.message=Internal server error.
v1.0.leave-setup-leave_reason.post.multi_read.reasonsCriteria.value=The Retrieve Leave Reasons request payload.


