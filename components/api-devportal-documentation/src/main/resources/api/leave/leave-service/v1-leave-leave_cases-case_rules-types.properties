#Comments for Properties
#Wed May 3 09:57:15 IST 2017
v1.0.leave-leave_cases-case_rules-types.name=Leave Case Rule Types
v1.0.leave-leave_cases-case_rules-types.parent=root.wfl.default
v1.0.leave-leave_cases-case_rules-types.description=This resource allows you to retrieve all Leave case rule types assigned to a Leave case within a specified date range. 

v1.0.leave-leave_cases-case_rules-types.get.currentpath.nickname=Retrieve Rule Types
v1.0.leave-leave_cases-case_rules-types.get.currentpath.summary=Returns rule types assigned to a Leave case within a date range.
v1.0.leave-leave_cases-case_rules-types.get.currentpath.notes=This operation returns rule types assigned to a Leave case within a date range.
v1.0.leave-leave_cases-case_rules-types.get.currentpath.response.200.message=Successful retrieval of rule type assignments for a Leave case within a date range.
v1.0.leave-leave_cases-case_rules-types.get.currentpath.response.400.message=<ul><li>WFL-100150 - Leave case is not specified.</li><li>WFL-100029 - The following Leave case could not be found: {id}.</li><li>WFL-100023 - The date format is invalid.</li><li>WFL-100065 - Start date is required.</li><li>WFL-100066 - End date is required.</li><li>WFL-100040 - The date range is invalid. The start date must be on or before the end date.</li></ul>
v1.0.leave-leave_cases-case_rules-types.get.currentpath.queryparam.leave_case_id.value=The ID of the Leave case.
v1.0.leave-leave_cases-case_rules-types.get.currentpath.queryparam.code.value=The external code associated with a Leave case.
v1.0.leave-leave_cases-case_rules-types.get.currentpath.queryparam.from.value=The start date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.leave-leave_cases-case_rules-types.get.currentpath.queryparam.to.value=The end date of a date range in ISO_LOCAL_DATE format (YYYY-MM-DD).
