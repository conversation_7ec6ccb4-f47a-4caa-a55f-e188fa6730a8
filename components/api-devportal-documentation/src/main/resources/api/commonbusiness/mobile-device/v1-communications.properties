#Communications-Configuration service
v1.0.communications-communications_configuration_service.name=Communications Configuration Service
v1.0.communications-communications_configuration_service.parent=root.mobileapp.default
v1.0.communications-communications_configuration_service.description=<p>The Communications Configuration API is used to retrieve metadata about the Talk application.</p>

#GET
v1.0.communications-communications_configuration_service.get.summary=Retrieves Communications configuration metadata for a WFM tenant.
v1.0.communications-communications_communications_service.get.notes=This operation retrieves Communications configuration metadata for a given WFM tenant.
v1.0.communications-communications_communications_service.get.nickname=Get Communications Configuration
v1.0.communications-communications_configuration_service.get.response.200.message=Successfully retrieved Communications configuration.
v1.0.communications-communications_configuration_service.get.response.204.message=No Communications Configuration found.
v1.0.communications-communications_configuration_service.get.response.500.message=Unexpected error encountered while retrieving Communications configuration data.
