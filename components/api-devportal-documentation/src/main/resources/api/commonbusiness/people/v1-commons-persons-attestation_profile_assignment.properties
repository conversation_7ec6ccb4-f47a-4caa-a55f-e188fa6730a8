v1.0.commons-persons-attestation_profile_assignments.name=Attestation Profile Assignments
v1.0.commons-persons-attestation_profile_assignments.parent=root.people.default
v1.0.commons-persons-attestation_profile_assignments.description=This resource allows you to assign or modify the assignment of Attestation profiles to employees.\n\nThe Attestation Profile, which is assigned to employees in People Information, contains one or more attestation assignments.\n\nRefer to the following topic for example calls and tutorials: [Attestation Profile Assignments](doc:attestation-profile-assignments-doc). For more information and tutorials about Attestations, refer to [A Guide to Attestations](doc:a-guide-to-attestations-doc).

v1.0.commons-persons-attestation_profile_assignments.get.currentpath.nickname=Retrieve Assigned Attestation Profiles
v1.0.commons-persons-attestation_profile_assignments.get.currentpath.notes=This operation returns a list of the assigned Attestation profiles for a person.
v1.0.commons-persons-attestation_profile_assignments.get.currentpath.summary=Returns a list of the assigned Attestation profiles for a person.
v1.0.commons-persons-attestation_profile_assignments.get.currentpath.response.200.message=Successfully retrieved a list of the assigned Attestation Profiles for a person.
v1.0.commons-persons-attestation_profile_assignments.get.currentpath.response.400.message=<p>Bad Request:</p><ul><li>WCO-101263 - The action required an existing person, but either the person for the key could not be found, the user does not have access rights to that person, or a database error occurred. Property name: {0}, value:{1}</li></ul>
v1.0.commons-persons-attestation_profile_assignments.get.currentpath.response.500.message=Internal server error.
v1.0.commons-persons-attestation_profile_assignments.get.currentpath.pathparam.personid.value=The ID of a person.
v1.0.commons-persons-attestation_profile_assignments.get.currentpath.queryparam.assignToManagerRole.value=An optional Boolean indicator of whether or not the attestation profile is assigned to a person's manager role.

v1.0.commons-persons-attestation_profile_assignments.post.currentpath.nickname=Assign Attestation Profile
v1.0.commons-persons-attestation_profile_assignments.post.currentpath.notes=This operation assigns an Attestation profile to a person.
v1.0.commons-persons-attestation_profile_assignments.post.currentpath.summary=Assigns an Attestation profile to a person.
v1.0.commons-persons-attestation_profile_assignments.post.currentpath.response.201.message=Successfully assigned an Attestation profile.
v1.0.commons-persons-attestation_profile_assignments.post.currentpath.response.400.message=<p>Bad Request:</p><ul><li>WCO-101263 - The action required an existing person, but either the person for the key could not be found, the user does not have access rights to that person, or a database error occurred. Property name: {0}, value: {1}</li><li>WCO-101231 - A property value is required, but was not specified. Property - {0}.</li><li>WCO-101237 - The property was not specified or empty/null - Property: {0}.</li><li>WTK-180730 - An Attestation Profile with the following id does not exist: {0}.</li><li>WCO-101232 - The value for the property is not valid - Name: {0}, Value: {1}.</li><li>WTK-180747 - An Attestation Profile with the following name does not exist: {0}.</li></ul>
v1.0.commons-persons-attestation_profile_assignments.post.currentpath.response.500.message=Internal server error.
v1.0.commons-persons-attestation_profile_assignments.post.currentpath.pathparam.personid.value=The ID of a person.
v1.0.commons-persons-attestation_profile_assignments.post.currentpath.dto.value=The Assign Attestation Profile request payload.

v1.0.commons-persons-attestation_profile_assignments_by_person_number.name=Attestation Profile Assignments by Person Number
v1.0.commons-persons-attestation_profile_assignments_by_person_number.parent=root.people.default
v1.0.commons-persons-attestation_profile_assignments_by_person_number.description=<p>This resource allows you to assign or modify the assignment of Attestation profiles to employees. This resource identifies each person using a person number.</p><p>The Attestation Profile, which is assigned to employees in People Information, contains one or more attestation assignments.</p>

v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.nickname=Retrieve Assigned Attestation Profiles by Person Number
v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.notes=This operation returns a list of the assigned Attestation profiles for a person by person number.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.summary=Returns a list of the assigned Attestation profiles for a person.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.response.200.message=Successfully retrieved a list of the assigned Attestation Profiles for a person.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.response.400.message=Bad Request: <ul><li>WCO-101263 - The action required an existing person, but either the person for the key could not be found, the user does not have access rights to that person, or a database error occurred. Property name: {0}, value:{1}</li></ul>.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.response.500.message=Internal server error.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.queryparam.person_number.value=A number that uniquely identifies a person. This is not an employee ID.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.queryparam.assignToManagerRole.value=An optional Boolean indicator of whether or not the attestation profile is assigned to a person's manager role.

v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.nickname=Assign Attestation Profile by Person Number
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.notes=This operation assigns an Attestation profile to a person by person number.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.summary=Assigns an Attestation profile to a person.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.response.201.message=Successfully assigned an Attestation profile.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.response.400.message=<p>Bad Request:</p><ul><li>WCO-101263 - The action required an existing person, but either the person for the key could not be found, the user does not have access rights to that person, or a database error occurred. Property name: {0}, value: {1}</li><li>WCO-101231 - A property value is required, but was not specified. Property - {0}.</li><li>WCO-101237 - The property was not specified or empty/null - Property: {0}.</li><li>WTK-180730 - An Attestation Profile with the following id does not exist: {0}.</li><li>WCO-101232 - The value for the property is not valid - Name: {0}, Value: {1}.</li><li>WTK-180747 - An Attestation Profile with the following name does not exist: {0}.</li></ul>
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.response.500.message=Internal server error.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.dto.value=The Assign Attestation Profile by Person Number request payload.

v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.nickname=Retrieve Attestation Profile Assignments by Person Number
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.notes=This operation returns Attestation profile assignments for one or more employees.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.summary=Returns Attestation profile assignments for one or more employees.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.response.200.message=Attestation profile assignment retrieved successfully.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.response.207.message=Partial success.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.response.400.message=<p>Bad Request:</p><ul><li>[WCO-101232] - The value for the property is not valid - Name: employees, Value: null.</li><li>[WCO-133101] - Please provide at least 1 of these parameters :- ids/qualifiers/refs.</li><li>[WCO-133102] - Please provide only 1 of these parameters :- ids/qualifiers/refs.</li></ul>
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.response.500.message=Internal server error.
v1.0.commons-persons-attestation_profile_assignments_multi_read.post.currentpath.employees.value=A list of person numbers of employees.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.requestbody.value=The Retrieve Attestation Profile Assignments by Person Number request payload.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.requestbody.value=Person Attestation Profile Assignment Request object used to filter attestation profile assignment.

v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.nickname=Update Attestation Profile Assignments by Person Number
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.notes=This operation updates Attestation profile assignments for one or more employees.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.summary=Updates Attestation profile assignments for one or more employees.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.response.200.message=Attestation profile assignments updated successfully.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.response.207.message=Partial success.
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.response.400.message=<p>Bad Request:</p><ul> <li>[WCO-101207] - Service limit error, number of records exceed the allowed limit : {batchSize} </li> <li>[WCO-101228] - You must enter a Start Date that occurs on or before the End Date. Start Date: {Date1}, End Date: {Date2} </li> <li>[WCO-101230] - The information in the bean is not valid.  Detailed errors should be wrapped within this exception.</li> <li>[WCO-101231] - A property value is required, but was not specified. Property - {missingProperty}. </li> <li>[WCO-101232] - The value for the property is not valid - Name: {propertyName}, Value: {propertyValue}. </li> <li>[WCO-101236] - A bad combination of properties and values was specified - Property 1: {propertyName1}, Value 1: {propertyValue1}, Property 2: {propertyName2}, Value 2: {propertyValue2}. </li> <li>[WCO-101237] - The property was not specified or empty/null - Property: {propertyName}. </li> <li>[WCO-101240] - This user is not authorized to perform the action or it is not a valid action name within the bean - Bean: {beanName}, Action: {actionName}. </li> <li>[WCO-101263] - The action required an existing person, but either the person for the key could not be found, the user does not have access rights to that person, or a database error occurred. Property name: {propertyName}, value: {propertyValue} </li> <li>[WCO-101265] - The manager's employee group does not have access to the employee's labor account. Ensure that the designated manager, his employee group and the employee's labor account are correct - Employee: {beanKey}, Manager: {beanName}. </li> <li>[WCO-101266] - The person was found within the system, but the user does not have access rights to that person at this time . Property name: {propertyName}, value: {propertyValue} </li> <li>[WCO-101271] - Completed with error(s). Detailed errors should be wrapped within this exception. </li> <li>[WCO-101272] - Completed with error(s). Detailed errors should be wrapped within this exception. </li> <li>[WCO-101305] - Duplicate employees are not allowed for bulk operations. </li> <li>[WCO-101306] - Maneger is not allowed to edit own profile.</li> <li>[WCO-101307] - Request for multi update is not defined. </li> <li>[WCO-101320] - An invalid parameter '{propertyName}' was passed. The parameter had a value of '{propertyValue}'. </li> </ul>
v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.response.500.message=Internal server error.
v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.personattestationprofileassignments.value=The Update Attestation Profile Assignments by Person Number request payload.
v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.requestbody.value=The Update Attestation Profile Assignments by Person Number request payload.




#Old key-value pairs
v1.0.commons-persons-attestation_profile_assignments_multi_read.post.currentpath.nickname=Retrieve Attestation Profile Assignments (O)
v1.0.commons-persons-attestation_profile_assignments_multi_read.post.currentpath.notes=This operation returns Attestation profile assignments for one or more employees.
v1.0.commons-persons-attestation_profile_assignments_multi_read.post.currentpath.summary=Returns Attestation profile assignments for one or more employees.
# The following string is actually for a 200 response:
v1.0.commons-persons-attestation_profile_assignments_multi_read.post.currentpath.response.201.message=Attestation profile assignment retrieved successfully.
v1.0.commons-persons-attestation_profile_assignments_multi_read.post.currentpath.response.207.message=Partial success.
v1.0.commons-persons-attestation_profile_assignments_multi_read.post.currentpath.response.400.message=<ul><li>[WCO-101232] - The value for the property is not valid - Name: employees, Value: null.</li><li>[WCO-133101] - Please provide at least 1 of these parameters :- ids/qualifiers/refs.</li><li>[WCO-133102] - Please provide only 1 of these parameters :- ids/qualifiers/refs.</li></ul>
v1.0.commons-persons-attestation_profile_assignments_multi_read.post.currentpath.response.500.message=Internal server error.

v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.nickname=Update Attestation Profile Assignments (O)
v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.notes=This operation updates Attestation profile assignments for one or more employees.
v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.summary=Updates Attestation profile assignments for one or more employees.
v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.response.201.message=Attestation profile assignments updated successfully.
v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.response.207.message=Partial success.
v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.response.400.message=Bad Request.
v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.response.500.message=Internal server error.
v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.personattestationprofileassignments.value=A list of Attestation profile assignment objects.
v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.queryparam.mergeEffectiveDating.value=A Boolean indicator of whether or not to merge Attestation profile assignments by effective date.