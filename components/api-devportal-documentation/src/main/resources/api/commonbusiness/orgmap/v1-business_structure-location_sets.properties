#Comments for Properties
#Tue Feb 28 14:25:30 IST 2017
v1.0.business_structure-location_sets.name=Location Sets
v1.0.business_structure-location_sets.parent=root.wco2.default
v1.0.business_structure-location_sets.description=<p>This resource provides a set of operations that allow you to retrieve and manipulate location sets.</p><p>Locations, which each represent a physical location (Operating Room or Mail Room) or a logistical unit that is not a physical location (Support, Administration, a Home Care Unit), can be grouped into sets.</p>

v1.0.business_structure-location_sets.put.{groupid}.nickname=Update Location Set by ID (Deprecated)
v1.0.business_structure-location_sets.put.{groupid}.summary=Updates a location set.
v1.0.business_structure-location_sets.put.{groupid}.notes=This operation updates an existing Organizational Map group (location set) by group ID.
v1.0.business_structure-location_sets.put.{groupid}.response.400.message=Incorrect Request Parameter/Invalid Request Body.
v1.0.business_structure-location_sets.put.{groupid}.response.200.message=Success
v1.0.business_structure-location_sets.put.{groupid}.pathparam.groupid.value=The group ID of a location set.
v1.0.business_structure-location_sets.put.{groupid}.queryparam.orgmaplocationsetsforrestv1.date.value=The effective date of the business structure whose location node is being referenced in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.business_structure-location_sets.put.{groupid}.payload.groupnode.value=The updated Organizational Map group (location set).

v1.0.business_structure-location_sets.post.{groupid}.nickname=Update Location Set by ID
v1.0.business_structure-location_sets.post.{groupid}.summary=Updates a location set.
v1.0.business_structure-location_sets.post.{groupid}.notes=This operation updates an existing Organizational Map group (location set) by group ID.
v1.0.business_structure-location_sets.post.{groupid}.response.400.message=Incorrect Request Parameter/Invalid Request Body.
v1.0.business_structure-location_sets.post.{groupid}.response.200.message=Success
v1.0.business_structure-location_sets.post.{groupid}.pathparam.groupid.value=The group ID of a location set.
v1.0.business_structure-location_sets.post.{groupid}.queryparam.orgmaplocationsetsforrestv1.date.value=The effective date of the business structure whose location node is being referenced in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.business_structure-location_sets.post.{groupid}.payload.groupnode.value=The updated Organizational Map group (location set).

v1.0.business_structure-location_sets.post.currentpath.nickname=Create Location Set
v1.0.business_structure-location_sets.post.currentpath.summary=Creates a location set.
v1.0.business_structure-location_sets.post.currentpath.notes=This operation creates an Organizational Map group (location set).
v1.0.business_structure-location_sets.post.currentpath.response.400.message=Incorrect Request Parameter/Invalid Request Body.
v1.0.business_structure-location_sets.post.currentpath.response.200.message=Success
v1.0.business_structure-location_sets.post.currentpath.queryparam.orgmaplocationsetsforrestv1.date.value=The effective date of the business structure whose location node is being referenced in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.business_structure-location_sets.post.queryparam.orgmaplocationsetsforrestv1.date.value=The effective date of the business structure whose location node is being referenced in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.business_structure-location_sets.post.payload.group.value=The location set to create.

v1.0.business_structure-location_sets.get.currentpath.nickname=Retrieve Location Set by Name
v1.0.business_structure-location_sets.get.currentpath.summary=Returns a location set by name and system.
v1.0.business_structure-location_sets.get.currentpath.notes=This operation returns an Organizational Map group (location set) by name and system.
v1.0.business_structure-location_sets.get.currentpath.response.400.message=Incorrect Request Parameter/Invalid Request Body.
v1.0.business_structure-location_sets.get.currentpath.response.200.message=Success
v1.0.business_structure-location_sets.get.currentpath.queryparam.name.value=The searching group name.
v1.0.business_structure-location_sets.get.currentpath.queryparam.orgmaplocationsetsforrestv1.context.value=Indicates if the context is ORG or FORECAST.
v1.0.business_structure-location_sets.get.currentpath.queryparam.orgmaplocationsetsforrestv1.date.value=The effective date of the business structure whose location node is being referenced in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.business_structure-location_sets.get.currentpath.queryparam.orgmaplocationsetsforrestv1.system.value=The searching group system.
v1.0.business_structure-location_sets.get.queryparam.name.value=The name of a location set.
v1.0.business_structure-location_sets.get.queryparam.orgmaplocationsetsforrestv1.system.value=All possible objects that can be selected to be returned in the Org Map Location set request.
v1.0.business_structure-location_sets.get.queryparam.orgmaplocationsetsforrestv1.context.value=Indicates if the context is ORG or FORECAST.
v1.0.business_structure-location_sets.get.queryparam.orgmaplocationsetsforrestv1.date.value=The effective date of the business structure whose location node is being referenced in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.business_structure-location_sets.get.queryparam.orgmaplocationsetsforrestv1.only_node_refs_tree.value=A Boolean indicator of whether or not to return a model that provides the Business Structure as a tree instead of a flat list.

v1.0.business_structure-location_sets.get.{id}.nickname=Retrieve Location Set by ID
v1.0.business_structure-location_sets.get.{id}.summary=Returns a location set by ID.
v1.0.business_structure-location_sets.get.{id}.notes=This operation returns an Organizational Map group (location set) by ID.
v1.0.business_structure-location_sets.get.{id}.response.400.message=Incorrect Request Parameter/Invalid Request Body.
v1.0.business_structure-location_sets.get.{id}.response.200.message=Success
v1.0.business_structure-location_sets.get.{id}.pathparam.id.value=The ID of the location set to update.
v1.0.business_structure-location_sets.get.{id}.queryparam.orgmaplocationsetsforrestv1.context.value=Indicate if the context is ORG or FORECAST.
v1.0.business_structure-location_sets.get.{id}.queryparam.orgmaplocationsetsforrestv1.date.value=The searching date.
v1.0.business_structure-location_sets.get.{id}.queryparam.orgmaplocationsetsforrestv1.only_node_refs_tree.value=A Boolean indicator of whether or not to return a model that provides the Business Structure as a tree instead of a flat list.

v1.0.business_structure-location_sets.post.multi_read.nickname=Retrieve Location Sets by List (Deprecated)
v1.0.business_structure-location_sets.post.multi_read.summary=Returns location sets. This operation is deprecated.
v1.0.business_structure-location_sets.post.multi_read.notes=<p><strong>This operation is deprecated; instead use POST /v2/commons/location_sets/multi_read.</strong></p><p>This operation returns Organizational Map groups (location sets) according to a list of groups data.</p>
v1.0.business_structure-location_sets.post.multi_read.response.400.message=Incorrect Request Parameter/Invalid Request Body.
v1.0.business_structure-location_sets.post.multi_read.response.200.message=Success
v1.0.business_structure-location_sets.post.multi_read.payload.request.value=Receives Orgmap Locations sets model as input.

v1.0.business_structure-location_sets.NotAvailable.currentpath.pathparam.id.value=The ID of the location set to update.
v1.0.business_structure-location_sets.NotAvailable.currentpath.queryparam.context.value=Indicate if the context is ORG or FORECAST.
v1.0.business_structure-location_sets.NotAvailable.currentpath.queryparam.name.value=The searching group name.
v1.0.business_structure-location_sets.NotAvailable.currentpath.queryparam.system.value=The searching group system.

v1.0.business_structure-location_sets.apply_upsert.nickname=Create or Update Location Set 
v1.0.business_structure-location_sets.apply_upsert.notes=<p>This operation creates or updates a location set.</p><br /><p>The associated Access Control Point is OJS_EDITOR with the edit permission ALLOWED.</p>
v1.0.business_structure-location_sets.apply_upsert.summary=Creates or updates a location set.
v1.0.business_structure-location_sets.apply_upsert.response.200.message=Success
v1.0.business_structure-location_sets.apply_upsert.response.207.message=Partially succeeded in updating the location set.
v1.0.business_structure-location_sets.apply_upsert.response.400.message=Incorrect Request Parameter/Invalid Request Body.
v1.0.business_structure-location_sets.apply_upsert.response.403.message=You are not authorized to perform this operation.

#Adding the following to allow correct resolution of keys; the format above should be correct but is not
v1.0.business_structure-location_sets.multi_upsert.nickname=Create or Update Location Set
v1.0.business_structure-location_sets.multi_upsert.notes=<p>This operation creates or updates a location set. Create/Update location set with this API can result in an empty location with no jobs. This is useful for integration-based maintenance for location sets, but the final state of the location set must include a job. </p><br /><p>The associated Access Control Point is OJS_EDITOR with the edit permission ALLOWED.</p>
v1.0.business_structure-location_sets.multi_upsert.summary=Creates or updates a location set.
v1.0.business_structure-location_sets.multi_upsert.response.200.message=Success
v1.0.business_structure-location_sets.multi_upsert.response.207.message=Partially succeeded in updating the location set.
v1.0.business_structure-location_sets.multi_upsert.response.400.message=<p>Bad Request:</p><ul> <li>[WCO-103002] - Some unexpected error occurs {detail}, please check the system log. </li> <li>[WCO-103015] - The organizational object reference is mandatory and cannot be missing or empty. </li> <li>[WCO-103284] - Some of the location sets could not be updated successfully. </li> <li>[WCO-103285] - At least one node ref is required for a location set. </li> <li>[WCO-103286] - The following location ID does not exist: {nodeID} </li> <li>[WCO-103287] - The following location qualifier does not exist: {qualifier} </li> <li>[WCO-103288] - Either ID or Name must be present in a location set. </li> <li>[WCO-103289] - An unknown error occurred. </li> <li>[WCO-103295] - The number of records exceeds the allowed limit of: {serviceLimit} </li> <li>[WCO-103296] - Add Ref is mandatory in create location set request. </li> <li>[WCO-103297] - You cannot remove a node in a create location set request. </li> <li>[WFP-90005] - Operation not permitted.{S} </li> <li>Incorrect Request Parameter/Invalid Request Body. </li> </ul>
v1.0.business_structure-location_sets.multi_upsert.response.403.message=You are not authorized to perform this operation. 
v1.0.business_structure-location_sets.multi_upsert.response.413.message=The Service Limit has been exceeded.
v1.0.business_structure-location_sets.payload.groupnodes.value=The Create or Update Location Set request payload.

v1.0.orgmapgroup.apimodelproperty.addnoderefs.description=Add Node references to a Location Set.
v1.0.orgmapgroup.apimodelproperty.removeNodeRefs.description=Remove Node references from a Location Set.
v1.0.orgmapgroup.apimodelproperty.effectiveOnDate.description=The date on which a location set is effective in ISO_LOCAL_DATE format (YYYY-MM-DD).

v1.0.business_structure-location_sets.post.multi_delete.nickname=Delete Location Sets
v1.0.business_structure-location_sets.post.multi_delete.notes=<p>This operation deletes one or more location sets.</p><br /><p>The associated Access Control Point is OJS_EDITOR with the edit permission ALLOWED.</p>
v1.0.business_structure-location_sets.post.multi_delete.summary=Deletes one or more location sets.
v1.0.business_structure-location_sets.post.multi_delete.response.200.message=Success
v1.0.business_structure-location_sets.post.multi_delete.response.204.message=Success
v1.0.business_structure-location_sets.post.multi_delete.response.207.message=Partially succeeded in deleting the specified location sets.
v1.0.business_structure-location_sets.post.multi_delete.response.400.message=Incorrect Request Parameter/Invalid Request Body.
v1.0.business_structure-location_sets.post.multi_delete.payload.request.value=The Delete Location Sets request payload.

