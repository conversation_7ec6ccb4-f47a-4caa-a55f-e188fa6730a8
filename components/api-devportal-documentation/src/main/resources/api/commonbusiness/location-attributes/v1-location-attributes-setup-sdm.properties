v1.0.locationattribute-location-attribute-setup-sdm.name=Location Attributes SDM
v1.0.locationattribute-location-attribute-setup-sdm.description=The labor attribute resource for SDM.
v1.0.locationattribute-location-attribute-setup-sdm.parent=root.wco2.default

#GET all keys
v1.0.locationattribute-location-attribute-setup-sdm.get.keys.nickname=Retrieve Location Attribute SDM Keys
v1.0.locationattribute-location-attribute-setup-sdm.get.keys.notes=This operation retrieves SDM keys for all Location Attributes.
v1.0.locationattribute-location-attribute-setup-sdm.get.keys.summary=Returns a list of SDM keys for all Location Attributes.
v1.0.locationattribute-location-attribute-setup-sdm.get.keys.response.200.message=Success.

#GET by key-name
v1.0.locationattribute-location-attribute-setup-sdm.get.nickname=Return Location Attribute for SDM
v1.0.locationattribute-location-attribute-setup-sdm.get.notes=This operation returns a Location Attribute by SDM key-name pair.
v1.0.locationattribute-location-attribute-setup-sdm.get.summary=Returns a Location Attribute for given key-name pair.
v1.0.locationattribute-location-attribute-setup-sdm.get.queryparam.key.value=Location Attribute key
v1.0.locationattribute-location-attribute-setup-sdm.get.queryparam.name.value=Location Attribute name
v1.0.locationattribute-location-attribute-setup-sdm.get.response.200.message=Success.
v1.0.locationattribute-location-attribute-setup-sdm.get.response.400.message=<ul><li>[WCO-129902] - The following object reference is invalid: {paramName}.</li><li>[WCO-129910] - At least one of the following parameters is mandatory: {paramNames}.</li><li>[WFC-111206] - Please provide at least 1 of id/qualifier in ref.</li></ul>
v1.0.locationattribute-location-attribute-setup-sdm.get.response.404.message=<ul><li>[WCO-129904] - Entity not found. Type: Attribute. Name or ID: {id}</li></ul>

#POST
v1.0.locationattribute-location-attribute-setup-sdm.create.nickname=Create a Location Attribute for SDM
v1.0.locationattribute-location-attribute-setup-sdm.create.notes=This operation creates a Location Attribute.
v1.0.locationattribute-location-attribute-setup-sdm.create.summary=Create a Location Attribute
v1.0.locationattribute-location-attribute-setup-sdm.create.param.labor_category=The location attribute to create.
v1.0.locationattribute-location-attribute-setup-sdm.create.response.200.message=Success.
v1.0.locationattribute-location-attribute-setup-sdm.create.response.400.message=<ul><li>[WCO-129901] - You must specify the following parameter: {paramName}.</li><li>[WCO-129919] - The following parameter can not be empty: {paramName}.</li><li>[WCO-129921] - The attribute name: {paramName} is not unique.</li><li>[WCO-129925] - Completed with error(s). Detailed errors should be wrapped within this exception.</li><li>[WCO-129927] - SDM key must be equal to the name.</li></ul>

#PUT
v1.0.locationattribute-location-attribute-setup-sdm.update.key.nickname=Update a Location Attribute for SDM
v1.0.locationattribute-location-attribute-setup-sdm.update.key.notes=This operation updates a Location Attribute by key
v1.0.locationattribute-location-attribute-setup-sdm.update.key.summary=Updates a Location Attribute.
v1.0.locationattribute-location-attribute-setup-sdm.update.key.pathparam.key.value=Location Attribute key for SDM
v1.0.locationattribute-location-attribute-setup-sdm.update.key.param.labor_category=The Location Attribute to update.
v1.0.locationattribute-location-attribute-setup-sdm.update.key.response.200.message=Success.
v1.0.locationattribute-location-attribute-setup-sdm.update.key.response.400.message=<ul><li>[WCO-129925] - Completed with error(s). Detailed errors should be wrapped within this exception.</li></ul>
v1.0.locationattribute-location-attribute-setup-sdm.update.key.response.404.message=<ul><li>[WCO-129904] - Entity not found. Type: Attribute. Name or ID: {id}</li></ul>

#POST
v1.0.locationattribute-location-attribute-setup-sdm.dependencies.nickname=Retrieve Location Attribute Dependencies for SDM
v1.0.locationattribute-location-attribute-setup-sdm.dependencies.notes=This operation get a Location Attribute's dependencies by SDM keys and returns SDMDependenciesResponses.
v1.0.locationattribute-location-attribute-setup-sdm.dependencies.summary=Return Location Attribute's dependencies.
v1.0.locationattribute-location-attribute-setup-sdm.dependencies.response.200.message=Success.
v1.0.locationattribute-location-attribute-setup-sdm.dependencies.response.400.message=<ul><li>[WCO-129901] - You must specify the following parameter: {paramName}.</li><li>[WCO-129902] - The following object reference is invalid: {paramName}.</li><li>[WCO-129905] - The following collection cannot be empty: {listName}.</li><li>[WCO-129913] - Entity not found. Type: {object}. Name or ID: {qualifier/id}.</li><li>[WCO-129926] - Your request includes two or more of the following parameters: ids, qualifiers, refs. These parameters are mutually exclusive.</li><li>[WCO-129927] - SDM key must be equal to the name.</li></ul>
v1.0.locationattribute-location-attribute-setup-sdm.dependencies.response.404.message=<ul><li>[WCO-129904] - Entity not found. Type: Attribute. Name or ID: {id}</li></ul>
v1.0.locationattribute-location-attribute-setup-sdm.dependencies.param.entities=A Location Attribute's SDM keys.