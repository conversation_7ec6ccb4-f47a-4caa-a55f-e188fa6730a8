v1.0.datacollection-bioconsent_history.parent=root.wco.default
v1.0.datacollection-bioconsent_history.name=Biometric Consent History
v1.0.datacollection-bioconsent_history.description=This resource allows you to retrieve employee biometric consent history data.

#MultiRead
v1.0.datacollection-bioconsent_history.multiread.nickname=Retrieve Biometric Consent History
v1.0.datacollection-bioconsent_history.multiread.notes=This operation returns employee biometric consent history by object references.\n\nThe associated Access Control Point is UDM_BIOMETRIC_CONSENT_HISTORY.\n\n## Service limit\n\nA service limit constrains the number of employees that can be processed in a single request.\n\n* The __Number of Employees__ cannot exceed 1,000.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.
v1.0.datacollection-bioconsent_history.multiread.summary=Returns employee biometric consent history.
v1.0.datacollection-bioconsent_history.multiread.response.200.message=Successfully retrieved employee biometric consent history.
v1.0.datacollection-bioconsent_history.multiread.response.207.message=Completed with error(s). Detailed errors should be wrapped within this exception.
v1.0.datacollection-bioconsent_history.multiread.response.403.message=Access rights violation: you do not have the required function access profile to perform this task.
v1.0.datacollection-bioconsent_history.multiread.response.400.message=<ul><li>[UDM-00100] - An exception was encountered processing the request.</li><li>[UDM-00101] - Please provide a valid key. Options are: personid, personnumber, badgenumber</li><li>[UDM-00102] - Please provide valid values for the specified key.</li><li>[UDM-00103] - Please provide a valid multi_key. Options are: [ aoid ], [ aoid, coid ], [ coid, aoid ]</li><li>[UDM-00104] - Please provide valid multi_key_values for the specified multi_key pair.</li><li>[UDM-00105] - Please provide either a valid key or multi_key pair.</li><li>[UDM-00106] - Please provide either a valid key or valid multi_key pair, not both.</li><li>[UDM-00107] - You can only provide one type of filter criteria at a time. Either the employees or the Hyperfind filter.</li><li>[UDM-00110] - Please provide a valid multi_key. Include coid as it is required. Options are: [ aoid, coid ], [ coid, aoid ]</li><li>[WCO-101726] - You must provide a valid index {indexValue}.</li><li>[WCO-101727] - You must provide a valid count {countValue}.</li><li>[WCO-101338] - Supplied Count's value exceeds maximum allowed limit:{countLimit}.</li></ul>

#Employee
v1.0.udm.employee.apimodel.description=Biometric Consent Employee Model
v1.0.udm.employee.apimodelproperty.aoid.description=The associate identifier.
v1.0.udm.employee.apimodelproperty.badgeNumber.description=The badge number of an employee.
v1.0.udm.employee.apimodelproperty.coid.description=The client identifier.
v1.0.udm.employee.apimodelproperty.fullName.description=The full name of an employee.
v1.0.udm.employee.apimodelproperty.personId.description=The person ID of an employee.
v1.0.udm.employee.apimodelproperty.personNumber.description=A number that uniquely identifies a person. This is not an employee ID.

#EmployeeCriteria
v1.0.udm.employeecriteria.apimodel.description=Criteria for employee information.
v1.0.udm.employeecriteria.apimodelproperty.key.description=The key associated with employee information.
v1.0.udm.employeecriteria.apimodelproperty.values.description=The search values associated with employee information.
v1.0.udm.employeecriteria.apimodelproperty.multiKey.description=The multi-search key pair. The key pair can be Associate Object ID (AOID) and Client Object ID (COID).
v1.0.udm.employeecriteria.apimodelproperty.multiKeyValues.description=The multi-search value pair. The value pair can be Associate Object ID (AOID) and Client Object ID (COID).
