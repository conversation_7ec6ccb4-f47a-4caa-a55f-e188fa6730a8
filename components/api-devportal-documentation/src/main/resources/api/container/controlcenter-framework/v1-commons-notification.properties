#Comments for Properties
#Wed Jan 18 16:23:58 IST 2017
v1.0.commons-notifications.name=Messaging Notifications
v1.0.messaging-notifications.name=Messaging Notifications
v1.0.commons-notifications.parent=root.wco.default
v1.0.commons-notifications.description=This resource allows you to send and retrieve common messaging notifications.

v1.0.commons-notifications.get.event_actions.nickname=Retrieve Event Actions
v1.0.commons-notifications.get.event_actions.summary=Returns a list of actions associated with events.
v1.0.commons-notifications.get.event_actions.notes=This operation returns a list of actions associated with event types. Event types are a combination of domain and event and have actions associated with them which are displayed on the Control Center for the notification belonging to a particular event type. The event types also determine the category to which a notification belongs in Control Center.
v1.0.commons-notifications.get.event_actions.response.200.message=Successful retrieval of event actions for the logged in user.
v1.0.commons-notifications.get.event_actions.response.400.message=[CONTROL_CENTER-10001]-Event actions could not be loaded. Try again.

v1.0.commons-notifications.get.{message_id}.nickname=Retrieve Notification Details
v1.0.commons-notifications.get.{message_id}.summary=Returns notification details.
v1.0.commons-notifications.get.{message_id}.notes=This operation returns details about a particular notification based on the provided message UUID.
v1.0.commons-notifications.get.{message_id}.response.200.message=Successful retrieval of notification details for the logged in user.
v1.0.commons-notifications.get.{message_id}.response.400.message=[CONTROL_CENTER-10001]-Notification detail could not be loaded. Try again.
v1.0.messaging-notifications.get.{message_id}.pathparam.message_id.value=UUID of the message.

v1.0.commons-notifications.post.review.nickname=Review Notifications by ID
v1.0.commons-notifications.post.review.summary=Reviews a list of notifications by ID.
v1.0.commons-notifications.post.review.notes=This operation reviews and acknowledges notifications asynchronously based on the list of message UUIDs provided. Once reviewed and acknowledged, notifications disappear from the Control Center Preview Panel and are moved to the Reviewed folder in Control Center.
v1.0.commons-notifications.post.review.response.200.message=A list of notifications successfully reviewed for the logged in user.
v1.0.commons-notifications.post.review.response.400.message=[CONTROL_CENTER-10001]-Notifications could not be reviewed. Try again.
v1.0.commons-notifications.post.review.param.messageids.value=A list of Notification Message UUIDs.
v1.0.commons-notifications.post.review.queryparam.reviewforall.value=A deprecated Boolean that has no effect.

v1.0.commons-notifications.post.multi_delete.nickname=Delete Multiple Notifications
v1.0.commons-notifications.post.multi_delete.summary=Deletes a list of notifications.
v1.0.commons-notifications.post.multi_delete.notes=This operation deletes notifications asynchronously based on the list of message UUIDs provided. Once deleted, notifications disappear from Control Center and the Preview Panel.
v1.0.commons-notifications.post.multi_delete.response.200.message=A list of notifications successfully deleted for the logged in user.
v1.0.commons-notifications.post.multi_delete.response.400.message=[CONTROL_CENTER-10001]-Notifications could not be deleted. Try again.
v1.0.commons-notifications.post.multi_delete.param.messageids.value=A list of Notification Message UUIDs.

v1.0.commons-notifications.post.review-item-{item_id}.nickname=Review Notifications by Item ID
v1.0.commons-notifications.post.review-item-{item_id}.summary=Reviews notifications.
v1.0.commons-notifications.post.review-item-{item_id}.notes=This operation reviews notifications asynchronously by item ID, which is the reference ID of the entity associated with the notification. Once reviewed, notifications disappear from the Control Center Preview Panel and are moved to the Reviewed folder in Control Center.
v1.0.commons-notifications.post.review-item-{item_id}.response.200.message=A list of notifications successfully reviewed for the logged in user.
v1.0.commons-notifications.post.review-item-{item_id}.response.400.message=[CONTROL_CENTER-10001]-Notifications could not be reviewed. Try again.
v1.0.commons-notifications.post.review-item-{item_id}.pathparam.item_id.value=The ID of the item.
v1.0.commons-notifications.post.review-item-{item_id}.queryparam.category.value=Name of the category.
v1.0.commons-notifications.post.review-item-{item_id}.queryparam.person_ids.value=Comma-separated list of person IDs.

v1.0.commons-notifications.get.count.nickname=Retrieve Notification Count
v1.0.commons-notifications.get.count.summary=Returns a count of all notifications for a user.
v1.0.commons-notifications.get.count.notes=This operation returns the category-based count along with the total count of new notifications for the logged-in user.
v1.0.commons-notifications.get.count.response.200.message=Successful retrieval of category wise count of notifications for the logged-in user.
v1.0.commons-notifications.get.count.response.400.message=[CONTROL_CENTER-10001]-Notification count could not be loaded. Try again.

v1.0.commons-notifications.post.multi_delete-item-{item_id}.nickname=Delete Notifications by Item ID
v1.0.commons-notifications.post.multi_delete-item-{item_id}.summary=Deletes notifications.
v1.0.commons-notifications.post.multi_delete-item-{item_id}.notes=This operation deletes notifications associated with the provided item ID, which is the reference ID of the entity associated with the notification. Once deleted, notifications disappear from Control Center and the Preview Panel.
v1.0.commons-notifications.post.multi_delete-item-{item_id}.response.200.message=A list of notifications successfully deleted for the logged in user.
v1.0.commons-notifications.post.multi_delete-item-{item_id}.response.400.message=[CONTROL_CENTER-10001]-Notifications could not be deleted. Try again.
v1.0.commons-notifications.post.multi_delete-item-{item_id}.pathparam.item_id.value=The ID of the item.
v1.0.commons-notifications.post.multi_delete-item-{item_id}.queryparam.category.value=Name of the category.
v1.0.commons-notifications.post.multi_delete-item-{item_id}.queryparam.person_ids.value=Comma-separated list of person IDs.

v1.0.commons-notifications.get.item-{item_id}.nickname=Retrieve Notifications by Item ID
v1.0.commons-notifications.get.item-{item_id}.summary=Returns a list of notifications for a given item ID.
v1.0.commons-notifications.get.item-{item_id}.notes=This operation returns a list of notifications associated with the provided item ID, which is the reference ID of the entity associated with the notification.
v1.0.commons-notifications.get.item-{item_id}.response.200.message=Successful retrieval of list of notifications for the logged in user.
v1.0.commons-notifications.get.item-{item_id}.response.400.message=[CONTROL_CENTER-10001]-Notifications could not be loaded. Try again.
v1.0.commons-notifications.get.item-{item_id}.queryparam.item_id.value=The ID of the item.
v1.0.commons-notifications.get.item-{item_id}.queryparam.category.value=Name of the category.

v1.0.commons-notifications.post.item.nickname=Retrieve Notifications by Item IDs
v1.0.commons-notifications.post.item.summary=Returns a list of notifications for a list of item IDs.
v1.0.commons-notifications.post.item.notes=This operation returns a list of all new and reviewed notifications associated with the list of item IDs provided.
v1.0.commons-notifications.post.item.response.200.message=Successful retrieval of list of notifications for the logged in user.
v1.0.commons-notifications.post.item.response.400.message=[CONTROL_CENTER-10001]-Notifications could not be loaded. Try again.
v1.0.commons-notifications.post.item.queryparam.category.value=Name of the category.

v1.0.commons-notifications.get.config.nickname=Retrieve Control Center Configuration
v1.0.commons-notifications.get.config.summary=Returns the Control Center configuration.
v1.0.commons-notifications.get.config.notes=This operation returns the Control Center configuration for the logged-in user. The configuration consists of the Control Center Access Point permission and the categories accessible to the logged-in user based on the Control Center Profile assigned through the Display Profile.
v1.0.commons-notifications.get.config.response.200.message=Successful retrieval of control center configuration for the logged in user.
v1.0.commons-notifications.get.config.response.400.message=[CONTROL_CENTER-10001]-Control center configuration could not be loaded. Try again.

v1.0.commons-notifications.get.currentpath.nickname=Retrieve Notifications
v1.0.commons-notifications.get.currentpath.summary=Returns a list of notifications for the logged-in user.
v1.0.commons-notifications.get.currentpath.notes=This operation returns a list of all new or reviewed notifications according to the access privileges of the logged-in user. Notifications are further filtered according to the provided category for the selected timeframe or based on the person IDs contained in the specified Hyperfind.</p>
v1.0.commons-notifications.get.currentpath.response.200.message=Successful retrieval of list of notifications for the logged in user.
v1.0.commons-notifications.get.currentpath.response.400.message=[CONTROL_CENTER-10001]-Notifications could not be loaded. Try again.
v1.0.commons-notifications.get.currentpath.queryparam.category.value=The name of a category.
v1.0.commons-notifications.get.currentpath.queryparam.status.value=The acknowledgement status filter. Valid values include `new` and `reviewed`. If any other string is passed, the status filter is ignored.
v1.0.commons-notifications.get.currentpath.queryparam.hfid.value=The ID of a particular Hyperfind.
v1.0.commons-notifications.get.currentpath.queryparam.start_date.value=The start date of a date range in ISO 8601 date format (YYYY-MM-DD hh:mm:ss). For example, September 27, 2022 at 6 PM is represented as 2022-09-27 18:00:00.000.
v1.0.commons-notifications.get.currentpath.queryparam.end_date.value=The end date of a date range in ISO 8601 date format (YYYY-MM-DD hh:mm:ss). For example, September 27, 2022 at 6 PM is represented as 2022-09-27 18:00:00.000.
v1.0.commons-notifications.get.currentpath.queryparam.page_num.value=The page number of the resulting set.

v1.0.commons-notifications.apply_read.nickname=Retrieve Bulk Notifications
v1.0.commons-notifications.apply_read.notes=This operation returns a list of all new or reviewed notifications according to the access privileges of the logged-in user. Notifications are further filtered according to the provided category for the selected timeframe or based on the person IDs contained in the specified Hyperfind.\n\nThe associated Access Control Point is NOTIFICATION_API_V1.\n\n## Service limit\n\nA service limit constrains the total number of notifications returned and the number of notifications returned in each paginated response page.\n\n* The __Total Number of Notifications__ cannot exceed 50,000.\n\n* The __Number of Notifications in Each Page__ cannot exceed 1,000.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.\n\n## Details\n\nThe `types` array accepts the following values:\n\n| type                                                       | domain     |\n|:----------------                                           | --------:  |\n| ATTENDANCE                                                 | attendance |\n| HCA_TRANSACTIONAL_DATA_PROCESS                             | HCA        |\n| AUDITOR_RUN                                                | KPIF       |\n| KPI_Historical_Load                                        | KPIF       |\n| LEAVE                                                      | leave      |\n| LEAVE_REQUEST                                              | leave      |\n| DATE_BASED_NOTIFICATION                                    | leave      |\n| LIMIT_BASED_NOTIFICATION                                   | leave      |\n| DOCUMENT_REMINDER                                          | leave      |\n| ANNOUNCEMENTS                                              | platform   |\n| PROACTIVELY_GUIDE_EMPLOYEES                                | platform   |\n| Integration_Completed                                      | platform   |\n| Integration_Started                                        | platform   |\n| Reference_Table_Upload_Completed                           | platform   |\n| Reference_Table_Upload_Failed                              | platform   |\n| Integration_Completed_Notification                         | platform   |\n| SCHEDULEREPORT                                             | reporting  |\n| VisibilityPeriod                                           | scheduler  |\n| PostSchedule                                               | scheduler  |\n| UnPostSchedule                                             | scheduler  |\n| RepostAfterEdit                                            | scheduler  |\n| FILL_OPEN_SHIFT                                            | scheduler  |\n| FILL_OPEN_SHIFT_OUTCOME                                    | scheduler  |\n| INACTIVE_REQUEST                                           | scheduler  |\n| NOTIFY_MY_COLLEAGUES                                       | scheduler  |\n| TIME_OFF                                                   | scheduler  |\n| AVAILABILITY_REQUEST                                       | scheduler  |\n| TIMECARD_CHANGE_REQUEST                                    | scheduler  |\n| AVAILABILITY_PATTERN_REQUEST                               | scheduler  |\n| OPEN_SHIFT                                                 | scheduler  |\n| SELF_SCHEDULE                                              | scheduler  |\n| COVER                                                      | scheduler  |\n| SWAP                                                       | scheduler  |\n| EMP_MEAL_BREAK_OVERTIME                                    | timekeeper |\n| EMP_MISSED_PUNCH_EXCEPTION                                 | timekeeper |\n| TIMECARD_REQUEST_REVIEW                                    | timekeeper |\n| requestPeriod                                              | timekeeper |\n| Timekeeping                                                | timekeeper |\n| WORKFLOW_TASK                                              | workflow   |
v1.0.commons-notifications.apply_read.summary=Returns a list of notifications for the selected time frame and provided input filter.
v1.0.commons-notifications.apply_read.response.200.message=Successful retrieval of list of notifications for the provide input filter.
v1.0.commons-notifications.apply_read.response.400.message=* [WFP-107023]-An invalid notification type '{invalidType}' was passed in the request.\n* [CONTROL_CENTER-10001]-Notifications could not be loaded. Try again.\n* [WFP-107026]-You must specify both startDateTime and endDateTime.\n* [WFP-107027]-Invalid date time format. Provide ISO-8601 formatted startDateTime and endDateTime ('yyyy-mm-ddTHH:MM:SS').\n* [WFP-107028]-If you specify pageToken in a request payload, you cannot include other request properties such as startDateTime and endDateTime along with hyperfind, allEmployees, types, or employees.\n* [WFP-107029]-You must specify only one of the following properties in a given request payload: hyperfind, allEmployees, or employees.\n* [WFP-107030]-Could not find the following Hyperfind query in the database - qualifier: {qualifier}.\n* [WFP-107031]-No data exists for the specified employee set.\n* [WFP-107032]-You specified an invalid or expired token.\n* [WFP-107036]-A blank notification type was passed in the request.\n* [WFP-107017]-Start date time cannot be after end date time.\n* [WFP-114012]-You are not authorized to perform this operation. Contact the system administrator to obtain permission.\n* [WFP-00312]-Could not find the HyperFind query in the database - ID: {id}.
v1.0.commons-notifications.apply_read.response.413.message=* [WFP-107035]-The query result exceeded the maximum threshold of {limit}.
v1.0.commons-notifications.apply_read.param.notificationrequestdto.value=The Retrieve Bulk Notifications request payload, which contains `startDateTime`, `endDateTime`, `hyperfind`, `types`, `allEmployees`, `employees` or `pageToken`.

v1.0.commons.notifications.headerparam.value=A header defining the language for translated strings. Values consist of locale language tags such as en-US, en-IN, or fr-FR.
