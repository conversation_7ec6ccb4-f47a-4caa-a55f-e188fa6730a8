#Comments for Properties
#Tue Apr 25 12:20:15 IST 2017
v1.0.business_analytics-mapping_categories.name=MappingCategory
v1.0.business_analytics-mapping_categories.parent=root.wfp.default
v1.0.business_analytics-mapping_categories.description=Name of the MappingCategoryRESTService that retrieves and updates the mapping category definitions for the KPI Builder.
v1.0.business_analytics-mapping_categories.get.{id}.nickname=Name of the retrieve operation. Default = Get Mapping Category by ID.
v1.0.business_analytics-mapping_categories.get.{id}.summary=Summary description of the retrieve operation.
v1.0.business_analytics-mapping_categories.get.{id}.notes=Description of the retrieve operation.
v1.0.business_analytics-mapping_categories.get.{id}.response.200.message=Successfully retrieved.
v1.0.business_analytics-mapping_categories.get.{id}.response.400.message=Bad Request.
v1.0.business_analytics-mapping_categories.get.{id}.response.500.message=Internal server error.
v1.0.business_analytics-mapping_categories.get.{id}.pathparam.id.value=The ID for the mapping category definition to retrieve.
v1.0.business_analytics-mapping_categories.get.currentpath.nickname=Get Mapping Category.
v1.0.business_analytics-mapping_categories.get.currentpath.summary=A list of all mapping categories based on the name, ID and name of the domain function, ID and name of the mapping category type, and any combination of parameters in the query.
v1.0.business_analytics-mapping_categories.get.currentpath.notes=A list of mapping category definitions. For a standard KPI, the name must correspond to the key in the database. The name cannot be localized.
v1.0.business_analytics-mapping_categories.get.currentpath.response.200.message=Successfully retrieved.
v1.0.business_analytics-mapping_categories.get.currentpath.response.400.message=Bad Request.
v1.0.business_analytics-mapping_categories.get.currentpath.response.500.message=Internal server error.
v1.0.business_analytics-mapping_categories.get.currentpath.queryparam.name.value=Name of the mapping category definition.
v1.0.business_analytics-mapping_categories.get.currentpath.queryparam.mapping_category_type_id.value=The ID for the mapping category definition.
v1.0.business_analytics-mapping_categories.get.currentpath.queryparam.mapping_category_type_name.value=Type of mapping category definition.
v1.0.business_analytics-mapping_categories.get.currentpath.queryparam.datasource_id.value=The ID for the data source.
v1.0.business_analytics-mapping_categories.get.currentpath.queryparam.ui_request.value= Flag to represent UI request.
v1.0.business_analytics-mapping_categories.post.currentpath.nickname=Name of the operation to create a mapping category.
v1.0.business_analytics-mapping_categories.post.currentpath.summary=Summary description of the operation to create a mapping category.
v1.0.business_analytics-mapping_categories.post.currentpath.notes=Description of the operation to create a mapping category. 
v1.0.business_analytics-mapping_categories.post.currentpath.response.200.message=Successfully Created.
v1.0.business_analytics-mapping_categories.post.currentpath.response.400.message=Bad Request.
v1.0.business_analytics-mapping_categories.post.currentpath.response.500.message=Internal server error.
v1.0.business_analytics-mapping_categories.post.{id}.nickname=Name of the operation to update a mapping category.
v1.0.business_analytics-mapping_categories.post.{id}.summary=Summary description of the operation to update a mapping category.
v1.0.business_analytics-mapping_categories.post.{id}.notes=Description of the operation to update a mapping category.
v1.0.business_analytics-mapping_categories.post.{id}.response.200.message=Successfully Updated.
v1.0.business_analytics-mapping_categories.post.{id}.response.400.message=Bad Request.
v1.0.business_analytics-mapping_categories.post.{id}.response.500.message=Internal server error.
v1.0.business_analytics-mapping_categories.post.{id}.pathparam.id.value=The ID for the mapping category definition to update.
v1.0.business_analytics-mapping_categories.get.actions.nickname=Get MDUI action Spec
v1.0.business_analytics-mapping_categories.get.actions.summary=Returns MDUI action spec
v1.0.business_analytics-mapping_categories.get.actions.notes=MDUI action Spec operation
v1.0.business_analytics-mapping_categories.get.actions.response.200.message=Successfully retrieved
v1.0.business_analytics-mapping_categories.get.actions.response.400.message=Bad Request
v1.0.business_analytics-mapping_categories.get.actions.response.500.message=Internal server error
v1.0.business_analytics-mapping_categories.get.permissions.nickname=Get MDUI permission Spec
v1.0.business_analytics-mapping_categories.get.permissions.summary=Returns MDUI permissions spec
v1.0.business_analytics-mapping_categories.get.permissions.notes=MDUI permission Spec operation
v1.0.business_analytics-mapping_categories.get.permissions.response.200.message=Successfully retrieved
v1.0.business_analytics-mapping_categories.get.permissions.response.400.message=Bad Request
v1.0.business_analytics-mapping_categories.get.permissions.response.500.message=Internal server error
v1.0.business_analytics-mapping_categories.get.dataspec.nickname=Get MDUI Data Spec
v1.0.business_analytics-mapping_categories.get.dataspec.summary=Returns MDUI Data spec
v1.0.business_analytics-mapping_categories.get.dataspec.notes=MDUI Data Spec operation
v1.0.business_analytics-mapping_categories.get.dataspec.response.200.message=Successfully retrieved
v1.0.business_analytics-mapping_categories.get.dataspec.response.400.message=Bad Request
v1.0.business_analytics-mapping_categories.get.dataspec.response.500.message=Internal server error
v1.0.business_analytics-mapping_categories.get.uispec.nickname=Get MDUI UI Spec
v1.0.business_analytics-mapping_categories.get.uispec.summary=Returns MDUI UI spec
v1.0.business_analytics-mapping_categories.get.uispec.notes=MDUI UI Spec operation
v1.0.business_analytics-mapping_categories.get.uispec.response.200.message=Successfully retrieved
v1.0.business_analytics-mapping_categories.get.uispec.response.400.message=Bad Request
v1.0.business_analytics-mapping_categories.get.uispec.response.500.message=Internal server error
