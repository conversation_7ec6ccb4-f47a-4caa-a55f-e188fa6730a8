#Comments for Properties
v1.0.scheduling-shift_profile_sets.name=Shift Profile Sets
v1.0.scheduling-shift_profile_sets.parent=root.wfs.default
v1.0.scheduling-shift_profile_sets.description=This resource allows a manager to create, retrieve, update and delete Shift Profile Sets.\n\nShift profile sets give the Schedule Generator more control over generated shifts by allowing shift profiles to apply rules to shifts.\n\nA Service Limit applies to some of the bulk operations against this resource. Refer to the [Limits](doc:limits-doc) topic for more information.

v1.0.scheduling-shift_profile_sets.get.{shiftprofilesetid}.nickname=Retrieve Shift Profile Set by ID
v1.0.scheduling-shift_profile_sets.get.{shiftprofilesetid}.notes=<p>This operation returns a shift profile set by ID.</p><br /><p>The associated Access Control Point is SHIFT_PROFILE_SET_ACCESS with action VIEW.</p>
v1.0.scheduling-shift_profile_sets.get.{shiftprofilesetid}.value=Returns a shift profile set.
v1.0.scheduling-shift_profile_sets.get.{shiftprofilesetid}.response.200.message=Successful retrieval of shift profile set.
v1.0.scheduling-shift_profile_sets.get.{shiftprofilesetid}.response.404.message=[WFS-107202]-A Shift Profile Set was not found with the specified ID: {shiftprofileset_reference_value}.
v1.0.scheduling-shift_profile_sets.get.{shiftprofilesetid}.response.403.message=[WFS-107203]-You do not have the correct permissions to perform that operation.
v1.0.scheduling-shift_profile_sets.get.{shiftprofilesetid}.pathparam.id.value=The ID of a shift profile set.

v1.0.scheduling-shift_profile_sets.get.currentpath.nickname=Retrieve All Shift Profile Sets or by Name
v1.0.scheduling-shift_profile_sets.get.currentpath.notes=<p>This operation returns all shift profile sets or one by name.</p><br /><p>The associated Access Control Point is SHIFT_PROFILE_SET_ACCESS with action VIEW.</p>
v1.0.scheduling-shift_profile_sets.get.currentpath.value=Returns all shift profile sets or one by name.
v1.0.scheduling-shift_profile_sets.get.currentpath.response.200.message=Successful retrieval of shift profile set.
v1.0.scheduling-shift_profile_sets.get.currentpath.response.400.message=[WFS-107202]-A Shift Profile Set was not found with the specified qualifier: {shiftprofileset_reference_value}.
v1.0.scheduling-shift_profile_sets.get.currentpath.response.404.message=[WFS-107202]-A Shift Profile Set was not found with the specified qualifier: {shiftprofileset_reference_value}.
v1.0.scheduling-shift_profile_sets.get.currentpath.response.403.message=[WFS-107203]-You do not have the correct permissions to perform that operation.
v1.0.scheduling-shift_profile_sets.get.currentpath.pathparam.name.value=The name of a shift profile set.

v1.0.scheduling-shift_profile_sets.post.multi_read.nickname=Retrieve Shift Profile Sets
v1.0.scheduling-shift_profile_sets.post.multi_read.notes=This operation returns one or more shift profile sets by object references.\n\nThe associated Access Control Point is SHIFT_PROFILE_SET_ACCESS with action VIEW.\n\n## Service limit\n\nA service limit constrains the number of shift profile sets that can be processed in a single request.\n\n* The __Number of Shift Profile Sets__ cannot exceed 20.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.
v1.0.scheduling-shift_profile_sets.post.multi_read.value=Returns one or more shift profile sets by object references.
v1.0.scheduling-shift_profile_sets.post.multi_read.response.200.message=Successful retrieval of one or more of shift profile sets.
v1.0.scheduling-shift_profile_sets.post.multi_read.response.207.message=[WCO-101272]-Completed with error(s). Detailed errors should be wrapped within this exception.
v1.0.scheduling-shift_profile_sets.post.multi_read.response.403.message=[WFS-107203]-You do not have the correct permissions to perform that operation.
v1.0.scheduling-shift_profile_sets.post.multi_read.response.400.message=<ul><li>[WFS-111208]-At least one of the criteria must be specified. Parameter: {paramName}</li><li>[WFS-111201]-Missing mandatory input parameter {param}.</li></ul>
v1.0.scheduling-shift_profile_sets.post.multi_read.response.413.message=[WFP-90003]-You exceeded the service limit for operations against the following resource: {resource}. No more than 20 items are permitted per operation. Batch your requests in chunks of 20 or less.
v1.0.scheduling-shift_profile_sets.post.multi_read.payload.request.value=The Retrieve Shift Profile Sets request payload, which consists of a set of shift profile sets objects references.

v1.0.scheduling-shift_profile_sets.post.create.nickname=Create Shift Profile Set
v1.0.scheduling-shift_profile_sets.post.create.notes=<p>This operation creates a shift profile set.</p><br /><p>The associated Access Control Point is SHIFT_PROFILE_SET_ACCESS with action EDIT.</p>
v1.0.scheduling-shift_profile_sets.post.create.value=Creates a shift profile set.
v1.0.scheduling-shift_profile_set.post.create.value=Creates a shift profile set.
v1.0.scheduling-shift_profile_sets.post.create.response.200.message=Successfully created Shift Profile set.
v1.0.scheduling-shift_profile_sets.post.create.response.400.message=<ul><li>[WFS-107205]-A Shift Profile Set already exists with the name: {shiftProfileSet_name}.</li><li>[WFS-107206]-There is a duplicate Shift Profile Instance: {shiftProfileInstance_name}.</li><li>[WFS-107207]-The following consistency validations failed: {errorMessages}.</li></ul>
v1.0.scheduling-shift_profile_sets.post.create.response.403.message=[WFS-107203]-You do not have the correct permissions to perform that operation.
v1.0.scheduling-shift_profile_sets.post.create.shiftprofileset.value=The Create Shift Profile Set request payload.

v1.0.scheduling-shift_profile_sets.post.multi_create.nickname=Create Shift Profile Sets
v1.0.scheduling-shift_profile_sets.post.multi_create.notes=This operation creates one or more shift profile sets.\n\nThe associated Access Control Point is SHIFT_PROFILE_SET_ACCESS with action EDIT.\n\n## Service limit\n\nA service limit constrains the number of shift profile sets that can be processed in a single request.\n\n* The __Number of Shift Profile Sets__ cannot exceed 20.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.
v1.0.scheduling-shift_profile_sets.post.multi_create.value=Creates one or more shift profile sets.
v1.0.scheduling-shift_profile_sets.post.multi_create.response.200.message=Successfully updated a list of Shift Profile Sets.
v1.0.scheduling-shift_profile_sets.post.multi_create.response.400.message=<ul><li>[WFS-111208]-At least one of the criteria must be specified. Parameter: {paramName}</li><li>[WCO-101271]-Completed with error(s). Detailed errors should be wrapped within this exception.</li></ul>
v1.0.scheduling-shift_profile_sets.post.multi_create.response.413.message=[WFP-90003]-You exceeded the service limit for operations against the following resource: {resource}. No more than 20 items are permitted per operation. Batch your requests in chunks of 20 or less.
v1.0.scheduling-shift_profile_sets.post.multi_create.response.207.message=[WCO-101272]-Completed with error(s). Detailed errors should be wrapped within this exception.
v1.0.scheduling-shift_profile_sets.post.multi_create.response.403.message=[WFS-107203]-You do not have the correct permissions to perform that operation.
v1.0.scheduling-shift_profile_sets.post.multi_create.shiftprofilesets.value=The Create Shift Profile Sets request payload.

v1.0.scheduling-shift_profile_sets.put.update.nickname=Update Shift Profile Set by ID
v1.0.scheduling-shift_profile_sets.put.update.notes=<p>This operation updates a shift profile set by ID.</p><br /><p>The associated Access Control Point is SHIFT_PROFILE_SET_ACCESS with action EDIT.</p>
v1.0.scheduling-shift_profile_sets.put.update.value=Updates a shift profile set.
v1.0.scheduling-shift_profile_sets.put.update.response.200.message=Successfully updated Shift Profile set.
v1.0.scheduling-shift_profile_sets.put.update.response.404.message=[WFS-107202]-A Shift Profile Set was not found with the specified ID: {shiftprofileset_reference_value}.
v1.0.scheduling-shift_profile_sets.put.update.response.400.message=[WFS-111209]-ID in payload not consistent with ID from resource URL.
v1.0.scheduling-shift_profile_sets.put.update.response.403.message=[WFS-107203]-You do not have the correct permissions to perform that operation.
v1.0.scheduling-shift_profile_sets.put.update.pathparam.shiftprofilesetid.value=An ID that uniquely identifies a shift profile set.
v1.0.scheduling-shift_profile_sets.put.update.shiftprofileset.value=The Update Shift Profile Set by ID request payload.

v1.0.scheduling-shift_profile_sets.post.multi_update.nickname=Update Shift Profile Sets
v1.0.scheduling-shift_profile_sets.post.multi_update.notes=This operation updates one or more shift profile sets.\n\nThe associated Access Control Point is SHIFT_PROFILE_SET_ACCESS with action EDIT.\n\n## Service limit\n\nA service limit constrains the number of shift profile sets that can be processed in a single request.\n\n* The __Number of Shift Profile Sets__ cannot exceed 20.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.
v1.0.scheduling-shift_profile_sets.post.multi_update.value=Updates one or more shift profile sets.
v1.0.scheduling-shift_profile_sets.post.multi_update.response.200.message=Successfully updated a list of Shift Profile Sets.
v1.0.scheduling-shift_profile_sets.post.multi_update.response.400.message=<ul><li>[WFS-111208]-At least one of the criteria must be specified. Parameter: {paramName}</li><li>[WCO-101271]-Completed with error(s). Detailed errors should be wrapped within this exception.</li></ul>
v1.0.scheduling-shift_profile_sets.post.multi_update.response.413.message=[WFP-90003]-You exceeded the service limit for operations against the following resource: {resource}. No more than 20 items are permitted per operation. Batch your requests in chunks of 20 or less.
v1.0.scheduling-shift_profile_sets.post.multi_update.response.207.message=[WCO-101272]-Completed with error(s). Detailed errors should be wrapped within this exception.
v1.0.scheduling-shift_profile_sets.post.multi_update.response.403.message=[WFS-107203]-You do not have the correct permissions to perform that operation.
v1.0.scheduling-shift_profile_sets.post.multi_update.shiftprofilesets.value=The Update Shift Profile sets request payload.

v1.0.scheduling-shift_profile_sets.delete.{shiftprofilesetid}.nickname=Delete Shift Profile Set by ID
v1.0.scheduling-shift_profile_sets.delete.{shiftprofilesetid}.notes=<p>This operation deletes a shift profile set by ID.</p><br /><p>The associated Access Control Point is SHIFT_PROFILE_SET_ACCESS with action EDIT.</p>
v1.0.scheduling-shift_profile_sets.delete.{shiftprofilesetid}.summary=Deletes a shift profile set.
v1.0.scheduling-shift_profile_sets.delete.{shiftprofilesetid}.response.204.message=Successfully deleted one shift profile set.
v1.0.scheduling-shift_profile_sets.delete.{shiftprofilesetid}.response.404.message=[WFS-107202]-A Shift Profile Set was not found with the specified ID: {shiftprofileset_reference_value}.
v1.0.scheduling-shift_profile_sets.delete.{shiftprofilesetid}.response.403.message=[WFS-107203]-You do not have the correct permissions to perform that operation.
v1.0.scheduling-shift_profile_sets.delete.{shiftprofilesetid}.pathparam.shiftprofilesetid.value=An ID that uniquely identifies a shift profile set.

v1.0.scheduling-shift_profile_sets.multi_delete.nickname=Delete Shift Profile Sets
v1.0.scheduling-shift_profile_sets.multi_delete.notes=This operation deletes one or more shift profile sets.\n\nThe associated Access Control Point is SHIFT_PROFILE_SET_ACCESS with action EDIT.\n\n## Service limit\n\nA service limit constrains the number of shift profile sets that can be processed in a single request.\n\n* The __Number of Shift Profile Sets__ cannot exceed 20.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.
v1.0.scheduling-shift_profile_sets.multi_delete.value=Deletes one or more shift profile sets.
v1.0.scheduling-shift_profile_sets.multi_delete.response.204.message=Successfully deleted one or more shift profile sets.
v1.0.scheduling-shift_profile_sets.multi_delete.response.400.message=<ul><li>[WFS-111208]-At least one of the criteria must be specified. Parameter: {paramName}</li><li>[WFS-111201]-Missing mandatory input parameter {param}.</li></ul>
v1.0.scheduling-shift_profile_sets.multi_delete.response.413.message=[WFP-90003]-You exceeded the service limit for operations against the following resource: {resource}. No more than 20 items are permitted per operation. Batch your requests in chunks of 20 or less.
v1.0.scheduling-shift_profile_sets.post.multi_delete.response.413.message=[WFP-90003]-You exceeded the service limit for operations against the following resource: {resource}. No more than 20 items are permitted per operation. Batch your requests in chunks of 20 or less.
v1.0.scheduling-shift_profile_sets.multi_delete.response.207.message=[WCO-101272]-Completed with error(s). Detailed errors should be wrapped within this exception.
v1.0.scheduling-shift_profile_sets.multi_delete.response.403.message=[WFS-107203]-You do not have the correct permissions to perform that operation.
v1.0.scheduling-shift_profile_sets.multi_delete.request.value=The Delete Shift Profile sets request payload, which consists of a set of shift profiles objects references.