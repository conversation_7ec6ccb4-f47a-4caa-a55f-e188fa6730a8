
# Find by person id
v2.0.commons-persons-pay_code_value_profiles.get.{personid}.nickname=Retrieve Paycode Value Profile Assignment by ID
v2.0.commons-persons-pay_code_value_profiles.get.{personid}.value=Returns a person's paycode value profile assignment.
v2.0.commons-persons-pay_code_value_profiles.get.{personid}.notes=<p>This operation returns a person's paycode value profile assignment by person ID.</p><br /><p>The associated Access Control Point is PAYCODE_VALUES_PROFILE_ACCESS.</p>
v2.0.commons-persons-pay_code_value_profiles.get.{personid}.response.200.message=Successfully retrieved a person's paycode value profile assignment by person ID.
v2.0.commons-persons-pay_code_value_profiles.get.{personid}.response.404.message=Not found.
v2.0.commons-persons-pay_code_value_profiles.get.{personid}.response.403.message=You do not have permission to access this resource.
v2.0.commons-persons-pay_code_value_profiles.get.{personid}.response.500.message=Internal server error.
v2.0.commons-persons-pay_code_value_profiles.get.{personid}.pathparam.personid.value=An ID that uniquely identifies an employee. This ID is the same as `employeeId` and is not a person number.

# Find by person number
v2.0.commons-persons-pay_code_value_profiles.get.currentpath.nickname=Retrieve Paycode Value Profile Assignment by Person Number
v2.0.commons-persons-pay_code_value_profiles.get.currentpath.value=Returns a person's paycode value profile assignment.
v2.0.commons-persons-pay_code_value_profiles.get.currentpath.notes=<p>This operation returns a person's paycode value profile assignment by person number.</p><br /><p>The associated Access Control Point is PAYCODE_VALUES_PROFILE_ACCESS.</p>
v2.0.commons-persons-pay_code_value_profiles.get.currentpath.response.200.message=Successfully retrieved a person's paycode value profile assignment by person number.
v2.0.commons-persons-pay_code_value_profiles.get.currentpath.response.400.message=Bad request.
v2.0.commons-persons-pay_code_value_profiles.get.currentpath.response.403.message=You do not have permission to access this resource.
v2.0.commons-persons-pay_code_value_profiles.get.currentpath.response.404.message=Not found.
v2.0.commons-persons-pay_code_value_profiles.get.currentpath.response.500.message=Internal server error.
v2.0.commons-persons-pay_code_value_profiles.get.currentpath.queryparam.person_number.value=A number that uniquely identifies a person. This is not an employee ID.

# Multi find
v2.0.commons-persons-pay_code_value_profiles.post.multi_read.nickname=Retrieve Paycode Value Profile Assignments
v2.0.commons-persons-pay_code_value_profiles.post.multi_read.value=Returns paycode value profile assignments.
v2.0.commons-persons-pay_code_value_profiles.post.multi_read.notes=<p>This operation returns paycode value profile assignments by object references.</p><br /><p>The associated Access Control Point is PAYCODE_VALUES_PROFILE_ACCESS.</p>
v2.0.commons-persons-pay_code_value_profiles.post.multi_read.response.200.message=Successfully retrieved all of a person's paycode value profile assignments.
v2.0.commons-persons-pay_code_value_profiles.post.multi_read.response.207.message=Partially retrieved a person's paycode value profile assignments.
v2.0.commons-persons-pay_code_value_profiles.post.multi_read.response.400.message=Bad request.
v2.0.commons-persons-pay_code_value_profiles.post.multi_read.response.403.message=You do not have permission to access this resource.
v2.0.commons-persons-pay_code_value_profiles.post.multi_read.response.500.message=Internal server error.
v2.0.commons-persons-pay_code_value_profiles.post.multi_read.paycodevaluesprofilesassignmentrequest.value=The search criteria to retrieve single or multiple paycode value profile assignments.

# Update
v2.0.commons-persons-pay_code_value_profiles.put.currentpath.nickname=Update Paycode Value Profile Assignment by ID
v2.0.commons-persons-pay_code_value_profiles.put.currentpath.value=Updates one paycode value profile assignment for a person.
v2.0.commons-persons-pay_code_value_profiles.put.currentpath.notes=<p>This operation updates one paycode value profile assignment for a person by ID.</p><br /><p>The associated Access Control Point is PAYCODE_VALUES_PROFILE_ACCESS.</p>
v2.0.commons-persons-pay_code_value_profiles.put.currentpath.pathparam.personid.value=An ID that uniquely identifies an employee. This ID is the same as `employeeId` and is not a person number.
v2.0.commons-persons-pay_code_value_profiles.put.currentpath.response.200.message=Successfully updated a person's paycode value profile assignment.
v2.0.commons-persons-pay_code_value_profiles.put.currentpath.response.400.message=Bad request.
v2.0.commons-persons-pay_code_value_profiles.put.currentpath.response.403.message=You do not have permission to access this resource.
v2.0.commons-persons-pay_code_value_profiles.put.currentpath.response.404.message=Not found resource.
v2.0.commons-persons-pay_code_value_profiles.put.currentpath.response.500.message=Internal server error.
v2.0.commons-persons-pay_code_value_profiles.put.currentpath.personid.value=An ID that uniquely identifies an employee. This ID is the same as `employeeId` and is not a person number.
v2.0.commons-persons-pay_code_value_profiles.put.currentpath.paycodevalueprofileassignment.value=Paycode value profile assignment associated with a person.

# Multi update
v2.0.commons-persons-pay_code_value_profiles.post.multi_update.nickname=Update Paycode Value Profile Assignments
v2.0.commons-persons-pay_code_value_profiles.post.multi_update.value=Updates paycode value profile assignments.
v2.0.commons-persons-pay_code_value_profiles.post.multi_update.notes=<p>This operation updates paycode value profile assignments for one or more persons.</p><br /><p>The associated Access Control Point is PAYCODE_VALUES_PROFILE_ACCESS.</p>
v2.0.commons-persons-pay_code_value_profiles.post.multi_update.response.200.message=Successfully updated paycode value profile assignments for one or more persons.
v2.0.commons-persons-pay_code_value_profiles.post.multi_update.response.207.message=Partially updated paycode value profile assignments for one or more persons.
v2.0.commons-persons-pay_code_value_profiles.post.multi_update.response.400.message=Bad request.
v2.0.commons-persons-pay_code_value_profiles.post.multi_update.response.403.message=You do not have permission to access this resource.
v2.0.commons-persons-pay_code_value_profiles.post.multi_update.response.500.message=Internal server error.
v2.0.commons-persons-pay_code_value_profiles.post.multi_update.paycodevalueprofileassignments.value=A collection of paycode value profile assignments associated with mutiple persons.

# Delete by person id
v2.0.commons-persons-pay_code_value_profiles.delete.{personid}.nickname=Delete Paycode Value Profile Assignment by ID
v2.0.commons-persons-pay_code_value_profiles.delete.{personid}.value=Deletes a person's paycode value profile assignment.
v2.0.commons-persons-pay_code_value_profiles.delete.{personid}.notes=<p>This operation deletes a person's paycode value profile assignment by person ID.</p><br /><p>The associated Access Control Point is PAYCODE_VALUES_PROFILE_ACCESS.</p>
v2.0.commons-persons-pay_code_value_profiles.delete.{personid}.pathparam.personid.value=An ID that uniquely identifies an employee. This ID is the same as `employeeId` and is not a person number.
v2.0.commons-persons-pay_code_value_profiles.delete.{personid}.response.204.message=Successfully deleted a person's paycode value profile assignment by person ID.
v2.0.commons-persons-pay_code_value_profiles.delete.{personid}.response.200.message=Successfully deleted a person's paycode value profile assignment by person ID.
v2.0.commons-persons-pay_code_value_profiles.delete.{personid}.response.400.message=Bad request.
v2.0.commons-persons-pay_code_value_profiles.delete.{personid}.response.403.message=You do not have permission to access this resource.
v2.0.commons-persons-pay_code_value_profiles.delete.{personid}.response.404.message=Not found.
v2.0.commons-persons-pay_code_value_profiles.delete.{personid}.response.500.message=Internal server error.

# Multi delete
v2.0.commons-persons-pay_code_value_profiles.post.multi_delete.nickname=Delete Paycode Value Profile Assignments
v2.0.commons-persons-pay_code_value_profiles.post.multi_delete.value=Deletes paycode value profile assignments for one or more persons.
v2.0.commons-persons-pay_code_value_profiles.post.multi_delete.notes=<p>This operation deletes paycode value profile assignments for one or more persons.</p><br /><p>The associated Access Control Point is PAYCODE_VALUES_PROFILE_ACCESS.</p>
v2.0.commons-persons-pay_code_value_profiles.post.multi_delete.response.200.message=Successfully deleted paycode value profile assignments for one or more persons.
v2.0.commons-persons-pay_code_value_profiles.post.multi_delete.response.204.message=Successfully deleted paycode value profile assignments for one or more persons.
v2.0.commons-persons-pay_code_value_profiles.post.multi_delete.response.207.message=Partially deleted paycode value profile assignments for one or more persons.
v2.0.commons-persons-pay_code_value_profiles.post.multi_delete.response.400.message=Bad request.
v2.0.commons-persons-pay_code_value_profiles.post.multi_delete.response.403.message=You do not have permission to access this resource.
v2.0.commons-persons-pay_code_value_profiles.post.multi_delete.response.500.message=Internal server error.
v2.0.commons-persons-pay_code_value_profiles.post.multi_delete.paycodevaluesprofilesassignmentrequest.value=The search criteria to delete single or multiple paycode value profile assignments.