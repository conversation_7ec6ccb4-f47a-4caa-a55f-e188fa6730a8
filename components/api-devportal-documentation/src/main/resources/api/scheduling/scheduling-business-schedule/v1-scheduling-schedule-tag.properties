#Comments for Properties
#Wed July 19 2017
v1.0.schedule-tag.name=Schedule Tags
v1.0.schedule-tag.parent=root.wfs.default
v1.0.schedule-tag.description=<p>This resource allows you to create, modify, and delete schedule tags.</p><p>Schedule tags are generic descriptors that can be used to add information to a schedule. They are user-defined and can either be purely informational or they can have business-related impacts (like On Call tags). The main Schedule tag characteristics are the date, the start and end times, the assigned employee, and the associated job or jobs. Schedule tags can be added, deleted, or modified. A Schedule tag is similar to a Shift but with only one segment. It is always associated with the employee's primary job, defined in People Editor. A transfer type Schedule tag is associated with the transferred employee's business structure job, work rule, payer cost center or labor categories.</p>
v1.0.schedule-tag.post.currentpath.nickname=Create Schedule Tag
v1.0.schedule-tag.post.currentpath.summary=Creates a Schedule tag.
v1.0.schedule-tag.post.currentpath.notes=This operation creates a Schedule tag according to the specified parameters. 
v1.0.schedule-tag.post.currentpath.response.200.message=Successful creation of a Schedule tag.
v1.0.schedule-tag.post.currentpath.response.400.message=Schedule tag creation unsuccessful.
v1.0.schedule-tag.post.currentpath.headerparam.tokenrestutil.token_header_name.value=The ID of the current business interaction.
v1.0.schedule-tag.post.payload.tag.value=The Schedule tag to create.
v1.0.schedule-tag.get.{tagid}.nickname=Retrieve Schedule Tag by ID
v1.0.schedule-tag.get.{tagid}.summary=Returns a Schedule tag.
v1.0.schedule-tag.get.{tagid}.notes=This operation returns a Schedule tag by ID.
v1.0.schedule-tag.get.{tagid}.response.200.message=Successful retrieval of Schedule tag.
v1.0.schedule-tag.get.{tagid}.response.404.message=Schedule tag not found.
v1.0.schedule-tag.get.{tagid}.headerparam.tokenrestutil.token_header_name.value=The ID of the current business interaction.
v1.0.schedule-tag.get.{tagid}.pathparam.tagid.value=The ID of the Schedule tag.
v1.0.schedule-tag.post.multi_read.nickname=Retrieve Schedule Tags by ID
v1.0.schedule-tag.post.multi_read.notes=This operation returns one or more Schedule tags by ID.
v1.0.schedule-tag.post.multi_read.summary=Returns one or more Schedule tags.
v1.0.schedule-tag.post.multi_read.response.200.message=Successful retrieval of Schedule tags.
v1.0.schedule-tag.post.multi_read.response.400.message=Schedule tags retrieval unsuccessful.
v1.0.schedule-tag.post.multi_read.response.404.message=Schedule tags not found.
v1.0.schedule-tag.post.multi_read.payload.request.value=Set of parameters used to retrieve Schedule tags.
v1.0.schedule-tag.post.multi_read.headerparam.tokenrestutil.token_header_name.value=The ID of the current business interaction.
v1.0.schedule-tag.post.{tagid}.nickname=Update Schedule Tag by ID
v1.0.schedule-tag.post.{tagid}.summary=Updates a Schedule tag.
v1.0.schedule-tag.post.{tagid}.notes=This operation updates a Schedule tag by ID.
v1.0.schedule-tag.post.{tagid}.response.200.message=Successful update of a Schedule tag.
v1.0.schedule-tag.post.{tagid}.response.404.message=Schedule tag not found.
v1.0.schedule-tag.post.{tagid}.headerparam.tokenrestutil.token_header_name.value=The ID of the current business interaction.
v1.0.schedule-tag.post.{tagid}.pathparam.tagid.value=The ID of the Schedule tag.
v1.0.schedule-tag.post.{tagid}.payload.tag.value=The Schedule tag to update.
v1.0.schedule-tag.delete.{tagid}.nickname=Delete Schedule Tag by ID
v1.0.schedule-tag.delete.{tagid}.summary=Deletes a Schedule tag.
v1.0.schedule-tag.delete.{tagid}.notes=This operation deletes a Schedule tag by ID.
v1.0.schedule-tag.delete.{tagid}.response.200.message=Successful deletion of a Schedule tag.
v1.0.schedule-tag.delete.{tagid}.response.404.message=Schedule tag not found.
v1.0.schedule-tag.delete.{tagid}.headerparam.tokenrestutil.token_header_name.value=The ID of the current business interaction.
v1.0.schedule-tag.delete.{tagid}.pathparam.tagid.value=The ID of the Schedule tag.
v1.0.schedule-tag.post.multi_create.nickname=Create Schedule Tags
v1.0.schedule-tag.post.multi_create.summary=Creates one or more Schedule tags.
v1.0.schedule-tag.post.multi_create.notes=This operation creates one or more Schedule tags according to the IDs provided.
v1.0.schedule-tag.post.multi_create.headerparam.tokenrestutil.token_header_name.value=The ID of the current business interaction.
v1.0.schedule-tag.post.multi_create.response.200.message=Successful creation of Schedule tags.
v1.0.schedule-tag.post.multi_create.response.400.message=Schedule tags creation unsuccessful.
v1.0.schedule-tag.post.multi_create.payload.tags.value=The Schedule tags to create.
v1.0.schedule-tag.post.multi_update.nickname=Update Schedule Tags
v1.0.schedule-tag.post.multi_update.summary=Updates one or more Schedule tags.
v1.0.schedule-tag.post.multi_update.notes=This operation updates one or more Schedule tags according to the IDs provided.
v1.0.schedule-tag.post.multi_update.response.200.message=Successful update of Schedule tags.
v1.0.schedule-tag.post.multi_update.response.400.message=Schedule tags updated unsuccessful.
v1.0.schedule-tag.post.multi_update.headerparam.tokenrestutil.token_header_name.value=The ID of the current business interaction.
v1.0.schedule-tag.post.multi_update.payload.tags.value=The Schedule tags to update.
v1.0.schedule-tag.post.multi_delete.nickname=Delete Schedule Tags
v1.0.schedule-tag.post.multi_delete.summary=Deletes one or more Schedule tags.
v1.0.schedule-tag.post.multi_delete.notes=This operation deletes one or more Schedule tags according to the specified parameters.
v1.0.schedule-tag.post.multi_delete.response.200.message=Successful deleted Schedule tags.
v1.0.schedule-tag.post.multi_delete.response.400.message=Schedule tags could not be deleted.
v1.0.schedule-tag.post.multi_delete.headerparam.tokenrestutil.token_header_name.value=The ID of the current business interaction.
v1.0.schedule-tag.post.multi_delete.payload.request.value=Set of parameters used to delete Schedule tags.
