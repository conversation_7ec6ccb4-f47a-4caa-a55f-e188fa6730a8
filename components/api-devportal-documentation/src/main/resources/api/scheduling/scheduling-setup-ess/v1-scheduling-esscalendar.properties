#Comments for Properties
v1.0.ess-calendar-settings.name=ESS Calendar Settings
v1.0.ess-calendar-settings.parent=root.wfss.default
v1.0.ess-calendar-settings.description=This resource allows a manager to create, retrieve, update, and delete Employee Self Service (ESS) Calendar Settings.

v1.0.ess-calendar-settings.get.{id}.nickname=Retrieve ESS Calendar Settings by ID
v1.0.ess-calendar-settings.get.{id}.summary=Returns ESS Calendar Settings.
v1.0.ess-calendar-settings.get.{id}.notes=This operation returns Employee Self Service (ESS) Calendar Settings by ID.
v1.0.ess-calendar-settings.get.{id}.response.200.message=Successful retrieval of ESS Calendar Settings.
v1.0.ess-calendar-settings.get.{id}.response.404.message=ESS Calendar Settings not found.
v1.0.ess-calendar-settings.get.{id}.pathparam.id.value=The ID of the ESS Calendar Settings.

v1.0.ess-calendar-settings.get.currentpath.nickname=Retrieve ESS Calendar Settings by Name
v1.0.ess-calendar-settings.get.currentpath.summary=Returns an ESS Calendar Settings.
v1.0.ess-calendar-settings.get.currentpath.notes=This operation returns a specific Employee Self Service (ESS) Calendar Settings by name.
v1.0.ess-calendar-settings.get.currentpath.response.200.message=Successful retrieval of ESS Calendar Settings.
v1.0.ess-calendar-settings.get.currentpath.response.404.message=ESS Calendar Settings not found.
v1.0.ess-calendar-settings.get.pathparam.name.value=The name of the ESS Calendar Settings.

v1.0.ess-calendar-settings.post.multi_read.nickname=Retrieve Multiple ESS Calendar Settings
v1.0.ess-calendar-settings.post.multi_read.summary=Returns one or more ESS Calendar Settings.
v1.0.ess-calendar-settings.post.multi_read.notes=This operation returns specific Employee Self Service (ESS) Calendar Settings by object references or returns all ESS Calendar Settings if no references are provided.
v1.0.ess-calendar-settings.post.multi_read.response.200.message=Successful retrieval of ESS Calendar Settings.
v1.0.ess-calendar-settings.post.multi_read.response.404.message=ESS Calendar Settings retrieval unsuccessful.
v1.0.ess-calendar-settings.post.multi_read.payload.request.value=Set of ESS Calendar Settings object references.

v1.0.ess-calendar-settings.post.currentpath.nickname=Create ESS Calendar Settings
v1.0.ess-calendar-settings.post.currentpath.summary=Creates an ESS Calendar Settings.
v1.0.ess-calendar-settings.post.currentpath.notes=This operation creates an Employee Self Service (ESS) Calendar Settings.
v1.0.ess-calendar-settings.post.currentpath.response.200.message=Successful creation of ESS Calendar Settings.
v1.0.ess-calendar-settings.post.currentpath.response.400.message=ESS Calendar Settings not created.
v1.0.ess-calendar-settings.post.payload.request.value=The ESS Calendar Settings to create.

v1.0.ess-calendar-settings.post.multi_create.nickname=Create Multiple ESS Calendar Settings
v1.0.ess-calendar-settings.post.multi_create.summary=Create a set of new ESS Calendar Settings.
v1.0.ess-calendar-settings.post.multi_create.notes=This operation creates a set of new Employee Self Service (ESS) Calendar Settings.
v1.0.ess-calendar-settings.post.multi_create.response.200.message=Successful creation of ESS Calendar Settings.
v1.0.ess-calendar-settings.post.multi_create.response.400.message=ESS Calendar Settings creation unsuccessful.
v1.0.ess-calendar-settings.post.multi_create.payload.request.value=A collection of ESS Calendar Settings to create.

v1.0.ess-calendar-settings.put.{id}.nickname=Update ESS Calendar Settings
v1.0.ess-calendar-settings.put.{id}.summary=Updates an ESS Calendar Settings.
v1.0.ess-calendar-settings.put.{id}.notes=This operation updates an Employee Self Service (ESS) Calendar Settings by ID.
v1.0.ess-calendar-settings.put.{id}.response.200.message=Successful update of an ESS Calendar Settings.
v1.0.ess-calendar-settings.put.{id}.response.404.message=ESS Calendar Settings not found.
v1.0.ess-calendar-settings.put.{id}.pathparam.id.value=The ID of the ESS Calendar Settings.
v1.0.ess-calendar-settings.put.{id}.payload.request.value=The ESS Calendar Settings to update.

v1.0.ess-calendar-settings.put.multi_update.nickname=Update Multiple ESS Calendar Settings
v1.0.ess-calendar-settings.put.multi_update.summary=Updates one or more ESS Calendar Settings.
v1.0.ess-calendar-settings.put.multi_update.notes=This operation updates one or more Employee Self Service (ESS) Calendar Settings.
v1.0.ess-calendar-settings.put.multi_update.response.200.message=Successful update of ESS Calendar Settings.
v1.0.ess-calendar-settings.put.multi_update.response.400.message=ESS Calendar Settings update was unsuccessful.
v1.0.ess-calendar-settings.put.multi_update.payload.request.value=The set of ESS Calendar Settings to update.

v1.0.ess-calendar-settings.delete.{id}.nickname=Delete ESS Calendar Settings by ID
v1.0.ess-calendar-settings.delete.{id}.summary=Deletes an ESS Calendar Settings.
v1.0.ess-calendar-settings.delete.{id}.notes=This operation deletes an Employee Self Service (ESS) Calendar Settings by ID.
v1.0.ess-calendar-settings.delete.{id}.response.200.message=Successful deletion of an ESS Calendar Settings.
v1.0.ess-calendar-settings.delete.{id}.response.404.message=ESS Calendar Settings not found.
v1.0.ess-calendar-settings.delete.{id}.pathparam.id.value=The ID of the ESS Calendar Settings.

v1.0.ess-calendar-settings.post.multi_delete.nickname=Delete Multiple ESS Calendar Settings
v1.0.ess-calendar-settings.post.multi_delete.summary=Deletes one or more ESS Calendar Settings.
v1.0.ess-calendar-settings.post.multi_delete.notes=This operation deletes one or more ESS Calendar Settings according to the parameters provided.
v1.0.ess-calendar-settings.post.multi_delete.response.200.message=Successful deleted ESS Calendar Settings.
v1.0.ess-calendar-settings.post.multi_delete.response.400.message=ESS Calendar Settings could not be deleted.
v1.0.ess-calendar-settings.post.multi_delete.payload.request.value=Set of parameters used to delete ESS Calendar Settings.

v1.0.ess-calendar-settings.get.transfer_types.nickname=Retrieve Transfer Types for ESS Calendar Settings
v1.0.ess-calendar-settings.get.transfer_types.summary=Returns all ESS Calendar Settings transfer types.
v1.0.ess-calendar-settings.get.transfer_types.notes=<p>This operation returns all ESS Calendar Settings transfer types.</p><br /><p>The associated Access Control Point is as follows:</p><ul><li><strong>ACP Key:</strong> CALENDAR_VIEWS_SETUP</li><li><strong>API Access Controlled By:</strong> Allowed</li><li><strong>Default Value:</strong> Disallowed</li><li><strong>Location in the UI:</strong> Manager - Common Setup > Calendar Views Setup</li></ul>
v1.0.ess-calendar-settings.get.transfer_types.response.200.message=Successful retrieval of ESS Calendar Settings transfer types.

v1.0.esscalendar.visiblewidget.apimodel.description=The visible widget object model.
v1.0.esscalendar.visiblewidget.widget.apimodelproperty.name=The name of a visible widget.
v1.0.esscalendar.visiblewidget.widget.apimodelproperty.id=The ID of a visible widget.
v1.0.esscalendar.visiblewidget.widget.apimodelproperty.label=The label of a visible widget.
v1.0.esscalendar.visiblewidget.widget.apimodelproperty.order=The order of a visible widget.

v1.0.ess-calendar-settings.get.schedule_insights_widget_entities.nickname=Retrieve All Schedule Insights Widget Entities
v1.0.ess-calendar-settings.get.schedule_insights_widget_entities.notes=This operation returns a list of all Schedule Insights Widget Entities.\n\nThe associated Access Control Point is REST_API_SETUP.
v1.0.ess-calendar-settings.get.schedule_insights_widget_entities.summary=Returns a list of all Schedule Insights Widget Entities.
v1.0.ess-calendar-settings.get.schedule_insights_widget_entities.response.200.message=Successfully retrieved all Schedule Insights Widget Entities.
v1.0.ess-calendar-settings.get.schedule_insights_widget_entities.response.403.message=[WFS-105037]-You do not have the correct permissions to perform that operation.