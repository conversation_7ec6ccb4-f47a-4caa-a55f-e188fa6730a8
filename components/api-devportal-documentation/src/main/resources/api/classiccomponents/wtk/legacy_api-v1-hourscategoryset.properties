#Comments for Properties
#Tue Dec 20 10:52:35 IST 2016
v1.0.legacy_api-hourscategoryset.name=iresthourscategoryset
v1.0.legacy_api-hourscategoryset.group=group.wtk
v1.0.legacy_api-hourscategoryset.parent=root.wtk.default
v1.0.legacy_api-hourscategoryset.description=Hours category sets define a list of hours categories that will be associated to a metrics setting.
v1.0.legacy_api-hourscategoryset.post.add.nickname=Not Available
v1.0.legacy_api-hourscategoryset.post.add.summary=Adds a hours category set.  If the hours category set already exists, it will be updated, otherwise it will be added.
v1.0.legacy_api-hourscategoryset.post.add.notes=Not Available
v1.0.legacy_api-hourscategoryset.put.update-{name}.nickname=Not Available
v1.0.legacy_api-hourscategoryset.put.update-{name}.summary=Updates an existing hours category set.  If the hours category set does not exist, it will be added.
v1.0.legacy_api-hourscategoryset.put.update-{name}.notes=Not Available
v1.0.legacy_api-hourscategoryset.put.update-{name}.pathparam.name.value=Not Available
v1.0.legacy_api-hourscategoryset.put.update-{name}.queryparam.description.value=Not Available
v1.0.legacy_api-hourscategoryset.delete.delete-{name}.nickname=Not Available
v1.0.legacy_api-hourscategoryset.delete.delete-{name}.summary=Deletes the hours category set for the specified name.  If the hours category set does not exist, no error will occur.
v1.0.legacy_api-hourscategoryset.delete.delete-{name}.notes=Not Available
v1.0.legacy_api-hourscategoryset.delete.delete-{name}.pathparam.name.value=Not Available
v1.0.legacy_api-hourscategoryset.put.updateonly-{name}.nickname=Not Available
v1.0.legacy_api-hourscategoryset.put.updateonly-{name}.summary=Updates an existing hours category set.  An error will be thrown of the hours category set does not exist.
v1.0.legacy_api-hourscategoryset.put.updateonly-{name}.notes=Not Available
v1.0.legacy_api-hourscategoryset.put.updateonly-{name}.pathparam.name.value=Not Available
v1.0.legacy_api-hourscategoryset.put.updateonly-{name}.queryparam.description.value=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieve-{name}.nickname=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieve-{name}.summary=Returns the hours category set for the specified name.
v1.0.legacy_api-hourscategoryset.get.retrieve-{name}.notes=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieve-{name}.pathparam.name.value=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieveall.nickname=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieveall.summary=Returns all the hours category sets.
v1.0.legacy_api-hourscategoryset.get.retrieveall.notes=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieveforupdate-{name}.nickname=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieveforupdate-{name}.summary=Returns the hours category set for the specified name.
v1.0.legacy_api-hourscategoryset.get.retrieveforupdate-{name}.notes=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieveforupdate-{name}.pathparam.name.value=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieveallforupdate.nickname=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieveallforupdate.summary=Returns all the hours category sets.
v1.0.legacy_api-hourscategoryset.get.retrieveallforupdate.notes=Not Available
v1.0.legacy_api-hourscategoryset.post.addonly.nickname=Not Available
v1.0.legacy_api-hourscategoryset.post.addonly.summary=Adds a hours category set.  An error will be thrown if the hours category set already exists.
v1.0.legacy_api-hourscategoryset.post.addonly.notes=Not Available
v1.0.legacy_api-hourscategoryset.delete.deleteonly-{name}.nickname=Not Available
v1.0.legacy_api-hourscategoryset.delete.deleteonly-{name}.summary=Deletes the hours category set for the specified name.  If the hours category set does not exist, an error will be thrown.
v1.0.legacy_api-hourscategoryset.delete.deleteonly-{name}.notes=Not Available
v1.0.legacy_api-hourscategoryset.delete.deleteonly-{name}.pathparam.name.value=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieveallnames.nickname=Not Available
v1.0.legacy_api-hourscategoryset.get.retrieveallnames.summary=Returns all the hours category set names.
v1.0.legacy_api-hourscategoryset.get.retrieveallnames.notes=Not Available
