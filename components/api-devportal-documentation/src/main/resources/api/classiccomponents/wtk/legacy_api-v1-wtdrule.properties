#Comments for Properties
#Tue Dec 20 10:52:34 IST 2016
v1.0.legacy_api-wtdrule.name=irestwtdrule
v1.0.legacy_api-wtdrule.group=group.wtk
v1.0.legacy_api-wtdrule.parent=root.wtk.default
v1.0.legacy_api-wtdrule.description=This tag lets you retrieve, add, delete, or modify Working Time Directive rules. A Working Time Directive rule specifies information such as maximum working hours and minimum amounts of time off for certain workers subject to Working Time Directive limits (for example, young workers or hazardous duty workers). You should assign Working Time Directive rules only to those employees who have working time limits. The pay codes to which Working Time Directive rules are defined by the WTDGlobal tag. The rules that you define in this WTDRule tag are assigned to individual employees by the WTDEmployeeAssignment tag.
v1.0.legacy_api-wtdrule.post.add.nickname=Not Available
v1.0.legacy_api-wtdrule.post.add.summary=Adds a new WTDRule. If the object already exists a new one is created.
v1.0.legacy_api-wtdrule.post.add.notes=Not Available
v1.0.legacy_api-wtdrule.put.update-{ruleid}.nickname=Not Available
v1.0.legacy_api-wtdrule.put.update-{ruleid}.summary=Updates a rule with new properties. If the rule does not exist one is created.
v1.0.legacy_api-wtdrule.put.update-{ruleid}.notes=Not Available
v1.0.legacy_api-wtdrule.put.update-{ruleid}.pathparam.ruleid.value=Not Available
v1.0.legacy_api-wtdrule.delete.delete-{ruleid}.nickname=Not Available
v1.0.legacy_api-wtdrule.delete.delete-{ruleid}.summary=Deletes the rule if exists. If it does not exist nothing happens.
v1.0.legacy_api-wtdrule.delete.delete-{ruleid}.notes=Not Available
v1.0.legacy_api-wtdrule.delete.delete-{ruleid}.pathparam.ruleid.value=Not Available
v1.0.legacy_api-wtdrule.put.updateonly-{ruleid}.nickname=Not Available
v1.0.legacy_api-wtdrule.put.updateonly-{ruleid}.summary=Updates a rule. If the rule does not exist then an object key not found exception is thrown.
v1.0.legacy_api-wtdrule.put.updateonly-{ruleid}.notes=Not Available
v1.0.legacy_api-wtdrule.put.updateonly-{ruleid}.pathparam.ruleid.value=Not Available
v1.0.legacy_api-wtdrule.get.retrieve-{ruleid}.nickname=Not Available
v1.0.legacy_api-wtdrule.get.retrieve-{ruleid}.summary=Returns the Working Time Directive rule for the specified RuleId. If the rule does not exist and an Object key not found, an exception is thrown.
v1.0.legacy_api-wtdrule.get.retrieve-{ruleid}.notes=Not Available
v1.0.legacy_api-wtdrule.get.retrieve-{ruleid}.pathparam.ruleid.value=Not Available
v1.0.legacy_api-wtdrule.get.retrieveall.nickname=Not Available
v1.0.legacy_api-wtdrule.get.retrieveall.summary=Retrieves all Working Time Directive rules.
v1.0.legacy_api-wtdrule.get.retrieveall.notes=Not Available
v1.0.legacy_api-wtdrule.get.retrieveforupdate-{name}.nickname=Not Available
v1.0.legacy_api-wtdrule.get.retrieveforupdate-{name}.summary=Returns a Working Time Directive rule for a name provided for SDM update. If the rule does not exist and an Object key is not found, an exception is thrown.
v1.0.legacy_api-wtdrule.get.retrieveforupdate-{name}.notes=Not Available
v1.0.legacy_api-wtdrule.get.retrieveforupdate-{name}.pathparam.name.value=Not Available
v1.0.legacy_api-wtdrule.get.retrieveallforupdate.nickname=Not Available
v1.0.legacy_api-wtdrule.get.retrieveallforupdate.summary=Returns all rules for SDM update.
v1.0.legacy_api-wtdrule.get.retrieveallforupdate.notes=Not Available
v1.0.legacy_api-wtdrule.post.addonly.nickname=Not Available
v1.0.legacy_api-wtdrule.post.addonly.summary=Adds a new WTDRule. If the object exists already a duplicate exception is thrown.
v1.0.legacy_api-wtdrule.post.addonly.notes=Not Available
v1.0.legacy_api-wtdrule.delete.deleteonly-{ruleid}.nickname=Not Available
v1.0.legacy_api-wtdrule.delete.deleteonly-{ruleid}.summary=If the rule exists it will be deleted if it does not then an object key not found exception is thrown.
v1.0.legacy_api-wtdrule.delete.deleteonly-{ruleid}.notes=Not Available
v1.0.legacy_api-wtdrule.delete.deleteonly-{ruleid}.pathparam.ruleid.value=Not Available
v1.0.legacy_api-wtdrule.get.retrieveallnames.nickname=Not Available
v1.0.legacy_api-wtdrule.get.retrieveallnames.summary=Returns the names of all Working Time Directive rules.
v1.0.legacy_api-wtdrule.get.retrieveallnames.notes=Not Available
