v1.0.totalizing-processor_to_rule.parent=root.wtk.default
v1.0.totalizing-processor_to_rule.name=Associate Extension Processor to Rule.

v1.0.totalizing-processor_to_rule.post.notes=Creates the extension processor rule association.
v1.0.totalizing-processor_to_rule.post.nickname=This operation creates the extension processor rule association.
v1.0.totalizing-processor_to_rule.post.summary=Extension Processor Rule association Create.
v1.0.totalizing-processor_to_rule.post.response.200.message=Successfully created the new extension processor rule association.
v1.0.totalizing-processor_to_rule.post.response.400.message=Bad Request.
v1.0.totalizing-processor_to_rule.post.response.403.message=Access denied.
v1.0.totalizing-processor_to_rule.post.response.404.message=The extension processor is not found.

v1.0.totalizing-processor_to_rule.put.notes=Updates the existing extension processor rule association.
v1.0.totalizing-processor_to_rule.put.nickname=This operation updates one extension processor rule configuration association.
v1.0.totalizing-processor_to_rule.put.summary=Extension Processor Rule config association Update.
v1.0.totalizing-processor_to_rule.put.path.id=The primary identifier of extension processor rule config.
v1.0.totalizing-processor_to_rule.put.response.200.message=Successfully updated the extension processor rule config.
v1.0.totalizing-processor_to_rule.put.response.400.message=Bad Request.
v1.0.totalizing-processor_to_rule.put.response.403.message=Access denied.
v1.0.totalizing-processor_to_rule.put.response.404.message=The extension processor is not found.

v1.0.totalizing-processor_to_rule.delete.notes=Delete the existing extension processor rule association.
v1.0.totalizing-processor_to_rule.delete.nickname=This operation will delete the extension processor rule association.
v1.0.totalizing-processor_to_rule.delete.summary=Extension Processor Rule association Delete.
v1.0.totalizing-processor_to_rule.delete.path.id=The primary identifier of extension processor rule association.
v1.0.totalizing-processor_to_rule.delete.response.204.message=Successfully deleted the extension processor rule association.
v1.0.totalizing-processor_to_rule.delete.response.400.message=Bad Request.
v1.0.totalizing-processor_to_rule.delete.response.403.message=Access denied.

v1.0.totalizing-processor_to_rule.getall.notes=Returns a list of all extension processor rule associations.
v1.0.totalizing-processor_to_rule.getall.nickname=This operation will get all extension processor rule associations.
v1.0.totalizing-processor_to_rule.getall.summary=Extension Processor Rule association Get All.
v1.0.totalizing-processor_to_rule.getall.query.ruletype=Rule type to get
v1.0.totalizing-processor_to_rule.getall.response.200.message=Successfully retrieved a list of all extension processor rule association.
v1.0.totalizing-processor_to_rule.getall.response.403.message=Access denied.
v1.0.totalizing-processor_to_rule.getall.response.400.message=Only single tenants are supported.

v1.0.totalizing-processor_to_rule.get.{id}.notes=Returns extension processor rule association by id.
v1.0.totalizing-processor_to_rule.get.{id}.nickname=This operation will get by id the extension processor rule association.
v1.0.totalizing-processor_to_rule.get.{id}.summary=Extension Processor Rule association Get By Id.
v1.0.totalizing-processor_to_rule.get.{id}.path.id=The primary identifier of extension processor rule association.
v1.0.totalizing-processor_to_rule.get.{id}.response.200.message=Successfully returned extension processor.
v1.0.totalizing-processor_to_rule.get.{id}.response.403.message=Access denied.
v1.0.totalizing-processor_to_rule.get.{id}.response.400.message=Only single tenants are supported.
v1.0.totalizing-processor_to_rule.get.{id}.response.404.message=The extension processor is not found.
