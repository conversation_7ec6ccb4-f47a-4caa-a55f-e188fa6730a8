#Comments for Properties
#Tue Dec 20 13:04:59 IST 2016
v1.0.legacy_api-leaverule.name=irestleaverule
v1.0.legacy_api-leaverule.group=group.wfl
v1.0.legacy_api-leaverule.parent=root.wfl.default
v1.0.legacy_api-leaverule.description=The Leave Rule tag is used by the LeaveCascader to calculate leave takings.
v1.0.legacy_api-leaverule.put.update.nickname=Not Available
v1.0.legacy_api-leaverule.put.update.summary=Updates a leave rule. If the rule is not found, it is created. Values of "disregardAccrualRules", "userOverride" and "isActive" can be "true" or "false" only.
v1.0.legacy_api-leaverule.put.update.notes=Not Available
v1.0.legacy_api-leaverule.put.update.queryparam.name.value=Not Available
v1.0.legacy_api-leaverule.put.update.queryparam.isactive.value=Not Available
v1.0.legacy_api-leaverule.put.update.queryparam.category.value=Not Available
v1.0.legacy_api-leaverule.put.update.queryparam.rootnode.value=Not Available
v1.0.legacy_api-leaverule.put.update.queryparam.description.value=Not Available
v1.0.legacy_api-leaverule.put.update.queryparam.disregardaccrualrules.value=Not Available
v1.0.legacy_api-leaverule.put.update.queryparam.useroverride.value=Not Available
v1.0.legacy_api-leaverule.delete.delete-{name}.nickname=Not Available
v1.0.legacy_api-leaverule.delete.delete-{name}.summary=Deletes a leave rule.
v1.0.legacy_api-leaverule.delete.delete-{name}.notes=Not Available
v1.0.legacy_api-leaverule.delete.delete-{name}.pathparam.name.value=Not Available
v1.0.legacy_api-leaverule.get.retrieve-{name}.nickname=Not Available
v1.0.legacy_api-leaverule.get.retrieve-{name}.summary=Retrieves a leave rule.
v1.0.legacy_api-leaverule.get.retrieve-{name}.notes=Not Available
v1.0.legacy_api-leaverule.get.retrieve-{name}.pathparam.name.value=Not Available
v1.0.legacy_api-leaverule.get.retrieveall.nickname=Not Available
v1.0.legacy_api-leaverule.get.retrieveall.summary=Retrieves all leave rules.
v1.0.legacy_api-leaverule.get.retrieveall.notes=Not Available
v1.0.legacy_api-leaverule.get.retrieveforupdate-{name}.nickname=Not Available
v1.0.legacy_api-leaverule.get.retrieveforupdate-{name}.summary=Retrieves an existing leave rule, by name.
v1.0.legacy_api-leaverule.get.retrieveforupdate-{name}.notes=Not Available
v1.0.legacy_api-leaverule.get.retrieveforupdate-{name}.pathparam.name.value=Not Available
v1.0.legacy_api-leaverule.get.retrieveallnames.nickname=Not Available
v1.0.legacy_api-leaverule.get.retrieveallnames.summary=Retrieves the list names of all leave rules (active and inactive).
v1.0.legacy_api-leaverule.get.retrieveallnames.notes=Not Available
v1.0.legacy_api-leaverule.put.updateonly.nickname=Not Available
v1.0.legacy_api-leaverule.put.updateonly.summary=Updates an existing leave rule. Values of "disregardAccrualRules", "userOverride" and "isActive" can be "true" or "false" only.
v1.0.legacy_api-leaverule.put.updateonly.notes=Not Available
v1.0.legacy_api-leaverule.put.updateonly.queryparam.name.value=Not Available
v1.0.legacy_api-leaverule.put.updateonly.queryparam.isactive.value=Not Available
v1.0.legacy_api-leaverule.put.updateonly.queryparam.category.value=Not Available
v1.0.legacy_api-leaverule.put.updateonly.queryparam.rootnode.value=Not Available
v1.0.legacy_api-leaverule.put.updateonly.queryparam.description.value=Not Available
v1.0.legacy_api-leaverule.put.updateonly.queryparam.disregardaccrualrules.value=Not Available
v1.0.legacy_api-leaverule.put.updateonly.queryparam.useroverride.value=Not Available
v1.0.legacy_api-leaverule.post.addonly.nickname=Not Available
v1.0.legacy_api-leaverule.post.addonly.summary=Adds a leave rule.
v1.0.legacy_api-leaverule.post.addonly.notes=Not Available
