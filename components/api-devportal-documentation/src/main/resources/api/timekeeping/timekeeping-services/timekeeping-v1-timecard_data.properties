#Business - Timekeeping Timecard Data Service - v1
v1.0.timekeeping-timecard_data.name=Timecard Metrics
v1.0.timekeeping-timecard_data.parent=root.wtk4.default
v1.0.timekeeping-timecard_data.description=This resource allows you to access a range of metrics related to employee timecards, such as averages, exception counts, data related to full time and part time employees, projected, scheduled, actual, and corrected totals, accrual transactions and summaries, and total summaries broken down by projected, scheduled, and actual for shift totals and daily totals. 

v1.0.timekeeping-timecard_data.post.multi_read.nickname=Retrieve Timecard Data for Multiple Employees
v1.0.timekeeping-timecard_data.post.multi_read.summary=Returns timecard data matching the specified search criteria.
v1.0.timekeeping-timecard_data.post.multi_read.notes=This operation returns timecard data for a set of employees.\n\nThe data included in the response can be specified via the `select` property. Search criteria includes the following: FTPTDATA_ALL, FTPTDATA, AVERAGING, SCHEDULED_TOTALS, CONTRACT_TOTALS, PROJECTED_TOTALS, PROJECTED_TOTALS_ONLY_CORRECTIONS, PROJECTED_TOTALS_EXCLUDE_CORRECTIONS, ACTUAL_TOTALS, ACTUAL_TOTALS_ONLY_CORRECTIONS, ACTUAL_TOTALS_EXCLUDE_CORRECTIONS, EXCEPTION_TOTAL, EXCEPTION_TOTAL_UNREVIEWED, EXCEPTION_TOTAL_EMPLOYEE_JUSTIFIED, EXCEPTION_TOTAL_MANAGER_JUSTIFIED, EXCEPTION_TOTAL_AUTO_RESOLVED, SHIFT_ACTUAL_TOTAL_SUMMARY, SHIFT_SCHEDULED_TOTAL_SUMMARY, SHIFT_CONTRACT_TOTAL_SUMMARY, SHIFT_PROJECTED_TOTAL_SUMMARY, DAILY_ACTUAL_TOTAL_SUMMARY, DAILY_SCHEDULED_TOTAL_SUMMARY, DAILY_CONTRACT_TOTAL_SUMMARY, DAILY_PROJECTED_TOTAL_SUMMARY, ACCRUAL_SUMMARY, ACCRUAL_TRANSACTIONS, ABSENCE_EXCEPTION, ISR_DAILY, ISR_SUMMARY.\n\n* FTPTDATA_ALL -- For each employee and pay code, this full time and part time data contains the number of weeks from a specified date range, the total number of hours worked during those weeks, and the average weekly hours worked.\n\n* FTPTDATA -- For each employee and pay code, this full time and part time data contains the number of weeks from a specified date range, the total number of hours worked during those weeks (including partial weeks), the number of worked weeks (a worked week is defined as having worked hours greater than 0 during that week), and the average weekly hours worked.\n\n* AVERAGING -- The average totals for a specified timeframe based on pay code and other selected rollup types.\n\n* SCHEDULED_TOTALS -- The totals from scheduled items.\n\n* CONTRACT_TOTALS -- The totals from contract items.\n\n* PROJECTED_TOTALS -- The totals from projected items, including totals from historical corrections.\n\n* PROJECTED_TOTALS_ONLY_CORRECTIONS -- The totals from historical corrections based on a projection.\n\n* PROJECTED_TOTALS_EXCLUDE_CORRECTIONS -- The totals from a projection that exclude historical corrections.\n\n* ACTUAL_TOTALS -- The totals from actual worked items. <strong>Note:</strong> For security reasons, when selecting ACTUAL_TOTALS, your application should call this operation with the parameter partial_success set to true. In addition to providing better error handling, the parameter also correctly validates that the calling user has access to all the returned employee totals.\n\n* ACTUAL_TOTALS_ONLY_CORRECTIONS -- The totals from historical corrections for actual worked items.\n\n* ACTUAL_TOTALS_EXCLUDE_CORRECTIONS -- The totals from actual worked items that exclude historical corrections.\n\n* EXCEPTION_TOTAL -- The total number of exceptions.\n\n* EXCEPTION_TOTAL_UNREVIEWED -- The total number of unreviewed exceptions.\n\n* EXCEPTION_TOTAL_EMPLOYEE_JUSTIFIED -- The total number of employee justified exceptions.\n\n* EXCEPTION_TOTAL_MANAGER_JUSTIFIED -- The total number of manager justified exceptions.\n\n* EXCEPTION_TOTAL_AUTO_RESOLVED -- The total number of auto-resolved exceptions.\n\n* SHIFT_ACTUAL_TOTAL_SUMMARY -- The number of hours for actual worked items by shift.\n\n* SHIFT_SCHEDULED_TOTAL_SUMMARY -- The number of hours for scheduled work items by shift.\n\n* SHIFT_CONTRACT_TOTAL_SUMMARY -- The number of hours for contract work items by shift.\n\n* SHIFT_PROJECTED_TOTAL_SUMMARY -- The number of hours for projected work items by shift.\n\n* DAILY_ACTUAL_TOTAL_SUMMARY -- The number of hours for actual worked items by day. <strong>Note:</strong> When retrieving the DAILY_ACTUAL_TOTAL_SUMMARY, all standard pay codes for the period are returned and no filtering occurs.\n\n* DAILY_SCHEDULED_TOTAL_SUMMARY -- The number of hours for scheduled work items by day.\n\n* DAILY_CONTRACT_TOTAL_SUMMARY -- The number of hours for contract work items by day.\n\n* DAILY_PROJECTED_TOTAL_SUMMARY -- The number of hours for projected work items by day.\n\n* ACCRUAL_SUMMARY -- A daily summary of accruals and various balances for tracking periods.\n\n* ACCRUAL_TRANSACTIONS -- A list of accrual transactions within a specified timeframe.\n\n* ABSENCE_EXCEPTION -- A list of exceptions during a specified timeframe that are due to employee absence from scheduled shifts.\n\n* ISR_DAILY -- A Boolean indicator of whether or not to include summary report.\n\n* ISR_SUMMARY -- A Boolean indicator of whether or not to include daily report.\n\n## Best Practices\n\n1. __Explicitly define entities in the `select` property__: Always include specific entities in the `select` property. Avoid omitting this property or providing an empty list. Limit the `select` list to 10 entities or less. For example: `"select": ["EXCEPTION_TOTAL", "EXCEPTION_TOTAL_UNREVIEWED"]`\n2. __Optimize employee data retrieval__: Retrieve data for a maximum of 200 employees within a single pay period. Alternately, limit data retrieval to a maximum timeframe of 366 days.\n3. __Utilize filters for specific data__: Always apply filters when applicable to obtain more precise data. For instance, specify pay codes if only data related to certain pay codes is required.\n\n## Service limit\n\nA service limit constrains the number of employees multiplied by the number of days that can be processed in a single request.\n\n* The __Number of Employees__ multiplied by the __Number of Days__ cannot exceed 280,000.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.\n\n## Multiple Positions feature\n\nIf the Multiple Positions feature switch is enabled, the attribute count of the response is computed based on exceptions and assignments and the `position` property appears in the response body. If the feature switch is disabled, the attribute count of the response is computed based on exceptions. The `position` property only supports the following `select` parameters in the request payload:\n\n* EXCEPTION_TOTAL\n\n* EXCEPTION_TOTAL_UNREVIEWED\n\n* EXCEPTION_TOTAL_EMPLOYEE_JUSTIFIED\n\n* EXCEPTION_TOTAL_MANAGER_JUSTIFIED\n\n* EXCEPTION_TOTAL_AUTO_RESOLVED\n\n## Notes\n\n* The ability to return wages in the response is controlled by the WAGES Access Control Point.\n\n### Request example\n\nThe following request payload demonstrates a valid call when the Multiple Assignments feature is enabled:\n\n```\n{\n    "select": [\n        "EXCEPTION_TOTAL",\n        "EXCEPTION_TOTAL_UNREVIEWED",\n        "EXCEPTION_TOTAL_EMPLOYEE_JUSTIFIED",\n        "EXCEPTION_TOTAL_MANAGER_JUSTIFIED",\n        "EXCEPTION_TOTAL_AUTO_RESOLVED"\n    ],\n    "where": {\n        "employeeSet": {\n            "dateRange": {\n                "startDate":"2022-06-28",\n                "endDate":"2022-07-28"\n            },\n            "employees": {\n                "ids": [],\n                "qualifiers": [],\n                "refs": [\n                    {\n                        "id": 243\n                    },\n                    {\n                        "id": 244\n                    }\n                ]\n            }\n        },\n        "includeApprovingManagers": false\n    },\n    "rollupContext": {\n        "byEmployee": true,\n        "byPaycode": true,\n        "byDate": true,\n        "byDateRange": false,\n        "byOrg": false,\n        "byLaborCategory": false\n    }\n}\n\n```\n\n### Response example\n\nThe following response body demonstrates a success response when the Multiple Assignments feature is enabled:\n\n```\n[\n  {\n    "employeeId": {\n      "id": 244,\n      "qualifier": "20336",\n      "name": "20336"\n    },\n    "exceptioncounts": [\n      {\n        "uniqueId": "244:19:-244",\n        "employee": {\n          "id": 244,\n          "qualifier": "20336",\n          "name": "20336"\n        },\n        "exceptionType": {\n          "id": 19,\n          "name": "SHORT_TOTAL_BREAK",\n          "description": "SHORT_TOTAL_BREAK_DSC",\n          "displayName": "Short Total Break",\n          "category": "BREAK_EXCEPTIONS"\n        },\n        "count": 1,\n        "position": {\n          "id": -244,\n          "qualifier": "Assignment",\n          "name": "Assignment"\n        }\n      },\n      {\n        "uniqueId": "244:16:-244",\n        "employee": {\n          "id": 244,\n          "qualifier": "20336",\n          "name": "20336"\n        },\n        "exceptionType": {\n          "id": 16,\n          "name": "MISSED_OUT_PUNCH",\n          "description": "MISSED_OUT_PUNCH_DSC",\n          "displayName": "Missed Out Punch",\n          "category": "OUT_PUNCH_EXCEPTIONS"\n        },\n        "count": 1,\n        "position": {\n          "id": -244,\n          "qualifier": "Assignment",\n          "name": "Assignment"\n        }\n      },\n      {\n        "uniqueId": "244:10:-244",\n        "employee": {\n          "id": 244,\n          "qualifier": "20336",\n          "name": "20336"\n        },\n        "exceptionType": {\n          "id": 10,\n          "name": "UNEXCUSED_ABSENCE",\n          "description": "UNEXCUSED_ABSENCE_DSC",\n          "displayName": "Unexcused Absence",\n          "category": "ABSENCE_EXCEPTIONS"\n        },\n        "count": 1,\n        "position": {\n          "id": -244,\n          "qualifier": "Assignment",\n          "name": "Assignment"\n        }\n      }\n    ],\n    "exceptioncountsUnreviewed": [\n      {\n        "uniqueId": "244:19:-244",\n        "employee": {\n          "id": 244,\n          "qualifier": "20336",\n          "name": "20336"\n        },\n        "exceptionType": {\n          "id": 19,\n          "name": "SHORT_TOTAL_BREAK",\n          "description": "SHORT_TOTAL_BREAK_DSC",\n          "displayName": "Short Total Break",\n          "category": "BREAK_EXCEPTIONS"\n        },\n        "count": 1,\n        "position": {\n          "id": -244,\n          "qualifier": "Assignment",\n          "name": "Assignment"\n        }\n      },\n      {\n        "uniqueId": "244:16:-244",\n        "employee": {\n          "id": 244,\n          "qualifier": "20336",\n          "name": "20336"\n        },\n        "exceptionType": {\n          "id": 16,\n          "name": "MISSED_OUT_PUNCH",\n          "description": "MISSED_OUT_PUNCH_DSC",\n          "displayName": "Missed Out Punch",\n          "category": "OUT_PUNCH_EXCEPTIONS"\n        },\n        "count": 1,\n        "position": {\n          "id": -244,\n          "qualifier": "Assignment",\n          "name": "Assignment"\n        }\n      },\n      {\n        "uniqueId": "244:10:-244",\n        "employee": {\n          "id": 244,\n          "qualifier": "20336",\n          "name": "20336"\n        },\n        "exceptionType": {\n          "id": 10,\n          "name": "UNEXCUSED_ABSENCE",\n          "description": "UNEXCUSED_ABSENCE_DSC",\n          "displayName": "Unexcused Absence",\n          "category": "ABSENCE_EXCEPTIONS"\n        },\n        "count": 1,\n        "position": {\n          "id": -244,\n          "qualifier": "Assignment",\n          "name": "Assignment"\n        }\n      }\n    ]\n  },\n  {\n    "employeeId": {\n      "id": 243,\n      "qualifier": "20335",\n      "name": "20335"\n    },\n    "exceptioncounts": [\n      {\n        "uniqueId": "243:10:126",\n        "employee": {\n          "id": 243,\n          "qualifier": "20335",\n          "name": "20335"\n        },\n        "exceptionType": {\n          "id": 10,\n          "name": "UNEXCUSED_ABSENCE",\n          "description": "UNEXCUSED_ABSENCE_DSC",\n          "displayName": "Unexcused Absence",\n          "category": "ABSENCE_EXCEPTIONS"\n        },\n        "count": 1,\n        "position": {\n          "id": 126,\n          "qualifier": "Assignment1",\n          "name": "Assignment1"\n        }\n      },\n      {\n        "uniqueId": "243:10:-243",\n        "employee": {\n          "id": 243,\n          "qualifier": "20335",\n          "name": "20335"\n        },\n        "exceptionType": {\n          "id": 10,\n          "name": "UNEXCUSED_ABSENCE",\n          "description": "UNEXCUSED_ABSENCE_DSC",\n          "displayName": "Unexcused Absence",\n          "category": "ABSENCE_EXCEPTIONS"\n        },\n        "count": 1,\n        "position": {\n          "id": -243,\n          "qualifier": "Assignment",\n          "name": "Assignment"\n        }\n      }\n    ],\n    "exceptioncountsUnreviewed": [\n      {\n        "uniqueId": "243:10:126",\n        "employee": {\n          "id": 243,\n          "qualifier": "20335",\n          "name": "20335"\n        },\n        "exceptionType": {\n          "id": 10,\n          "name": "UNEXCUSED_ABSENCE",\n          "description": "UNEXCUSED_ABSENCE_DSC",\n          "displayName": "Unexcused Absence",\n          "category": "ABSENCE_EXCEPTIONS"\n        },\n        "count": 1,\n        "position": {\n          "id": 126,\n          "qualifier": "Assignment1",\n          "name": "Assignment1"\n        }\n      },\n      {\n        "uniqueId": "243:10:-243",\n        "employee": {\n          "id": 243,\n          "qualifier": "20335",\n          "name": "20335"\n        },\n        "exceptionType": {\n          "id": 10,\n          "name": "UNEXCUSED_ABSENCE",\n          "description": "UNEXCUSED_ABSENCE_DSC",\n          "displayName": "Unexcused Absence",\n          "category": "ABSENCE_EXCEPTIONS"\n        },\n        "count": 1,\n        "position": {\n          "id": -243,\n          "qualifier": "Assignment",\n          "name": "Assignment"\n        }\n      }\n    ]\n  }\n]\n\n```
v1.0.timekeeping-timecard_data.post.multi_read.response.200.message=Successfully retrieved timecard data for one or more employees.
v1.0.timekeeping-timecard_data.post.multi_read.response.207.message=Retrieved timecard data for one or more employee with partial success.
v1.0.timekeeping-timecard_data.post.multi_read.response.400.message=<p>Bad Request:</p> <ul> <li>[WTK-112000] - Missing id for object ref lookup. </li> <li>[WTK-112001] - Missing qualifier for object ref lookup. </li> <li>[WTK-112002] - Missing object ref for object ref lookup. </li> <li>[WTK-112003] - Missing {resolver} resolver. </li> <li>[WTK-112004] - ObjectRef with id: {id} could not be found. </li> <li>[WTK-112005] - ObjectRef with qualifier: {qualifier} could not be found. </li> <li>[WTK-113028] - Totals data cannot be retrieved. Enable the Timekeeping System Setting 'site.timekeeping.CompressedWorkWeek.enabled'. </li> <li>[WTK-113029] - The 'forWorkWeek' property only supports the '0', '1', or '2' symbolicPeriod id. </li> <li>[WTK-145051] - Service Limits are exceeded when you request the {SERVICE_NAME}  for {NUMBER_ENTRIES} employees and {NUMBER_DAYS} days. Submit the request again but with a number of employees that does not exceed the service limit {LIMIT}. </li> <li>[WTK-147500] - Timekeeping timecarddata service was accessed with a bad reference. </li> <li>At least one employee is required. </li> </ul>
v1.0.timekeeping-timecard_data.post.multi_read.request.value=The set of conditions used by a retrieve timecard data request for one or more employees.
v1.0.timekeeping-timecard_data.post.multi_read.partial_success.value=A Boolean indicator of whether or not processing continues if errors are encountered. Defaults to false.

# These annotations link to a private API.
v1.0.timekeeping.get.timecard_data.notes=This operation returns timecard data for one employee.
v1.0.timekeeping.get.timecard_data.nickname=Retrieve Timecard Data
v1.0.timekeeping.get.timecard_data.response.200.message=Successfully retrieved timecard data.
v1.0.timekeeping.get.timecard_data.response.400.message=Bad Request: <ul><li>At least one employee is required.</li></ul>
v1.0.timekeeping.get.timecard_data.summary=Returns timecard data.      
v1.0.timekeeping.get.timecard_data.queryparam.select.value=The set of conditions used by a retrieve timecard data request.

v1.0.timekeeping.get.timecard_data.queryparam.from.value=The start date in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.timekeeping.get.timecard_data.queryparam.to.value=The end date in ISO_LOCAL_DATE format (YYYY-MM-DD).
v1.0.timekeeping.get.timecard_data.queryparam.symbolic_period_ref.value=A symbolic identifier that represents a timeframe or a span of time.
v1.0.timekeeping.get.timecard_data.queryparam.employee_id.value=An ID that uniquely identifies an employee. This is not a person number.
v1.0.timekeeping.get.timecard_data.queryparam.person_number.value=A number that uniquely identifies a person. This is not an employee ID.
v1.0.timekeeping.get.timecard_data.queryparam.rollup_date_range.value=A Boolean indicator of whether or not to roll up the date range to only show the values as of the last day of the provided date range in the dailySummaries section of accrual balance data.
v1.0.timekeeping.get.timecard_data.queryparam.paycodes.value=A list of object references representing pay codes.
v1.0.timekeeping.get.timecard_data.queryparam.exceptionTypes.value=A list of object references representing exception types.