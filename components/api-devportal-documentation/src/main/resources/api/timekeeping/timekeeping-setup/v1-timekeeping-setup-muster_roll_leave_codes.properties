v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.name=Muster Roll Leave Codes
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.description=<p>The Muster Roll Leave Code resource allows you to manage one or more leave codes.</p>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.parent=root.wtk.default

# Get All
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.findall.nickname=Retrieve All Leave Codes
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.findall.summary=Returns all Leave Codes.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.findall.notes=This operation returns all Leave Codes.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.findall.response.200.message=Success.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.findall.response.204.message=No Content. No Leave Code found.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.findall.response.400.message=<ul><li>[WTK-180954] - Paycode not found for reference {reference}: {value}</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.findall.response.403.message=<ul><li>[WTK-180961] - The user does not have access to {fapName} with action {fapAction}.</li></ul>

# Get By Id
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.get.{id}.nickname=Retrieve Leave Code by ID
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.get.{id}.summary=Returns a Leave Code.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.get.{id}.notes=This operation returns a Leave Code by ID.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.get.{id}.response.200.message=Successfully retrieved.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.get.{id}.response.400.message=<ul><li>[WTK-180954] - Paycode not found for reference {reference}: {value}</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.get.{id}.response.403.message=<ul><li>[WTK-180961] - The user does not have access to {fapName} with action {fapAction}.</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.get.{id}.response.404.message=<ul><li>[WTK-180955] - The Muster Roll Leave Code entity was not found. ID: {id}.</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.get.{id}.param.id=The unique ID of a Leave Code.

# Create
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.post.{id}.nickname=Create Leave Code
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.post.{id}.summary=Creates a Leave Code.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.post.{id}.notes=This operation creates a Leave Code.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.post.{id}.response.200.message=Successfully created.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.post.{id}.response.400.message=<ul><li>[WTK-180952] - The following object reference is invalid: {payCode}.</li><li>[WTK-180953] - You must specify the following parameter: {paramName}.</li><li>[WTK-180954] - Paycode not found for reference {reference}: {value}</li><li>[WTK-180956] - The Leave Code must not exceed 6 characters.</li><li>[WTK-180957] - The Leave Code name {leaveCodeName} includes a character from the reserved set {forbiddenSymbols}</li><li>[WTK-180958] - The following parameter cannot be empty: {paramName}.</li><li>[WTK-180959] - The following parameter is duplicated: {paramName}. Value duplicated: {paramValue}.</li><li>[WTK-180960] - The description must not exceed 50 characters.</li><li>[WTK-180962] - A Leave Code with the paycode {payCode} configured already exists.</li><li>[WTK-180963] - Leave code names {forbiddenNames} are reserved and cannot be used.</li><li>[WTK-180965] - Minimum Hours per Full Day must be one minute greater than Maximum Hours per Half Day.</li><li>[WTK-180966] - {paramName} value must be less than Maximum value.</li><li>[WTK-180967] - Value must be null for non hour-based paycodes. Field Name: {paramName}.</li><li>[WTK-180968] - The field value is out of range. Field Name: {paramName}, Max Value: {maxValue}, Min Value: {minValue}.</li><li>[WTK-180969] - This field does not allow a null value. Field Name: {paramName}.</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.post.{id}.response.403.message=<ul><li>[WTK-180961] - The user does not have access to {fapName} with action {fapAction}.</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.post.{id}.response.404.message=<ul><li>[WTK-180955] - The Muster Roll Leave Code entity was not found. ID: {id}.</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.post.{id}.param.dto=The Create Leave Code request payload.

# Update
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.put.{id}.nickname=Update Leave Code by ID
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.put.{id}.summary=Updates a Leave Code.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.put.{id}.notes=This operation updates a Leave Code by ID.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.put.{id}.response.200.message=Successfully updated.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.put.{id}.response.400.message=<ul><li>[WTK-180951] - The ID from the path does not match ID from request payload.</li><li>[WTK-180952] - The following object reference is invalid: {payCode}.</li><li>[WTK-180953] - You must specify the following parameter: {paramName}.</li><li>[WTK-180954] - Paycode not found for reference {reference}: {value}</li><li>[WTK-180956] - The Leave Code must not exceed 6 characters.</li><li>[WTK-180957] - The Leave Code name {leaveCodeName} includes a character from the reserved set {forbiddenSymbols}</li><li>[WTK-180958] - The following parameter cannot be empty: {paramName}.</li><li>[WTK-180959] - The following parameter is duplicated: {paramName}. Value duplicated: {paramValue}.</li><li>[WTK-180960] - The description must not exceed 50 characters.</li><li>[WTK-180962] - A Leave Code with the paycode {payCode} configured already exists.</li><li>[WTK-180963] - Leave code names {forbiddenNames} are reserved and cannot be used.</li><li>[WTK-180965] - Minimum Hours per Full Day must be one minute greater than Maximum Hours per Half Day.</li><li>[WTK-180966] - {paramName} value must be less than Maximum value.</li><li>[WTK-180967] - Value must be null for non hour-based paycodes. Field Name: {paramName}.</li><li>[WTK-180968] - The field value is out of range. Field Name: {paramName}, Max Value: {maxValue}, Min Value: {minValue}.</li><li>[WTK-180969] - This field does not allow a null value. Field Name: {paramName}.</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.put.{id}.response.403.message=<ul><li>[WTK-180961] - The user does not have access to {fapName} with action {fapAction}.</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.put.{id}.response.404.message=<ul><li>[WTK-180955] - The Muster Roll Leave Code entity was not found. ID: {id}.</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.put.{id}.param.id=The unique ID of a Leave Code.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.put.{id}.param.dto=The Update Leave Code by ID request payload.

# Delete
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.delete.{id}.nickname=Delete Leave Code by ID
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.delete.{id}.summary=Deletes a Leave Code.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.delete.{id}.notes=This operation deletes a Leave Code by ID.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.delete.{id}.response.204.message=Successfully deleted.
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.delete.{id}.response.403.message=<ul><li>[WTK-180961] - The user does not have access to {fapName} with action {fapAction}.</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.delete.{id}.response.404.message=<ul><li>[WTK-180955] - The Muster Roll Leave Code entity was not found. ID: {id}.</li></ul>
v1.0.timekeeping-setup-timekeeping_muster_roll_leave_code.delete.{id}.param.id=The unique ID of a Leave Code.
