v1.0.timekeeping-setup-fixedrules.name=Timekeeping Fixed Rules
v1.0.timekeeping-setup-fixedrules.description=<p>This resource provides a set of operations that allow you to work with fixed rules.</p><p>Fixed rules identify constant pay policies that are assigned to employees, such as pay periods, day divides, and the <strong>Hours belong to</strong> option. Unlike work rule assignments, which can change with employee schedules, fixed rule assignments do not change. If there is no fixed rule assigned, the defaults in the fixed rules apply to the pay rule.</p>
v1.0.timekeeping-setup-fixedrules.parent=root.wtk2.default

#Summary and Notes are backwards in Java
v1.0.timekeeping-setup-fixedrules.post.currentpath.nickname=Create Fixed Rule
v1.0.timekeeping-setup-fixedrules.post.currentpath.notes=Creates and returns a fixed rule.
v1.0.timekeeping-setup-fixedrules.post.currentpath.summary=This operation creates and returns a fixed rule.
v1.0.timekeeping-setup-fixedrules.post.currentpath.response.200.message=Successfully: <ul><li>The fixed rule is created.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.currentpath.response.400.message=Bad Request: <ul><li>Input validation fails.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.currentpath.response.403.message=Bad Request: <ul><li>The user is not authorized to create fixed rule.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.currentpath.response.409.message=Bad Request: <ul><li>The fixed rule already exists.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.currentpath.response.500.message=Internal server error: <ul><li>Some unexpected server error occurred.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.currentpath.apiparam.basefixedruledto.value=The Create Fixed Rule request payload.

v1.0.timekeeping-setup-fixedrules.get.{id}.nickname=Retrieve Fixed Rule by ID
v1.0.timekeeping-setup-fixedrules.get.{id}.summary=Returns a fixed rule.
v1.0.timekeeping-setup-fixedrules.get.{id}.notes=This operation returns a fixed rule by ID.
v1.0.timekeeping-setup-fixedrules.get.{id}.pathparam.id.value=The ID of a fixed rule to retrieve.
v1.0.timekeeping-setup-fixedrules.get.{id}.response.200.message=Successfully: <ul><li>The fixed rule is retrieved.</li></ul>
v1.0.timekeeping-setup-fixedrules.get.{id}.response.403.message=Bad Request: <ul><li>The user is not authorized to retrieve fixed rule.</li></ul>
v1.0.timekeeping-setup-fixedrules.get.{id}.response.404.message=Bad Request: <ul><li>The fixed rule with requested id does not exist.</li></ul>

v1.0.timekeeping-setup-fixedrules.delete.{id}.nickname=Delete Fixed Rule by ID
v1.0.timekeeping-setup-fixedrules.delete.{id}.summary=Deletes a fixed rule.
v1.0.timekeeping-setup-fixedrules.delete.{id}.notes=This operation deletes a fixed rule by ID.
v1.0.timekeeping-setup-fixedrules.delete.{id}.pathparam.id.value=The ID of a fixed rule to delete.
v1.0.timekeeping-setup-fixedrules.delete.{id}.response.204.message=Successfully: <ul><li>The fixed rule is deleted.</li></ul>
v1.0.timekeeping-setup-fixedrules.delete.{id}.response.400.message=Bad Request: <ul><li>Unable to delete selected rule.</li></ul>
v1.0.timekeeping-setup-fixedrules.delete.{id}.response.403.message=Bad Request: <ul><li>The user is not authorized to delete fixed rule.</li></ul>
v1.0.timekeeping-setup-fixedrules.delete.{id}.response.404.message=Bad Request: <ul><li>The fixed rule with requested id does not exist.</li></ul>

v1.0.timekeeping-setup-fixedrules.post.multi_read.nickname=Retrieve Fixed Rules
v1.0.timekeeping-setup-fixedrules.post.multi_read.summary=Returns fixed rules by IDs, qualifiers, or object references.
v1.0.timekeeping-setup-fixedrules.post.multi_read.notes=This operation returns fixed rules by IDs, qualifiers, or object references.
v1.0.timekeeping-setup-fixedrules.post.multi_read.response.200.message=Success: <ul><li>The fixed rules were retrieved.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_read.response.207.message=Partial success: <ul><li>Partially retrieved a list of fixed rules. Some of the specified object references do not exist.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_read.response.400.message=Bad Request: <ul><li>Only one from ids/qualifiers/refs is allowed in the request.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_read.response.403.message=Bad Request: <ul><li>The user is not authorized to retrieve fixed rules.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_read.response.404.message=Bad Request: <ul><li>Fixed rules with the specified references do not exist.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_read.apiparam.multireadrequest.value=The Retrieve Fixed Rules request payload.

v1.0.timekeeping-setup-fixedrules.get.currentpath.nickname=Retrieve All Fixed Rules
v1.0.timekeeping-setup-fixedrules.get.currentpath.summary=Returns all fixed rules.
v1.0.timekeeping-setup-fixedrules.get.currentpath.notes=This operation returns all fixed rules.
v1.0.timekeeping-setup-fixedrules.get.currentpath.response.200.message=Success: <ul><li>The fixed rules were retrieved.</li></ul>
v1.0.timekeeping-setup-fixedrules.get.currentpath.response.403.message=Bad Request: <ul><li>The user is not authorized to retrieve fixed rules.</li></ul>

v1.0.timekeeping-setup-fixedrules.post.multi_create.nickname=Create Fixed Rules
v1.0.timekeeping-setup-fixedrules.post.multi_create.summary=Creates and returns fixed rules.
v1.0.timekeeping-setup-fixedrules.post.multi_create.notes=This operation creates and returns fixed rules.
v1.0.timekeeping-setup-fixedrules.post.multi_create.response.200.message=Successfully: <ul><li>The fixed rules are created.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_create.response.207.message=Successfully: <ul><li>Partially created a list of fixed rules.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_create.response.400.message=Bad Request: <ul><li>Only one from ids/qualifiers/refs is allowed in the request.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_create.response.403.message=Bad Request: <ul><li>The user is not authorized to create fixed rules.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_create.response.413.message=Bad Request: <ul><li>Number of fixed rules provided in request exceeds service limit.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_create.apiparam.basefixedrulelist.value=The Create Fixed Rules request payload.

v1.0.timekeeping-setup-fixedrules.put.id.nickname=Update Fixed Rule by ID
v1.0.timekeeping-setup-fixedrules.put.id.summary=Updates and returns a fixed rule.
v1.0.timekeeping-setup-fixedrules.post.id.summary=Updates and returns a fixed rule.
v1.0.timekeeping-setup-fixedrules.put.id.notes=This operation updates and returns a fixed rule by ID.
v1.0.timekeeping-setup-fixedrules.put.id.response.200.message=Success: <ul><li>The fixed rules were updated.</li></ul>
v1.0.timekeeping-setup-fixedrules.put.id.response.400.message=Bad Request: <ul><li>Input validation failed.</li></ul>
v1.0.timekeeping-setup-fixedrules.put.id.response.403.message=Bad Request: <ul><li>The user is not authorized to update fixed rule.</li></ul>
v1.0.timekeeping-setup-fixedrules.put.id.response.404.message=Bad Request: <ul><li>Fixed rules with the specified references do not exist.</li></ul>
v1.0.timekeeping-setup-fixedrules.put.id.apiparam.fixedruledto.value=The Update Fixed Rule request payload.
v1.0.timekeeping-setup-fixedrules.put.id.pathparam.id.value=The ID of a fixed rule.

v1.0.timekeeping-setup-fixedrules.post.multi_delete.nickname=Delete Fixed Rules
v1.0.timekeeping-setup-fixedrules.post.multi_delete.summary=Deletes fixed rules by IDs, qualifiers, or object references.
v1.0.timekeeping-setup-fixedrules.post.multi_delete.notes=This operation deletes fixed rules by IDs, qualifiers, or object references.
v1.0.timekeeping-setup-fixedrules.post.multi_delete.apiparam.multideleterequest.value=The Delete Fixed Rules request payload.
v1.0.timekeeping-setup-fixedrules.post.multi_delete.response.204.message=Success: <ul><li>The fixed rules are deleted.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_delete.response.207.message=Partial success: <ul><li>Partially deleted a list of fixed rules.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_delete.response.400.message=Bad Request: <ul><li>Only one from ids/qualifiers/refs is allowed in the request.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_delete.response.403.message=Bad Request: <ul><li>The user is not authorized to delete fixed rules.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_delete.response.404.message=Bad Request: <ul><li>Fixed rules with the specified references do not exist.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_delete.response.413.message=Bad Request: <ul><li>Number of fixed rules provided in request exceeds service limit.</li></ul>

v1.0.timekeeping-setup-fixedrules.post.multi_update.nickname=Update Fixed Rules
v1.0.timekeeping-setup-fixedrules.post.multi_update.summary=Updates and returns fixed rules.
v1.0.timekeeping-setup-fixedrules.post.multi_update.notes=This operation updates and returns fixed rules.
v1.0.timekeeping-setup-fixedrules.post.multi_update.apiparam.fixedrulelist.value=The Update Fixed Rules request payload.
v1.0.timekeeping-setup-fixedrules.post.multi_update.response.200.message=Success: <ul><li>The fixed rules are updated.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_update.response.207.message=Partial success: <ul><li>Partially updated a list of fixed rules.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_update.response.400.message=Bad Request: <ul><li>Input validation fails.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_update.response.403.message=Bad Request: <ul><li>The user is not authorized to delete fixed rules.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_update.response.404.message=Bad Request: <ul><li>Fixed rules with the specified references do not exist.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_update.response.413.message=Bad Request: <ul><li>Number of fixed rules provided in request exceeds service limit.</li></ul>

v1.0.timekeeping-setup-fixedrules.post.multi_upsert.nickname=Create or Update Fixed Rules
v1.0.timekeeping-setup-fixedrules.post.multi_upsert.summary=Creates or updates one or more fixed rules.
v1.0.timekeeping-setup-fixedrules.post.multi_upsert.notes=This operation creates or updates one or more fixed rules.
v1.0.timekeeping-setup-fixedrules.post.multi_upsert.apiparam.fixedrulelist.value=The Create or Update Fixed Rules request payload.
v1.0.timekeeping-setup-fixedrules.post.multi_upsert.response.200.message=Success: <ul><li>The fixed rules are updated created.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_upsert.response.207.message=Partial success: <ul><li>Partially processed a list of fixed rules.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_upsert.response.400.message=Bad Request: <ul><li>Input validation fails.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_upsert.response.403.message=Bad Request: <ul><li>The user is not authorized to create or update fixed rules.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_upsert.response.404.message=Bad Request: <ul><li>Fixed rules with the specified references do not exist.</li></ul>
v1.0.timekeeping-setup-fixedrules.post.multi_upsert.response.413.message=Bad Request: <ul><li>Number of fixed rules provided in request exceeds service limit.</li></ul>
