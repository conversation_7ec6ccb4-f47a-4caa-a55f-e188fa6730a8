#For task group API
@v2.0.forecasting-setup-template_task_groups.post.import_task_group.nickname=Import Task Group.
@v2.0.forecasting-setup-template_task_groups.post.import_task_group.notes=<p>This operation import a task group.</p><br /><p>The associated Access Control Point is WFF_TASK_GROUPS.</p>
@v2.0.forecasting-setup-template_task_groups.post.import_task_group.summary=Import a task group.
@v2.0.forecasting-setup-template_task_groups.post.import_task_group.response.200.message=Success.
@v2.0.forecasting-setup-template_task_groups.post.import_task_group.response.400.message=<ul><li>[WFF-270000] - The following collection cannot be empty: {listName}.</li> <ul><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li> <li>[WFF-270002] - The following parameter must be null: {paramName}.</li> <li>[WFF-270006] - The following object reference is invalid: {refName}.</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li> <li>[WFF-270012] - The following parameter cannot be empty: {paramName}.</li> <li>[WFF-270025] - Request body is missing. Specify request parameters.</li> <li>[WFF-270028] - Category path {categoryPathName} is not site type.</li> <li>[WFF-274001] - Maximum break duration should be value from 0 to 120, that is a multiple of 15 minutes.</li> <li>[WFF-274002] - Break placement window should be value from 0 to 360, that is a multiple of 15 minutes.</li> <li>[WFF-274003] - Cannot update type of the task group {name} as there are assigned tasks.</li> <li>[WFF-274004] - Cannot update generic department for existing Task Group: {name}.</li> <li>[WFF-180000] - The task group name exceeds maximum allowable length.</li> <li>[WFF-180001] - The task group description length exceeds maximum allowable length.</li> <li>[WFF-180002] - A task group with name {name} already exists.</li> <li>[WFF-180010] - The break placement window must be greater than or equal to maximum break duration.</li><li>[WFF-180048] - Cannot assign one or more store specific tasks to the generic task group.</li></ul>
@v2.0.forecasting-setup-template_task_groups.post.import_task_group.response.403.message==<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
@v2.0.forecasting-setup-template_task_groups.post.import_task_group.param.taskGroupTemplate=The import Task Group request payload.

#For org job assignments API
v2.0.forecasting-setup-template_task_groups.post.import_job_assignments.nickname=Import Task Group Org Job Assignments.
v2.0.forecasting-setup-template_task_groups.post.import_job_assignments.notes=<p>This operation assigns org jobs to a task group.</p><br /><p>The associated Access Control Point is WFF_TASK_GROUPS.</p>
v2.0.forecasting-setup-template_task_groups.post.import_job_assignments.summary=Import a org job in task group.
v2.0.forecasting-setup-template_task_groups.post.import_job_assignments.response.200.message=Success.
v2.0.forecasting-setup-template_task_groups.post.import_job_assignments.response.207.message=<ul><li>[WFF-270029] - Completed with error(s). Detailed errors should be wrapped within this exception.</li></ul>
v2.0.forecasting-setup-template_task_groups.post.import_job_assignments.response.400.message=<ul><li>[WFF-270000] - The following collection cannot be empty: {listName}.</li> <ul><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li> <li>[WFF-270002] - The following parameter must be null: {paramName}.</li> <li>[WFF-270006] - The following object reference is invalid: {refName}.</li> <li>[WFF-270008] - The following collection contains one or more duplicated entities: {collectionName}.</li> <li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li> <li>[WFF-270012] - The following parameter cannot be empty: {paramName}.</li> <li>[WFF-270025] - Request body is missing. Specify request parameters.</li> <li>[WFF-270028] - Category path {categoryPathName} is not site type.</li> <li>[WFF-274001] - Maximum break duration should be value from 0 to 120, that is a multiple of 15 minutes.</li><li>[WFF-180036] - The Task Group assignment failed for the following jobs: {jobs}. These jobs must be assigned to the department that references the generic department for the task group.</li><li>[WFF-180054] - Cannot assign selected Jobs. Jobs are already assigned to another Task Groups: {jobsWithTaskGroups}.</li> <li>[WFF-180057] - The provided jobs have different week start days.</li> <li>[WFF-180059] - The day of week of the effective date is not equal to start day of the week for selected jobs.</li></ul>
v2.0.forecasting-setup-template_task_groups.post.import_job_assignments.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v2.0.forecasting-setup-template_task_groups.post.import_job_assignments.response.413.message=<ul><li>[WFF-274006] - The specified number of jobs exceeds the maximum number of jobs that can be assigned to Task Groups. Actual: {actualNumberJobs}. Limit: {maxNumberJobsPerAllTaskGroups}.</li><li>[WFF-274009] - The specified number of jobs exceeds the maximum number of jobs that can be assigned to effective version of a Task Group. Limit: {maxNumberJobsPerEffectiveVersion}.</li></ul>
v2.0.forecasting-setup-template_task_groups.post.import_job_assignments.param.orgJobAssignmentsTemplate=The import org job assignments to Task Group request payload.

#For task assignments API
v2.0.forecasting-setup-template_task_groups.post.import_task_assignments.nickname=Assign tasks to the task groups.
v2.0.forecasting-setup-template_task_groups.post.import_task_assignments.notes=<p>This API operation facilitates the assignment of a task for a specified task group name.</p><br /><p>The associated Access Control Point is WFF_TASKS.</p>
v2.0.forecasting-setup-template_task_groups.post.import_task_assignments.summary=Assign the given tasks to respective task groups.
v2.0.forecasting-setup-template_task_groups.post.import_task_assignments.response.200.message=Success.
v2.0.forecasting-setup-template_task_groups.post.import_task_assignments.response.207.message=[WFF-270029] - Completed with error(s). Detailed errors should be wrapped within this exception.
v2.0.forecasting-setup-template_task_groups.post.import_task_assignments.response.400.message=<ul><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270006] - The following object reference is invalid: {paramName}.</li><li>[WFF-270009] - Entity or entities not found. Type: {type}. Name or names or ID or IDs: {ids/names}.</li><li>[WFF-270015] - The following reference list cannot be empty: {paramName}.</li><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-270029] - Completed with error(s). Detailed errors should be wrapped within this exception.</li><li>[WFF-270059] - Entity or entities not found in generic department: {genericDepartment}. Type: {type}. Name or names or id or ids: {id/qualifier}.</li><li>[WFF-274014]-Unrecognized property {propertyName} at {propertyValue} supplied for request.</li><li>[WFF-274015]-Task Group {propertyName} is not effective-dated for date {propertyValue}.</li><li>[WFF-274016]-A version of this task group already exists with this effective start date.</li><li>[WFF-274017]-The task ID is already associated with the provided effective task group ID {taskGroupId}.</li><li>[WFF-180047] - Cannot assign one or more tasks to a task group in a different store.</li><li>[WFF-180048] - Cannot assign one or more store specific tasks to the generic task group.</li></ul>
v2.0.forecasting-setup-template_task_groups.post.import_task_assignments.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v2.0.forecasting-setup-template_task_groups.post.import_task_assignments.response.404.message=<ul><li>[WFF-270032] - Entity not found. Type: Task Group. Name or ID: {qualifier}.</li></ul>
v2.0.forecasting-setup-template_task_groups.post.import_task_assignments.param.request=Allocate task to each task group based on their respective task group IDs
