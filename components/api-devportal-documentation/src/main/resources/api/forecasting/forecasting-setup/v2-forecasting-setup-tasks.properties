v2.0.forecasting-setup-tasks.post.create.nickname=Create Labor Task
v2.0.forecasting-setup-tasks.post.create.notes=<p>This operation creates a labor task.</p><br /><p>The associated Access Control Point is WFF_TASKS.</p>
v2.0.forecasting-setup-tasks.post.create.summary=Creates a labor task.
v2.0.forecasting-setup-tasks.post.create.response.200.message=Success.
v2.0.forecasting-setup-tasks.post.create.response.400.message=<ul><li>[WFF-175002] - A task with name {taskName} already exists.</li><li>[WFF-175007] - Time independent task {taskName} cannot be assigned labor standards with combined distribution. Please set the task to Time Dependent or remove combined distribution from its labor standard assignments.</li><li>[WFF-175008] - Combined distribution cannot be assigned to time independent task {taskName}.</li><li>[WFF-175024] - Cannot assign one or more standards to the task in a different store.</li><li>[WFF-175025] - Cannot assign one or more store specific standards to the generic task.</li><li>[WFF-175028] - Category path {DepartmentPathName} is not site type.</li><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270002] - "The following parameter must be null:{paramName}.</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-270059] - Entity or entities not found in generic department: {genericDepartmentName}. Type: {object}. Name(s) or ID(s):{qualifier/id}.</li><li>[WFF-293902] - Allocation of extra labor before traffic cannot be specified for time dependent task. Please set the task to Time Independent or remove the param allocateExtraLaborBeforeTraffic.</li><li>[WFF-270008] - The following collection contains one or more duplicated entities: {collectionName}.</li></ul>
v2.0.forecasting-setup-tasks.post.create.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v2.0.forecasting-setup-tasks.post.create.param.task=The Create Labor Task request payload.

v2.0.forecasting-setup-tasks.get.{id}.nickname=Retrieve Labor Task by ID
v2.0.forecasting-setup-tasks.get.{id}.notes=<p>This operation returns a labor task by ID.</p><br /><p>The associated Access Control Point is WFF_TASKS.</p>
v2.0.forecasting-setup-tasks.get.{id}.summary=Returns a labor task.
v2.0.forecasting-setup-tasks.get.{id}.response.200.message=Success.
v2.0.forecasting-setup-tasks.get.{id}.response.404.message=<ul><li>[WFF-270032] - Entity not found. Type: {object}. Name or ID: {qualifier/id}.</li></ul>
v2.0.forecasting-setup-tasks.get.{id}.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v2.0.forecasting-setup-tasks.get.{id}.pathparam.id.value=The unique ID of a labor task.

v2.0.forecasting-setup-tasks.get.nickname=Retrieve Labor Tasks by Name and Department
v2.0.forecasting-setup-tasks.get.notes=<p>This operation returns a labor task by name and generic department or returns all labor tasks associated with a generic department if only a generic department is passed.</p><br /><p>The associated Access Control Point is WFF_TASKS.</p>
v2.0.forecasting-setup-tasks.get.summary=Returns one or more labor tasks according to the specified parameters.
v2.0.forecasting-setup-tasks.get.response.200.message=Success.
v2.0.forecasting-setup-tasks.get.response.400.message=<ul><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names: {qualifier}.</li><li>[WFF-270016] - At least one of the following parameters is mandatory: [generic_department_id, generic_department_name].</li></ul>
v2.0.forecasting-setup-tasks.get.response.404.message=<ul><li>[WFF-270032] - Entity not found. Type: {object}. Name or ID: {qualifier/id}.</li></ul>
v2.0.forecasting-setup-tasks.get.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v2.0.forecasting-setup-tasks.get.queryparam.taskname.value=The name of a labor task.
v2.0.forecasting-setup-tasks.get.queryparam.genericdepartmentid.value=The ID of a generic department.
v2.0.forecasting-setup-tasks.get.queryparam.genericdepartmentname.value=The name of a generic department.

v2.0.forecasting-setup-tasks.put.update.nickname=Update Labor Task by ID
v2.0.forecasting-setup-tasks.put.update.notes=<p>This operation updates a labor task object by ID.</p><br /><p>The associated Access Control Point is WFF_TASKS.</p>
v2.0.forecasting-setup-tasks.put.update.summary=Updates a labor task.
v2.0.forecasting-setup-tasks.put.update.response.200.message=Success.
v2.0.forecasting-setup-tasks.put.update.response.400.message=<ul><li>[WFF-000012] - One or more reserved characters were found in the Task name.</li><li>[WFF-175000] - The task name exceeds maximum allowable length.</li><li>[WFF-175001] - The task description length exceeds maximum allowable length.</li><li>[WFF-175002] - A task with name {taskName} already exists.</li><li>[WFF-175008] - "Combined distribution cannot be assigned to time independent task {taskName}."</li><li>[WFF-175024] - Cannot assign one or more standards to the task in a different store.</li><li>[WFF-175025] - Cannot assign one or more store specific standards to the generic task.</li><li>[WFF-175026] - Time independent effective version of task {taskName} cannot be assigned labor standards with combined distribution. Please set effective version of the task to Time Dependent or remove combined distribution from its labor standard assignments."</li><li>[WFF-175027] - A version of this task already exists with this effective start date."</li><li>[WFF-175028] - Category path {DepartmentPathName} is not site type.</li><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270002] - "The following parameter must be null:{paramName}.</li><li>[WFF-270005] - "ID from path does not match ID from request payload.</li><li>[WFF-270008] - The following collection contains one or more duplicated entities: {collectionName}.</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-270028] - Category path {sitePath} is not site type.</li><li>[WFF-270059] - Entity or entities not found in generic department: {genericDepartmentName}. Type: {object}. Name(s) or ID(s):{qualifier/id}.</li><li>[WFF-293902] - Allocation of extra labor before traffic cannot be specified for time dependent task. Please set the task to Time Independent or remove the param allocateExtraLaborBeforeTraffic.</li><li>[WFF-273903] - You cannot change the site for a store-specific task.</li><li>[WFF-273904] - You cannot change a generic department.</li><li>[WFF-273905] - You cannot change the type of a task when it has assigned labor standards or it is assigned to task group(s). itself.</li></ul>
v2.0.forecasting-setup-tasks.put.update.response.403.message=[WFF-270022] - The user is not authorized to perform this action.
v2.0.forecasting-setup-tasks.put.update.response.404.message=<ul><li>[WFF-270032] - Entity not found. Type: {object}. Name or ID: {qualifier/id}.</li></ul>
v2.0.forecasting-setup-tasks.put.update.response.409.message=<ul><li>[WFF-175016] - Cannot update the task task to database. Some other user has already modified the task.</li></ul>
v2.0.forecasting-setup-tasks.put.update.{id}.pathparam.id.value=The unique ID of a labor task.
v2.0.forecasting-setup-tasks.put.update.param.task=The Update Labor Task by ID request payload.

v2.0.forecasting-setup-tasks.post.multi_update.nickname=Update Labor Tasks
v2.0.forecasting-setup-tasks.post.multi_update.notes=This operation updates one or more labor tasks.\n\nThe associated Access Control Point is WFF_TASKS.\n\n## Service limit\n\nA service limit constrains the number of labor standards that can be processed in a single request.\n\n* The __Number of Labor Standards__ for all tasks cannot exceed 3,000.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.
v2.0.forecasting-setup-tasks.post.multi_update.summary=Updates labor tasks.
v2.0.forecasting-setup-tasks.post.multi_update.response.200.message=Success.
v2.0.forecasting-setup-tasks.post.multi_update.response.207.message=[WFF-270029] - Completed with error(s). Detailed errors should be wrapped within this exception.
v2.0.forecasting-setup-tasks.post.multi_update.response.400.message=<ul><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li><li>[WFF-175002] - A task with name {taskName} already exists.</li><li>[WFF-175008] - Combined distribution cannot be assigned to time independent task {taskName}.</li><li>[WFF-273903] - You can not change site for store specific task.</li><li>[WFF-291000] - Category path {DepartmentPathName} is not site type.</li><li>[WFF-273904] - You can not change generic department.</li><li>[WFF-175008] - Combined distribution can not be assigned to time independent effective version of task {taskName}.</li><li>[WFF-273905] - You cannot change the type of a task when it has assigned labor standards or it is assigned to task group(s).</li><li>[WFF-175016] - Cannot update the task {taskName} to database. Some other user has already modified the task.</li><li>[WFF-175007] - Time independent task {taskname} cannot be assigned labor standards with combined distribution. Please set the task to Time Dependent or remove combined distribution from its labor standard assignments.</li><li>[WFF-270002] - The following parameter must be null:{paramName}.</li><li>[WFF-273902] - You cannot specify the allocation of extra labor before traffic for a time-dependent task. Set the task to time-independent or remove the allocateExtraLaborBeforeTraffic parameter.</li><li>[WFF-270008] - One or more effective versions conflicts with another effective version. StartDate for effective version should be unique.</li></ul>
v2.0.forecasting-setup-tasks.post.multi_update.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v2.0.forecasting-setup-tasks.post.multi_update.response.413.message=[WFF-273906] - The specified number of labor standards for all tasks exceeds the service limit. Actual: {actualNumberLaborStandardsPerAllTasks}. Limit: {maxNumberLaborStandardsPerAllTasks}.
v2.0.forecasting-setup-tasks.post.multi_update.param.tasks=The Update Labor Tasks request payload.

v2.0.forecasting-setup-tasks.delete.{id}.nickname=Delete Labor Task by ID
v2.0.forecasting-setup-tasks.delete.{id}.notes=<p>This operation deletes a labor task by ID.</p><br /><p>The associated Access Control Point is WFF_TASKS.</p>
v2.0.forecasting-setup-tasks.delete.{id}.summary=Deletes a labor task.
v2.0.forecasting-setup-tasks.delete.{id}.response.204.message=Success.
v2.0.forecasting-setup-tasks.delete.{id}.response.400.message=<ul><li>[WFF-273901] - You cannot delete a task while it has labor standards assigned to it.</li><li>[WFF-175017] - Cannot delete the task {taskName}. It is assigned to one or more task group(s). Please unassign it from all task groups before trying to delete it.</li></ul>
v2.0.forecasting-setup-tasks.delete.{id}.response.403.message=[WFF-270022] - The user is not authorized to perform this action.
v2.0.forecasting-setup-tasks.delete.{id}.response.404.message=<ul><li>[WFF-270032] - Entity not found. Type: {object}. Name or ID: {qualifier/id}.</li></ul>
v2.0.forecasting-setup-tasks.delete.{id}.pathparam.id.value=The unique ID of a labor task.

v2.0.forecasting-setup-tasks.post.multi_create.nickname=Create Labor Tasks
v2.0.forecasting-setup-tasks.post.multi_create.notes=This operation creates one or more labor tasks.\n\nThe associated Access Control Point is WFF_TASKS.\n\n## Service limit\n\nA service limit constrains the number of labor standards that can be processed in a single request.\n\n* The __Number of Labor Standards__ for all tasks cannot exceed 3,000.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.
v2.0.forecasting-setup-tasks.post.multi_create.summary=Creates labor tasks.
v2.0.forecasting-setup-tasks.post.multi_create.response.200.message=Success.
v2.0.forecasting-setup-tasks.post.multi_create.response.207.message=[WFF-270029] - Completed with error(s). Detailed errors should be wrapped within this exception.
v2.0.forecasting-setup-tasks.post.multi_create.response.400.message=<ul><li>[WFF-175002] - A task with name {taskName} already exists.</li><li>[WFF-175007] - Time independent task {taskName} cannot be assigned labor standards with combined distribution. Please set the task to Time Dependent or remove combined distribution from its labor standard assignments.</li><li>[WFF-175008] - Combined distribution cannot be assigned to time independent task {taskName}.</li><li>[WFF-175024] - Cannot assign one or more standards to the task in a different store.</li><li>[WFF-175025] - Cannot assign one or more store specific standards to the generic task.</li><li>[WFF-175028] - Category path {DepartmentPathName} is not site type.</li><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270002] - "The following parameter must be null:{paramName}.</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-270059] - Entity or entities not found in generic department: {genericDepartmentName}. Type: {object}. Name(s) or ID(s):{qualifier/id}.</li><li>[WFF-293902] - Allocation of extra labor before traffic first cannot be specified for time dependent task. Please set the task to Time Independent or remove the param allocateExtraLaborBeforeTrafficFirst.</li><li>[WFF-270000] - The following collection cannot be empty: {listName}</li><li>[WFF-270008] - The following collection contains one or more duplicated entities: {collectionName}.</li></ul>
v2.0.forecasting-setup-tasks.post.multi_create.response.403.message=[WFF-270022] - The user is not authorized to perform this action.
v2.0.forecasting-setup-tasks.post.multi_create.response.413.message=[WFF-273906] - The specified number of labor standards for all tasks exceeds the service limit. Actual: {actualNumberLaborStandardsPerAllTasks}. Limit: {maxNumberLaborStandardsPerAllTasks}.
v2.0.forecasting-setup-tasks.post.multi_create.param.tasks=The Create Labor Tasks request payload.

v2.0.forecasting-setup-tasks.post.multi_read.nickname=Retrieve Labor Tasks
v2.0.forecasting-setup-tasks.post.multi_read.notes=<p>This operation returns Labor Tasks according to the specified object references and generic department.</p><br /><p>The associated Access Control Point is WFF_TASKS.</p>
v2.0.forecasting-setup-tasks.post.multi_read.summary=Returns labor tasks by object references and generic department.
v2.0.forecasting-setup-tasks.post.multi_read.response.200.message=Success.
v2.0.forecasting-setup-tasks.post.multi_read.response.207.message=[WFF-270029] - Completed with error(s). Detailed errors should be wrapped within this exception.
v2.0.forecasting-setup-tasks.post.multi_read.response.400.message=<ul><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li><li>[WFF-270061] - Your request includes multiple, mutually exclusive parameters: {paramName}.</li><li>[WFF-270015] - The following reference list cannot be empty: {paramName}.</li><li>[WFF-270016] - At least one of the following parameters is mandatory: {paramNames}.</li><li>[WFF-270059] - Entity or entities not found in generic department: {genericDepartmentName}. Type: {object}. Name(s) or ID(s):{qualifier/id}.</li><li>[WFF-270025] - Request body is missing. Specify request parameters.</li></ul>
v2.0.forecasting-setup-tasks.post.multi_read.response.403.message=[WFF-270022] - The user is not authorized to perform this action.
v2.0.forecasting-setup-tasks.post.multi_read.response.413.message=[WFF-273908] - The specified Tasks have number of assigned labor standards that exceeds the limit. Actual: {actualNumber}. Limit: {maxNumber}.
v2.0.forecasting-setup-tasks.post.multi_read.param.taskmultioperationrequest=The Retrieve Labor Tasks request payload.

v2.0.forecasting-setup-tasks.post.multi_delete.nickname=Delete Labor Tasks
v2.0.forecasting-setup-tasks.post.multi_delete.notes=<p>This operation deletes labor tasks.</p><br /><p>The associated Access Control Point is WFF_TASKS.</p>
v2.0.forecasting-setup-tasks.post.multi_delete.summary=Deletes labor tasks.
v2.0.forecasting-setup-tasks.post.multi_delete.response.204.message=Success.
v2.0.forecasting-setup-tasks.post.multi_delete.response.207.message=[WFF-270029] - Completed with error(s). Detailed errors should be wrapped within this exception.
v2.0.forecasting-setup-tasks.post.multi_delete.response.400.message=<ul><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270006] - The following object reference is invalid: {refName}.</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li><li>[WFF-270061] - Your request includes multiple, mutually exclusive parameters: {paramName}.</li><li>[WFF-270015] - The following reference list cannot be empty: {paramName}.</li>[WFF-270029] - Completed with error(s). Detailed errors should be wrapped within this exception.</ul>
v2.0.forecasting-setup-tasks.post.multi_delete.response.403.message=[WFF-270022] - The user is not authorized to perform this action.
v2.0.forecasting-setup-tasks.post.multi_delete.param.multioperationtasksrequest=The Delete Labor Tasks request payload.

v2.0.forecasting-setup-tasks.post.versions.apply_delete.nickname=Delete Labor Task Effective Version by Criteria
v2.0.forecasting-setup-tasks.post.versions.apply_delete.notes=<p>This operation deletes a labor task effective version by ID and effective date and returns the labor task along with the remaining effective versions.</p><br /><p>The associated Access Control Point is WFF_TASKS.</p>
v2.0.forecasting-setup-tasks.post.versions.apply_delete.summary=Deletes a labor task effective version.
v2.0.forecasting-setup-tasks.post.versions.apply_delete.response.200.message=Success.
v2.0.forecasting-setup-tasks.post.versions.apply_delete.response.400.message=<ul><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270009] - Entity or entities not found. Type: Task. Name or names or ID or IDs: {qualifier/id}.</li><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-175029] - The Beginning of Time version cannot be deleted.</li><li>[WFF-175030] - You cannot delete effective version {effectiveSpan} that exists in the past.</li><li>[WFP-90100] - The date format is invalid.</li> <li>[WFP-00889] - The date is outside of the valid range of dates - Date = {Date}.</li></ul>
v2.0.forecasting-setup-tasks.post.versions.apply_delete.response.403.message=[WFF-270022] - The user is not authorized to perform this action.
v2.0.forecasting-setup-tasks.post.versions.apply_delete.param.taskapplydeleterequest=The Delete Labor Task Effective Version by Criteria request payload.