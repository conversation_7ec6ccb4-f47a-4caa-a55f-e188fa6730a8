v1.0.forecasting-setup-labor_distribution_group_profile.name=Labor Distribution Group Profiles
v1.0.forecasting-setup-labor_distribution_group_profile.description=<p>This resource allows you to create, retrieve, update, and delete labor distribution group profiles. Labor distribution group profiles allow labor distribution groups to be assigned to a site level or above.</p>
v1.0.forecasting-setup-labor_distribution_group_profile.parent=root.wff2.default

v1.0.forecasting-setup-labor_distribution_group_profile.post.nickname=Create Labor Distribution Group Profile
v1.0.forecasting-setup-labor_distribution_group_profile.post.notes=<p>This operation creates a labor distribution group profile.</p><p>The associated Access Control Point is as follows:</p><ul><li><strong>ACP Key:</strong> WFF_LABOR_DISTRIBUTION_GROUP_PROFILES</li><li><strong>API Access Controlled By:</strong> Edit: Allowed</li><li><strong>Default Value:</strong> Disallowed</li><li><strong>Location in the UI:</strong> Retail Manager > Manager - Common Setup > Forecast Configuration > Labor Distribution Group Profiles</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.summary=Creates a labor distribution group profile.
v1.0.forecasting-setup-labor_distribution_group_profile.post.response.200.message=Success.
v1.0.forecasting-setup-labor_distribution_group_profile.post.response.400.message=<p>Bad request:</p><ul><li>[WFF-270000] - The following collection cannot be empty: {listName}.</li><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270006] - The following object reference is invalid: {refName}.</li><li>[WFF-270008] - The following collection contains one or more duplicated entities: {listName}.</li><li>[WFF-270009] - Entity or entities not found. Type: {typeName}. Name or names or ID or IDs: {entityName}.</li><li>[WFF-270012] - The following parameter cannot be empty: {paramName}.</li><li>[WFF-270020] - {paramName} cannot contain any of the forbidden characters: {forbiddenSymbols}.</li><li>[WFF-270021] - {paramName} is too long. Max allowed length: {length} symbols.</li><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-270060] - {paramName} name {paramValue} is not unique.</li><li>[WFF-274905] - Labor Distribution Groups must have a unique Borrowing job in the Labor Distribution Group Profile.</li><li>[WFF-274904] - Location path {locationPathName} is not a site type or type above site.</li><li>[WFF-274906] - The following location already has another Labor Distribution Group Profile assigned to it: {objRef}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.param.model=The Create Labor Distribution Group Profile request payload.

v1.0.forecasting-setup-labor_distribution_group_profile.get.{id}.nickname=Retrieve Labor Distribution Group Profile by ID
v1.0.forecasting-setup-labor_distribution_group_profile.get.{id}.notes=<p>This operation returns a labor distribution group profile by ID.</p><p>The associated Access Control Point is as follows:</p><ul><li><strong>ACP Key:</strong> WFF_LABOR_DISTRIBUTION_GROUP_PROFILES</li><li><strong>API Access Controlled By:</strong> Edit: Allowed or View: Allowed</li><li><strong>Default Value:</strong> Disallowed</li><li><strong>Location in the UI:</strong> Retail Manager > Manager - Common Setup > Forecast Configuration > Labor Distribution Group Profiles</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.get.{id}.summary=Returns a labor distribution group profile.
v1.0.forecasting-setup-labor_distribution_group_profile.get.{id}.response.200.message=Success.
v1.0.forecasting-setup-labor_distribution_group_profile.get.{id}.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.get.{id}.response.404.message=<ul><li>[WFF-270032] - Entity not found. Type: {object}. Name or ID: {qualifier/id}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.get.{id}.param.id.value=The unique ID of a labor distribution group profile.

v1.0.forecasting-setup-labor_distribution_group_profile.post.apply_read.nickname=Retrieve Labor Distribution Group Profile by Location
v1.0.forecasting-setup-labor_distribution_group_profile.post.apply_read.notes=<p>This operation returns a labor distribution group profile by location.</p><p>The associated Access Control Point is as follows:</p><ul><li><strong>ACP Key:</strong> WFF_LABOR_DISTRIBUTION_GROUP_PROFILES</li><li><strong>API Access Controlled By:</strong> Edit: Allowed or View: Allowed</li><li><strong>Default Value:</strong> Disallowed</li><li><strong>Location in the UI:</strong> Retail Manager > Manager - Common Setup > Forecast Configuration > Labor Distribution Group Profiles</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.apply_read.summary=Returns a labor distribution group profile by location.
v1.0.forecasting-setup-labor_distribution_group_profile.post.apply_read.response.200.message=Success
v1.0.forecasting-setup-labor_distribution_group_profile.post.apply_read.response.400.message=<p>Bad request:</p><ul><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270006] - The following object reference is invalid: {refName}.</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names: {qualifier}.</li><li>[WFF-270025] - Request body is missing. Specify request parameters.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.apply_read.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.apply_read.param.context=The Retrieve Labor Distribution Group Profile by Location request payload, which contains a location reference and the assignment date of a labor distribution group profile.

v1.0.forecasting-setup-labor_distribution_group_profile.get.nickname=Retrieve All Labor Distribution Group Profiles or by Name
v1.0.forecasting-setup-labor_distribution_group_profile.get.notes=<p>This operation returns all labor distribution group profiles or a particular labor distribution group profile if a name is provided.</p><p>The associated Access Control Point is as follows:</p><ul><li><strong>ACP Key:</strong> WFF_LABOR_DISTRIBUTION_GROUP_PROFILES</li><li><strong>API Access Controlled By:</strong> Edit: Allowed or View: Allowed</li><li><strong>Default Value:</strong> Disallowed</li><li><strong>Location in the UI:</strong> Retail Manager > Manager - Common Setup > Forecast Configuration > Labor Distribution Group Profiles</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.get.summary=Returns all labor distribution group profiles or a particular labor distribution group profile by name.
v1.0.forecasting-setup-labor_distribution_group_profile.get.response.200.message=Success.
v1.0.forecasting-setup-labor_distribution_group_profile.get.response.404.message=<ul><li>[WFF-270032] - Entity not found. Type: {object}. Name or ID: {qualifier/id}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.get.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.get.param.name=The name of a labor distribution group profile.

v1.0.forecasting-setup-labor_distribution_group_profile.put.{id}.nickname=Update Labor Distribution Group Profile by ID
v1.0.forecasting-setup-labor_distribution_group_profile.put.{id}.notes=<p>This operation updates a labor distribution group profile by ID.</p><p>The associated Access Control Point is as follows:</p><ul><li><strong>ACP Key:</strong> WFF_LABOR_DISTRIBUTION_GROUP_PROFILES</li><li><strong>API Access Controlled By:</strong> Edit: Allowed</li><li><strong>Default Value:</strong> Disallowed</li><li><strong>Location in the UI:</strong> Retail Manager > Manager - Common Setup > Forecast Configuration > Labor Distribution Group Profiles</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.put.{id}.summary=Updates a labor distribution group profile.
v1.0.forecasting-setup-labor_distribution_group_profile.put.{id}.response.200.message=Success.
v1.0.forecasting-setup-labor_distribution_group_profile.put.{id}.response.400.message=<p>Bad request:</p><ul><li>[WFF-270000] - The following collection cannot be empty: {listName}.</li><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270005] - ID from path does not match ID from request payload.</li><li>[WFF-270006] - The following object reference is invalid: {refName}.</li><li>[WFF-270008] - The following collection contains one or more duplicated entities: {listName}.</li><li>[WFF-270009] - Entity or entities not found. Type: {typeName}. Name or names or ID or IDs: {entityName}.</li><li>[WFF-270012] - The following parameter cannot be empty: {paramName}.</li><li>[WFF-270020] - {paramName} cannot contain any of the forbidden characters: {forbiddenSymbols}.</li><li>[WFF-270021] - {paramName} is too long. Max allowed length: {length} symbols.</li><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-270060] - {paramName} name {paramValue} is not unique.</li><li>[WFF-274905] - Labor Distribution Groups must have a unique Borrowing job in the Labor Distribution Group Profile.</li><li>[WFF-274904] - Location path {locationPathName} is not a site type or type above site.</li><li>[WFF-274906] - The following location already has another Labor Distribution Group Profile assigned to it: {objRef}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.put.{id}.response.404.message=<ul><li>[WFF-270032] - Entity not found. Type: {object}. Name or ID: {qualifier/id}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.put.{id}.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.put.{id}.response.409.message=<ul><li>[WFF-270011] - Resource was already modified.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.put.{id}.param.laborDistributionGroupProfileId=The ID of a labor distribution group profile.
v1.0.forecasting-setup-labor_distribution_group_profile.put.{id}.param.model=The Update Labor Distribution Group Profile by ID request payload.

v1.0.forecasting-setup-labor_distribution_group_profile.delete.{id}.nickname=Delete Labor Distribution Group Profile by ID
v1.0.forecasting-setup-labor_distribution_group_profile.delete.{id}.notes=<p>This operation deletes a labor distribution group profile by ID.</p><p>The associated Access Control Point is as follows:</p><ul><li><strong>ACP Key:</strong> WFF_LABOR_DISTRIBUTION_GROUP_PROFILES</li><li><strong>API Access Controlled By:</strong> Edit: Allowed</li><li><strong>Default Value:</strong> Disallowed</li><li><strong>Location in the UI:</strong> Retail Manager > Manager - Common Setup > Forecast Configuration > Labor Distribution Group Profiles</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.delete.{id}.summary=Deletes a labor distribution group profile.
v1.0.forecasting-setup-labor_distribution_group_profile.delete.{id}.response.204.message=No Content.
v1.0.forecasting-setup-labor_distribution_group_profile.delete.{id}.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.delete.{id}.response.404.message=<ul><li>[WFF-270032] - Entity not found. Type: {object}. Name or ID: {qualifier/id}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.delete.{id}.param.id.value=The unique ID of a labor distribution group profile.
v1.0.forecasting-setup-labor_distribution_group_profile.delete.{id}.param.id=The unique ID of a labor distribution group profile.

v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_read.nickname=Retrieve Labor Distribution Group Profiles
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_read.notes=This operation returns one or more labor distribution group profiles.\n\n__Note:__ This operation supports a payload size of up to 1 MB.\n\nThe associated Access Control Point is as follows:\n\n* __ACP Key:__ WFF_LABOR_DISTRIBUTION_GROUP_PROFILES\n\n* __API Access Controlled By:__ Edit: Allowed\n\n* __Default Value:__ Disallowed\n\n* __Location in the UI:__ Retail Manager > Manager - Common Setup > Forecast Configuration > Labor Distribution Group Profiles\n\n## Example call\n\n### Example request\n\n```\n{\n  "where": {\n    "laborDistributionGroupProfiles": {\n      "ids": [\n        1,\n        2\n      ]\n    },\n    "asOfDate": "2022-06-21"\n  }\n}\n\n```\n\n### Example response\n\n```\n[\n  {\n    "id": 1,\n    "name": "Labor Distribution GroupProfile1",\n    "description": "Labor Distribution Group Profile Alaska",\n    "version": 1,\n    "laborDistributionGroups": [\n      {\n        "id": 18,\n        "qualifier": "Labor Distribution Group 1"\n      },\n      {\n        "id": 19,\n        "qualifier": "Labor Distribution Group 2"\n      }\n    ],\n    "laborDistributionGroupAssignments": [\n      {\n        "id": 486,\n        "qualifier": "Organization/United States/Metropolitan Store/Alaska"\n      }\n    ]\n  },\n  {\n    "id": 2,\n    "name": "Labor Distribution GroupProfile2",\n    "description": "Labor Distribution Group Profile desc",\n    "version": 1,\n    "laborDistributionGroups": [\n      {\n        "id": 20,\n        "qualifier": "Labor Distribution Group 3"\n      },\n      {\n        "id": 21,\n        "qualifier": "Labor Distribution Group 4"\n      }\n    ],\n    "laborDistributionGroupAssignments": [\n      {\n        "id": 201,\n        "qualifier": "Td1"\n      }\n    ]\n  }\n]\n\n```
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_read.summary=Returns one or more labor distribution group profiles.
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_read.response.200.message=Success
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_read.response.207.message=Partial success in retrieving labor distribution group profiles.<ul><li>[WFF-270006] - The following object reference is invalid: {refName}.</li><li>[WFF-270032] - Entity not found. Type: Labor Distribution Group Profile. Name or ID: {qualifier/id}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_read.response.400.message=<p>Bad request:</p><ul><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-270001] - You must specify the following parameter: {objectName}.</li><li>[WFF-270061] - Your request includes multiple, mutually exclusive parameters: [ids, qualifiers, refs].</li><li>[WFF-270015] - The following reference list cannot be empty: {paramName}.</li><li>[WFF-270006] - The following object reference is invalid: {refName}.</li><li>[WFF-270032] - Entity not found. Type: Labor Distribution Group Profile. Name or ID: {qualifier/id}.</li</ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_read.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_read.response.413.message=Request entity too large.<ul><li>[WFF-274908] - The specified list of Labor Distribution Group Profiles exceeds the maximum number: {maxNumber}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_read.param.context=The Retrieve Labor Distribution Group Profiles request payload.

v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_create.nickname=Create Labor Distribution Group Profiles
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_create.notes=This operation creates one or more labor distribution group profiles.\n\n__Note:__ This operation supports a payload size of up to 1 MB.\n\nThe associated Access Control Point is as follows:\n\n* __ACP Key:__ WFF_LABOR_DISTRIBUTION_GROUP_PROFILES\n\n* __API Access Controlled By:__ Edit: Allowed\n\n* __Default Value:__ Disallowed\n\n* __Location in the UI:__ Retail Manager > Manager - Common Setup > Forecast Configuration > Labor Distribution Group Profiles\n\n## Service limit\n\nA service limit constrains the number of labor distribution group profiles that can be processed in a single request.\n\n* The __Number of Labor Distribution Group Profiles__ cannot exceed 5,000.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.\n\n## Example call\n\n### Example request\n\n```\n[\n  {\n    "name": "Labor Distribution GroupProfile1",\n    "description": "Labor Distribution Group Profile Alaska",\n    "laborDistributionGroups": [\n      {\n        "id": 18,\n        "qualifier": "Labor Distribution Group 1"\n      },\n      {\n        "id": 19,\n        "qualifier": "Labor Distribution Group 2"\n      }\n    ],\n    "laborDistributionGroupAssignments": [\n      {\n        "id": 486,\n        "qualifier": "Organization/United States/Metropolitan Store/Alaska"\n      }\n    ],\n    "asOdDate": "2022-06-21"\n  },\n  {\n    "name": "Labor Distribution GroupProfile2",\n    "description": "Labor Distribution Group Profile desc",\n    "laborDistributionGroups": [\n      {\n        "id": 20,\n        "qualifier": "Labor Distribution Group 3"\n      },\n      {\n        "id": 21,\n        "qualifier": "Labor Distribution Group 4"\n      }\n    ],\n    "laborDistributionGroupAssignments": [\n      {\n        "id": 201,\n        "qualifier": "Td1"\n      }\n    ],\n    "asOdDate": "2022-06-22"\n  }\n]\n\n```\n\n### Example response\n\n```\n[\n  {\n    "id": 1,\n    "name": "Labor Distribution GroupProfile1",\n    "description": "Labor Distribution Group Profile Alaska",\n    "version": 1,\n    "laborDistributionGroups": [\n      {\n        "id": 18,\n        "qualifier": "Labor Distribution Group 1"\n      },\n      {\n        "id": 19,\n        "qualifier": "Labor Distribution Group 2"\n      }\n    ],\n    "laborDistributionGroupAssignments": [\n      {\n        "id": 486,\n        "qualifier": "Organization/United States/Metropolitan Store/Alaska"\n      }\n    ]\n  },\n  {\n    "id": 2,\n    "name": "Labor Distribution GroupProfile2",\n    "description": "Labor Distribution Group Profile desc",\n    "version": 1,\n    "laborDistributionGroups": [\n      {\n        "id": 20,\n        "qualifier": "Labor Distribution Group 3"\n      },\n      {\n        "id": 21,\n        "qualifier": "Labor Distribution Group 4"\n      }\n    ],\n    "laborDistributionGroupAssignments": [\n      {\n        "id": 201,\n        "qualifier": "Td1"\n      }\n    ]\n  }\n]\n\n```
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_create.summary=Creates one or more labor distribution group profiles.
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_create.response.200.message=Success
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_create.response.207.message=Partial success in creating and retrieving labor distribution group profiles.<ul><li>[WFF-270060] - {paramName} name {paramValue} is not unique.</li><li>[WFF-270020] - {paramName} cannot contain any of the forbidden characters: {forbiddenSymbols}.</li><li>[WFF-270021] - {paramName} is too long. Max allowed length: {length} symbols.</li><li>[WFF-270000] - The following collection cannot be empty: {listName}.</li><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270006] - The following object reference is invalid: {refName}</li><li>[WFF-270008] - The following collection contains one or more duplicated entities: {listName}</li><li>[WFF-270012] - The following parameter cannot be empty: {paramName}.</li><li>[WFF-274904] - Location path {locationPathName} is not a site type or type above site.</li><li>[WFF-274905] - Labor Distribution Groups must have unique Borrowing job in the Labor Distribution Group Profile.</li><li>[WFF-274906] - The following location already has another Labor Distribution Group Profile assigned to it: {objRef}.</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_create.response.400.message=<p>Bad request:</p><ul><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-270060] - {paramName} name {paramValue} is not unique.</li><li>[WFF-270020] - {paramName} cannot contain any of the forbidden characters: {forbiddenSymbols}.</li><li>[WFF-270021] - {paramName} is too long. Max allowed length: {length} symbols.</li><li>[WFF-270000] - The following collection cannot be empty: {listName}.</li><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270006] - The following object reference is invalid: {refName}</li><li>[WFF-270008] - The following collection contains one or more duplicated entities: {listName}</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li><li>[WFF-270012] - The following parameter cannot be empty: {paramName}.</li><li>[WFF-274904] - Location path {locationPathName} is not a site type or type above site.</li><li>[WFF-274905] - Labor Distribution Groups must have unique Borrowing job in the Labor Distribution Group Profile.</li><li>[WFF-274906] - The following location already has another Labor Distribution Group Profile assigned to it: {objRef}.</li><li>[WFF-270015] - The following collection cannot be empty: Labor Distribution Group Profiles.</li><li>[WFF-270013] - The request contains the following entity type which has the following duplicated parameter. Entity type: {object}. Parameter: {paramName}. Value duplicated: {paramValue}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_create.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_create.response.413.message=Request entity too large.<ul><li>[WFF-274908] - The specified list of Labor Distribution Group Profiles exceeds the maximum number: {maxNumber}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_create.param.models=The Create Labor Distribution Group Profiles request payload.

v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_update.nickname=Update Labor Distribution Group Profiles
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_update.notes=This operation updates one or more labor distribution group profiles.\n\n__Note:__ This operation supports a payload size of up to 1 MB.\n\nThe associated Access Control Point is as follows:\n\n* __ACP Key:__ WFF_LABOR_DISTRIBUTION_GROUP_PROFILES\n\n* __API Access Controlled By:__ Edit: Allowed\n\n* __Default Value:__ Disallowed\n\n* __Location in the UI:__ Retail Manager > Manager - Common Setup > Forecast Configuration > Labor Distribution Group Profiles\n\n## Service limit\n\nA service limit constrains the number of labor distribution group profiles that can be processed in a single request.\n\n* The __Number of Labor Distribution Group Profiles__ cannot exceed 5,000.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.\n\n## Example call\n\n### Example request\n\n```\n[\n  {\n    "name": "Labor Distribution GroupProfile1",\n    "description": "Labor Distribution Group Profile Alaska",\n    "version": 1,\n    "laborDistributionGroups": [\n      {\n        "id": 18,\n        "qualifier": "Labor Distribution Group 1"\n      },\n      {\n        "id": 19,\n        "qualifier": "Labor Distribution Group 2"\n      }\n    ],\n    "laborDistributionGroupAssignments": [\n      {\n        "id": 486,\n        "qualifier": "Organization/United States/Metropolitan Store/Alaska"\n      }\n    ],\n    "asOdDate": "2022-06-21"\n  },\n  {\n    "name": "Labor Distribution GroupProfile2",\n    "description": "Labor Distribution Group Profile desc",\n    "version": 1,\n    "laborDistributionGroups": [\n      {\n        "id": 20,\n        "qualifier": "Labor Distribution Group 3"\n      },\n      {\n        "id": 21,\n        "qualifier": "Labor Distribution Group 4"\n      }\n    ],\n    "laborDistributionGroupLocationAssignments": [\n      {\n        "id": 201,\n        "qualifier": "Td1"\n      }\n    ],\n    "asOdDate": "2022-06-22"\n  }\n]\n\n```\n\n### Example response\n\n```\n[\n  {\n    "id": 1,\n    "name": "Labor Distribution GroupProfile1",\n    "description": "Labor Distribution Group Profile Alaska",\n    "version": 2,\n    "laborDistributionGroups": [\n      {\n        "id": 18,\n        "qualifier": "Labor Distribution Group 1"\n      },\n      {\n        "id": 19,\n        "qualifier": "Labor Distribution Group 2"\n      }\n    ],\n    "laborDistributionGroupAssignments": [\n      {\n        "id": 486,\n        "qualifier": "Organization/United States/Metropolitan Store/Alaska"\n      }\n    ]\n  },\n  {\n    "id": 2,\n    "name": "Labor Distribution GroupProfile2",\n    "description": "Labor Distribution Group Profile desc",\n    "version": 2,\n    "laborDistributionGroups": [\n      {\n        "id": 20,\n        "qualifier": "Labor Distribution Group 3"\n      },\n      {\n        "id": 21,\n        "qualifier": "Labor Distribution Group 4"\n      }\n    ],\n    "laborDistributionGroupAssignments": [\n      {\n        "id": 201,\n        "qualifier": "Td1"\n      }\n    ]\n  }\n]\n\n```
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_update.summary=Updates one or more labor distribution group profiles.
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_update.response.200.message=Success
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_update.response.207.message=Partial success in updating and retrieving labor distribution group profiles.<ul><li>[WFF-270060] - {paramName} name {paramValue} is not unique.</li><li>[WFF-270020] - {paramName} cannot contain any of the forbidden characters: {forbiddenSymbols}.</li><li>[WFF-270021] - {paramName} is too long. Max allowed length: {length} symbols.</li><li>[WFF-270000] - The following collection cannot be empty: {listName}.</li><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270006] - The following object reference is invalid: {refName}</li><li>[WFF-270008] - The following collection contains one or more duplicated entities: {listName}</li><li>[WFF-270012] - The following parameter cannot be empty: {paramName}.</li><li>[WFF-274904] - Location path {locationPathName} is not a site type or type above site.</li><li>[WFF-274905] - Labor Distribution Groups must have unique Borrowing job in the Labor Distribution Group Profile.</li><li>[WFF-274906] - The following location already has another Labor Distribution Group Profile assigned to it: {objRef}.</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_update.response.400.message=<p>Bad request:</p><ul><li>[WFF-270011] - Resource was already modified.</li><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-270060] - {paramName} name {paramValue} is not unique.</li><li>[WFF-270020] - {paramName} cannot contain any of the forbidden characters: {forbiddenSymbols}.</li><li>[WFF-270021] - {paramName} is too long. Max allowed length: {length} symbols.</li><li>[WFF-270000] - The following collection cannot be empty: {listName}.</li><li>[WFF-270001] - You must specify the following parameter: {paramName}.</li><li>[WFF-270006] - The following object reference is invalid: {refName}</li><li>[WFF-270008] - The following collection contains one or more duplicated entities: {listName}</li><li>[WFF-270009] - Entity or entities not found. Type: {object}. Name or names or ID or IDs: {qualifier/id}.</li><li>[WFF-270012] - The following parameter cannot be empty: {paramName}.</li><li>[WFF-274904] - Location path {locationPathName} is not a site type or type above site.</li><li>[WFF-274905] - Labor Distribution Groups must have unique Borrowing job in the Labor Distribution Group Profile.</li><li>[WFF-274906] - The following location already has another Labor Distribution Group Profile assigned to it: {objRef}.</li><li>[WFF-270015] - The following collection cannot be empty: Labor Distribution Group Profiles.</li><li>[WFF-270013] - The request contains the following entity type which has the following duplicated parameter. Entity type: {object}. Parameter: {paramName}. Value duplicated: {paramValue}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_update.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_update.response.413.message=Request entity too large.<ul><li>[WFF-274908] - The specified list of Labor Distribution Group Profiles exceeds the maximum number: {maxNumber}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_update.param.models=The Update Labor Distribution Group Profiles request payload.

v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_delete.nickname=Delete Labor Distribution Group Profiles
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_delete.notes=This operation deletes one or more labor distribution group profiles.\n\n__Note:__ This operation supports a payload size of up to 1 MB.\n\nThe associated Access Control Point is as follows:\n\n* __ACP Key:__ WFF_LABOR_DISTRIBUTION_GROUP_PROFILES\n\n* __API Access Controlled By:__ Edit: Allowed\n\n* __Default Value:__ Disallowed\n\n* __Location in the UI:__ Retail Manager > Manager - Common Setup > Forecast Configuration > Labor Distribution Group Profiles\n\n## Service limit\n\nA service limit constrains the number of labor distribution group profiles that can be processed in a single request.\n\n* The __Number of Labor Distribution Group Profiles__ cannot exceed 5,000.\n\nRefer to the [Limits](doc:limits-doc) topic for more information.
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_delete.summary=Deletes one or more labor distribution group profiles.
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_delete.response.204.message=No Content
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_delete.response.207.message=Partial success in deleting Labor Distribution Group Profile.<ul><li>[WFF-270001] - You must specify the following parameter: {objectName}.</li><li>[WFF-270006] - The following object reference is invalid: {refName}.</li><li>[WFF-270032] - Entity not found. Type: Labor Distribution Group Profile. Name or ID: {qualifier/id}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_delete.response.400.message=<p>Bad request:</p><ul><li>[WFF-270001] - You must specify the following parameter: {objectName}.</li><li>[WFF-270006] - The following object reference is invalid: {refName}.</li><li>[WFF-270015] - The following reference list cannot be empty: {objectName}.</li><li>[WFF-270025] - Request body is missing. Specify request parameters.</li><li>[WFF-270032] - Entity not found. Type: Labor Distribution Group. Name or ID: {qualifier/id}.</li><li>[WFF-270061] - Your request includes multiple, mutually exclusive parameters: [ids, qualifiers, refs].</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_delete.response.403.message=<ul><li>[WFF-270022] - The user is not authorized to perform this action.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_delete.response.413.message=Request entity too large.<ul><li>[WFF-274908] - The specified list of Labor Distribution Group Profile exceeds the maximum number: {maxNumberOfLaborDistributionGroupProfile}.</li></ul>
v1.0.forecasting-setup-labor_distribution_group_profile.post.multi_delete.param.context=The Delete Labor Distribution Group Profiles request payload.