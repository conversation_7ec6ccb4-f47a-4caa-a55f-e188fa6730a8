v1.0.activities_setup-pay_code_action.name=Activity Actions
v1.0.activities_setup-pay_code_action.description=This resource allows you to retrieve and manipulate activity actions.\n\nPay code actions serve either one of two purposes:\n\n1. __Associate pay codes with activities__ - Eliminates the need to enter events for specific activities. For example, if you associate the "Vacation" pay code with the "Vacation" activity, when you enter the "Vacation" pay code as an edit, the timecard creates an event for "Vacation."\n\n2. __Allocate hours in a pay code to multiple projects__ - As an example, you can distribute employees' paid time off according to the percentage of hours worked on each project during a pay period. This is called dynamic allocation because the allocation changes whenever entered hours change for the projects in the pay period.\n\nYou can associate only hours-based pay codes with pay code actions.\n\nRefer to the [Pay Code Actions](doc:pay-code-actions-doc) topic for example calls and a tutorial.
v1.0.activities_setup-pay_code_action.parent=root.work.default

v1.0.activities_setup-pay_code_action.get.{name}.nickname=Retrieve All Activity Actions or by Name
v1.0.activities_setup-pay_code_action.get.{name}.notes=This operation returns a list of all activity actions or returns a single activity action by name.
v1.0.activities_setup-pay_code_action.get.{name}.summary=Returns all activity actions or one by name.
v1.0.activities_setup-pay_code_action.get.{name}.response.200.message=Success.
v1.0.activities_setup-pay_code_action.get.{name}.response.403.message=<ul><li>[WFA-000030] - The current user does not have access to {facpName}.</li></ul>
v1.0.activities_setup-pay_code_action.get.{name}.response.404.message=<ul><li>[WFA-000007] - Entity or entities not found. Type: Activity Action. Name or names or ID or IDs: {id}.</li></ul>
v1.0.activities_setup-pay_code_action.get.{name}.queryparam.name.value=The name of an activity action.
# Added the following key-value pair as it was missing from 09.03.00 release.
v1.0.activities_setup-pay_code_action.get.{type}.queryparam.name.value=The type of an activity action.
v1.0.activities_setup-pay_code_action.get.response.403.message=<ul><li>[WFA-000030] - The current user does not have access to {facpName}.</li></ul>

v1.0.activities_setup-pay_code_action.get.{id}.nickname=Retrieve Activity Action by ID
v1.0.activities_setup-pay_code_action.get.{id}.notes=This operation returns a activity action by ID.
v1.0.activities_setup-pay_code_action.get.{id}.summary=Returns a activity action.
v1.0.activities_setup-pay_code_action.get.{id}.response.200.message=Success.
v1.0.activities_setup-pay_code_action.get.{id}.response.403.message=<ul><li>[WFA-000030] - The current user does not have access to {facpName}.</li></ul>
v1.0.activities_setup-pay_code_action.get.{id}.response.404.message=<ul><li>[WFA-000007] - Entity or entities not found. Type: Activity Action. Name or names or ID or IDs: {id}.</li></ul>
v1.0.activities_setup-pay_code_action.get.{id}.pathparam.id.value=The unique ID of a activity action.

v1.0.activities_setup-pay_code_action.post.multi_read.nickname=Retrieve Activity Actions   
v1.0.activities_setup-pay_code_action.post.multi_read.notes=This operation returns activity actions by object references.  
v1.0.activities_setup-pay_code_action.post.multi_read.summary=Returns activity actions.  
v1.0.activities_setup-pay_code_action.post.multi_read.response.200.message=Success.  
v1.0.activities_setup-pay_code_action.post.multi_read.response.207.message=<ul><li>[WFA-000036] - Some of the entities were not exported because of errors in the source data. Correct those errors and try again.</li></ul>
v1.0.activities_setup-pay_code_action.post.multi_read.response.400.message=<ul><li>[WFA-000037] - None of the entities were exported because of errors in the source data. Correct those errors and try again.</li></ul>
v1.0.activities_setup-pay_code_action.post.multi_read.response.403.message=<ul><li>[WFA-000030] - The current user does not have access to {facpName}.</li></ul>
v1.0.activities_setup-pay_code_action.post.multi_read.param.refs=The Retrieve Activity Actions request payload.

v1.0.activities_setup-pay_code_action.post.create.nickname=Create Activity Action
v1.0.activities_setup-pay_code_action.post.create.notes=This operation creates a activity action.
v1.0.activities_setup-pay_code_action.post.create.summary=Creates a activity action.
v1.0.activities_setup-pay_code_action.post.create.response.200.message=Success.
v1.0.activities_setup-pay_code_action.post.create.response.400.message=<ul><li>[WFA-000003] - You must specify the following parameter: {paramName}.</li><li>[WFA-000008] - The following parameter cannot be empty: {paramName}.</li><li>[WFA-000027] - The two instances of the following parameter must have the same value: {paramName}.</li><li>[WFA-000008] - The following parameter cannot be empty: name.</li><li>[WFA-000015] - Name length exceeds N characters. Shorten the name.</li><li>[WFA-000016] - The field name cannot contain any of the following characters:<>()/';#.,$%=!.</li><li>[WFA-000015] - Name length exceeds N characters. Shorten the name.</li><li>[WFA-000008] - The following parameter cannot be empty: name.</li><li>WFA-000003 - You must specify the following parameter: name.</li><li>[WFA-000016] - The field name cannot contain any of the following characters:<>()/';#.,$%=!.</li><li>WFA-000018 - The following parameter cannot be a negative number: level.</li><li>WFA-000018 - The following parameter cannot be a negative number: sequence.</li><li>WFA-000004 - The following parameter must be null: header.id.</li><li>WFA-212009 - You cannot add a level 2 - 4 activity under a pre-defined activity.</li><li>WFA-212011 - Activity cannot contain labor category entry lists with the same labor category. Labor Category: {paramName}, Labor Category Entry List: {paramName}.</li></ul>
v1.0.activities_setup-pay_code_action.post.create.response.403.message=<ul><li>[WFA-000030] - The current user does not have access to {facpName}.</li></ul>
v1.0.activities_setup-pay_code_action.post.create.param.payCodeAction=The Create Activity Action request payload.

v1.0.activities_setup-pay_code_action.post.multi_create.nickname=Create Activity Actions
v1.0.activities_setup-pay_code_action.post.multi_create.notes=This operation creates one or more Activity Actions.
v1.0.activities_setup-pay_code_action.post.multi_create.summary=Creates one or more Activity Actions.
v1.0.activities_setup-pay_code_action.post.multi_create.response.200.message=Success.
v1.0.activities_setup-pay_code_action.post.multi_create.response.207.message=<ul><li>[WFA-000034] - Some of the entities were not imported because of errors in the source data. Correct those errors and try again.</li></ul>
v1.0.activities_setup-pay_code_action.post.multi_create.response.400.message=<ul><li>[WFA-000035] - None of the entities were imported because of errors in the source data. Correct those errors and try again.</li></ul>
v1.0.activities_setup-pay_code_action.post.multi_create.response.403.message=<ul><li>[WFA-000030] - The current user does not have access to {facpName}.</li></ul>
v1.0.activities_setup-pay_code_action.post.multi_create.param.payCodeActions=The Create Activity Actions request payload.

v1.0.activities_setup-pay_code_action.put.{id}.nickname=Update Activity Action by ID
v1.0.activities_setup-pay_code_action.put.{id}.notes=This operation updates a Activity Action by ID.
v1.0.activities_setup-pay_code_action.put.{id}.summary=Updates a Activity Action.
v1.0.activities_setup-pay_code_action.put.{id}.response.200.message=Success.
v1.0.activities_setup-pay_code_action.put.{id}.response.400.message=<ul><li>[WFA-000003] - You must specify the following parameter: {paramName}.</li><li>[WFA-000008] - The following parameter cannot be empty: {paramName}.</li><li>[WFA-000027] - The two instances of the following parameter must have the same value: {paramName}.</li><li>[WFA-000008] - The following parameter cannot be empty: name.</li><li>[WFA-000015] - Name length exceeds N characters. Shorten the name.</li><li>[WFA-000016] - The field name cannot contain any of the following characters:<>()/';#.,$%=!.</li><li>[WFA-000015] - Name length exceeds N characters. Shorten the name.</li><li>[WFA-000008] - The following parameter cannot be empty: name.</li><li>WFA-000003 - You must specify the following parameter: name.</li><li>[WFA-000016] - The field name cannot contain any of the following characters:<>()/';#.,$%=!.</li><li>WFA-000018 - The following parameter cannot be a negative number: level.</li><li>WFA-000018 - The following parameter cannot be a negative number: sequence.</li><li>WFA-000004 - The following parameter must be null: header.id.</li><li>WFA-212009 - You cannot add a level 2 - 4 activity under a pre-defined activity.</li><li>WFA-212011 - Activity cannot contain labor category entry lists with the same labor category. Labor Category: {paramName}, Labor Category Entry List: {paramName}.</li></ul>
v1.0.activities_setup-pay_code_action.put.{id}.response.404.message=<ul><li>[WFA-000007] - Entity or entities not found. Type: Activity Action. Name or names or ID or IDs: {id}.</li></ul>
v1.0.activities_setup-pay_code_action.put.{id}.response.403.message=<ul><li>[WFA-000030] - The current user does not have access to {facpName}.</li></ul>
v1.0.activities_setup-pay_code_action.put.{id}.pathparam.id.value=The unique ID of a Activity Action.
v1.0.activities_setup-pay_code_action.put.{id}.param.payCodeAction=The Update Activity Action by ID request payload.

v1.0.activities_setup-pay_code_action.post.multi_update.nickname=Update Activity Actions
v1.0.activities_setup-pay_code_action.post.multi_update.notes=This operation updates one or more Activity Actions.
v1.0.activities_setup-pay_code_action.post.multi_update.summary=Updates one or more Activity Actions.
v1.0.activities_setup-pay_code_action.post.multi_update.response.200.message=Success.
v1.0.activities_setup-pay_code_action.post.multi_update.response.207.message=<ul><li>[WFA-000034] - Some of the entities were not imported because of errors in the source data. Correct those errors and try again.</li></ul>
v1.0.activities_setup-pay_code_action.post.multi_update.response.400.message=<ul><li>[WFA-000035] - None of the entities were imported because of errors in the source data. Correct those errors and try again.</li></ul>
v1.0.activities_setup-pay_code_action.post.multi_update.response.403.message=<ul><li>[WFA-000030] - The current user does not have access to {facpName}.</li></ul>
v1.0.activities_setup-pay_code_action.post.multi_update.param.payCodeActions=The Update Activity Actions request payload.

v1.0.activities_setup-pay_code_action.delete.{id}.nickname=Delete Activity Action by ID
v1.0.activities_setup-pay_code_action.delete.{id}.notes=This operation deletes a Activity Action by ID.
v1.0.activities_setup-pay_code_action.delete.{id}.summary=Deletes a Activity Action.
v1.0.activities_setup-pay_code_action.delete.{id}.response.204.message=Success.
v1.0.activities_setup-pay_code_action.delete.{id}.response.400.message=<ul><li>[WFA-000003] - You must specify the following parameter: {paramName}.</li><li>[WFA-000008] - The following parameter cannot be empty: {paramName}.</li><li>[WFA-000027] - The two instances of the following parameter must have the same value: {paramName}.</li><li>[WFA-000008] - The following parameter cannot be empty: name.</li><li>[WFA-000015] - Name length exceeds N characters. Shorten the name.</li><li>[WFA-000016] - The field name cannot contain any of the following characters:<>()/';#.,$%=!.</li><li>[WFA-000015] - Name length exceeds N characters. Shorten the name.</li><li>[WFA-000008] - The following parameter cannot be empty: name.</li><li>WFA-000003 - You must specify the following parameter: name.</li><li>[WFA-000016] - The field name cannot contain any of the following characters:<>()/';#.,$%=!.</li><li>WFA-000018 - The following parameter cannot be a negative number: level.</li><li>WFA-000018 - The following parameter cannot be a negative number: sequence.</li><li>WFA-000004 - The following parameter must be null: header.id.</li><li>WFA-212009 - You cannot add a level 2 - 4 activity under a pre-defined activity.</li><li>WFA-212011 - Activity cannot contain labor category entry lists with the same labor category. Labor Category: {paramName}, Labor Category Entry List: {paramName}.</li></ul>
v1.0.activities_setup-pay_code_action.delete.{id}.response.403.message=<ul><li>[WFA-000030] - The current user does not have access to {facpName}.</li></ul>
v1.0.activities_setup-pay_code_action.delete.{id}.response.404.message=<ul><li>[WFA-000007] - Entity or entities not found. Type: Activity Action. Name or names or ID or IDs: {id}.</li></ul>
v1.0.activities_setup-pay_code_action.delete.{id}.pathparam.id.value=The ID of a Activity Action.

v1.0.activities_setup-pay_code_action.post.multi_delete.nickname=Delete Activity Actions
v1.0.activities_setup-pay_code_action.post.multi_delete.notes=This operation deletes one or more Activity Actions by object references.
v1.0.activities_setup-pay_code_action.post.multi_delete.summary=Deletes one or more Activity Actions.
v1.0.activities_setup-pay_code_action.post.multi_delete.response.204.message=Success.
v1.0.activities_setup-pay_code_action.post.multi_delete.response.207.message=<ul><li>[WFA-000038] - Some of the entities were not deleted because of errors in the source data. Correct those errors and try again.</li></ul>
v1.0.activities_setup-pay_code_action.post.multi_delete.response.400.message=<ul><li>[WFA-000039] - None of the entities were deleted because of errors in the source data. Correct those errors and try again.</li></ul>
v1.0.activities_setup-pay_code_action.post.multi_delete.response.403.message=<ul><li>[WFA-000030] - The current user does not have access to {facpName}.</li></ul>.
v1.0.activities_setup-pay_code_action.post.multi_delete.param.refs=The Delete Activity Actions request payload.     