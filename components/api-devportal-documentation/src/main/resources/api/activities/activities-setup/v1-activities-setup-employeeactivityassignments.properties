v1.0.activities_setup-employee_activity_assignments.name=Activity Assignments
v1.0.activities_setup-employee_activity_assignments.description=<p>This resource allows you to retrieve the activities directly assigned to employees.</p>
v1.0.activities_setup-employee_activity_assignments.parent=root.work.default

v1.0.activities_setup-employee_activity_assignments.post.multi_read.nickname=Retrieve Activities for Multiple Employees
v1.0.activities_setup-employee_activity_assignments.post.multi_read.notes=<p>This operation returns the activities assigned to employees.</p><br /><p>The associated Access Control Point is WFA_WORK_INTEGRATION_API.</p>
v1.0.activities_setup-employee_activity_assignments.post.multi_read.summary=Returns activities assigned to employees.
v1.0.activities_setup-employee_activity_assignments.post.multi_read.response.200.message=Success.
v1.0.activities_setup-employee_activity_assignments.post.multi_read.response.207.message=<ul><li>[WFA-000036] - Some of the entities were not exported because of errors in the source data. Correct those errors and try again.</li></ul>
v1.0.activities_setup-employee_activity_assignments.post.multi_read.response.400.message=<ul><li>[WFA-000037] - None of the entities were exported because of errors in the source data. Correct those errors and try again.</li></ul>
v1.0.activities_setup-employee_activity_assignments.post.multi_read.response.403.message=<ul><li>[WFA-000030] - The current user does not have access to {facpName}.</li></ul>
v1.0.activities_setup-employee_activity_assignments.post.multi_read.param.refs=The Retrieve Activities for Multiple Employees request payload.

