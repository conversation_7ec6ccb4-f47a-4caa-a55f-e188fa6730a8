API Path,API Type,Publishing Certified,V1.0,Action Paths,Bean Name
/authentication/access_token,PUBLIC,yes,yes,"POST-/,POST-/refresh",com.kronos.openam.api.Authentication
/authz/v1/token,PRIVATE,no,no,GET-/,com.kronos.authz.sac.impl.rest.service.AuthzTokenRestServiceImpl
/business_analytics/v1/conditional_attribute_values,PRIVATE,no,no,GET-/,com.kronos.kpifrm.builder.business.api.rest.services.ConditionalAttributeValueRestService
/business_analytics/v1/historical_computation,PRIVATE,no,no,POST-/initiate,com.kronos.kpifrm.builder.business.api.rest.services.HistoricalLoadRestService
/business_analytics/v1/kpi_columns,PRIVATE,no,no,GET-/,com.kronos.kpifrm.computeengine.evaluator.impl.rest.services.KPIDefinitionRestService
/business_analytics/v1/kpi_values/multi_read,PRIVATE,no,no,POST-/,com.kronos.kpifrm.computeengine.evaluator.impl.rest.services.KPIEvaluationRestService
/business_analytics/v1/kpis,PRIVATE,no,no,"GET-/,GET-/{id},POST-/,POST-/build_formula,POST-/get_kpis,POST-/{id}",com.kronos.kpifrm.builder.business.api.rest.services.KPIRestService
/business_analytics/v1/kpis,PRIVATE,no,no,"GET-/actions,GET-/dataSpec,GET-/permissions,GET-/uiSpec",com.kronos.kpifrm.builder.business.api.rest.services.KPIUIRestService
/business_analytics/v1/kpis/sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,PUT-/{key}",com.kronos.kpifrm.builder.business.api.rest.sdm.KPISDMRestService
/business_analytics/v1/mapping_categories,PRIVATE,no,no,"GET-/,GET-/{id},POST-/,POST-/{id}",com.kronos.kpifrm.builder.business.api.rest.services.MappingCategoryRestService
/business_analytics/v1/mapping_categories,PRIVATE,no,no,"GET-/actions,GET-/dataSpec,GET-/permissions,GET-/uiSpec",com.kronos.kpifrm.builder.business.api.rest.services.MappingCategoryUIRestService
/business_analytics/v1/mapping_categories/sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,PUT-/{key}",com.kronos.kpifrm.builder.business.api.rest.sdm.MappingCategorySDMRestService
/business_analytics/v1/metadata/aggregation_types,PRIVATE,no,no,GET-/,com.kronos.kpifrm.builder.business.api.rest.services.AggregationTypeRestService
/business_analytics/v1/metadata/conditional_attributes,PRIVATE,no,no,GET-/,com.kronos.kpifrm.builder.business.api.rest.services.KPIConditionalAttributeRestService
/business_analytics/v1/metadata/conditional_operators,PRIVATE,no,no,GET-/,com.kronos.kpifrm.builder.business.api.rest.services.ConditionalOperatorRestService
/business_analytics/v1/metadata/data_formats,PRIVATE,no,no,"GET-/actions,GET-/dataSpec,GET-/permissions,GET-/uiSpec",com.kronos.kpifrm.builder.business.api.rest.services.DisplayFormatTypeUIRestService
/business_analytics/v1/metadata/data_formats,PRIVATE,no,no,GET-/,com.kronos.kpifrm.builder.business.api.rest.services.DisplayFormatTypeRestService
/business_analytics/v1/metadata/datasource,PRIVATE,no,no,GET-/,com.kronos.kpifrm.builder.business.api.rest.services.DataSourceRestService
/business_analytics/v1/metadata/domain_functions,PRIVATE,no,no,"GET-/,GET-/actions,GET-/dataSpec,GET-/permissions,GET-/uiSpec,GET-/{id}",com.kronos.kpifrm.builder.business.api.rest.services.DomainFunctionRestService
/business_analytics/v1/metadata/function_types,PRIVATE,no,no,GET-/,com.kronos.kpifrm.builder.business.api.rest.services.FunctionTypeRestService
/business_analytics/v1/metadata/mapping_categories_types,PRIVATE,no,no,"GET-/,GET-/attributes,GET-/{id}",com.kronos.kpifrm.builder.business.api.rest.services.MappingCategoryTypeRestService
/business_analytics/v1/metadata/mapping_categories_types,PRIVATE,no,no,"GET-/actions/{name},GET-/dataSpec/{name},GET-/permissions/{name},GET-/uiSpec/{name}",com.kronos.kpifrm.builder.business.api.rest.services.MappingCategoryTypeUIRestService
/business_analytics/v1/metrics,PRIVATE,no,no,"GET-/,GET-/{id},POST-/,POST-/get_metrics,POST-/{id}",com.kronos.kpifrm.builder.business.api.rest.services.MetricRestService
/business_analytics/v1/metrics,PRIVATE,no,no,"GET-/actions,GET-/dataSpec,GET-/permissions,GET-/uiSpec",com.kronos.kpifrm.builder.business.api.rest.services.MetricUIRestService
/business_analytics/v1/metrics/sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,PUT-/{key}",com.kronos.kpifrm.builder.business.api.rest.sdm.MetricSDMRestService
/calendar_sync,PUBLIC,no,no,GET-/{guid},com.kronos.scheduling.widget.employeecalendarsync.api.IESSCalendarSyncForProviderServiceRest
/calendar_sync_migrated,PUBLIC,yes,yes,GET-/{guid},com.kronos.scheduling.calendarsync.api.IESSCalendarSyncForProviderServiceRest
/cluster_computing_framework/v1/compute,PRIVATE,no,no,"GET-/get_status_by_engine_type,GET-/get_status_by_engine_type_and_date_range,GET-/get_status_by_request_id,POST-/submit_request",com.kronos.ccfrm.compute.proxy.impl.rest.ClusterComputingRestService
/commonbusiness/v1/schedule_period,PRIVATE,no,no,GET-/,com.kronos.commonbusiness.scheduleperiod.restservice.SchedulePeriodRestService
/commonbusiness/v1/symbolic_time,PRIVATE,no,no,"GET-/current_date,GET-/current_date_time,GET-/current_week,GET-/days_span_date,GET-/days_span_date_time,GET-/last_n_days,GET-/last_week,GET-/n_days_from_next_day,GET-/n_days_from_yesterday,GET-/next_n_days,GET-/next_week,GET-/today,GET-/tomorrow,GET-/week_to_date,GET-/yesterday",com.kronos.commonbusiness.timezone.api.restservice.SymbolicTimePeriodRestService
/commonbusiness/v1/timezone,PRIVATE,no,no,GET-/,com.kronos.commonbusiness.timezone.api.restservice.TimeZoneRestService
/config_app/v1/hfq_minimal,PRIVATE,no,no,"GET-,GET-/{id}",com.kronos.configapp.commonbusiness.peopleinfo.restservice.HFQRestService
/eventmanager/tenantProvision,PRIVATE,no,no,"DELETE-,POST-",com.kronos.wfc.commonapp.eventmanager.framework.restservices.api.EvtMgrProvisionRestService
/forecasting/v2/volume/features,PRIVATE,no,no,POST-/multi_read,com.kronos.forecasting.volume.feature.business.rest.FeatureDataServiceForRestV2
/legacy_api/v1/APIDefaultNotifications,PRIVATE,no,no,"GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},GET-/retrieve/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestAPIDefaultNotifications
/legacy_api/v1/APIHolidayProfile,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Update",com.kronos.wfc.rest.services.api.IRestAPIHolidayProfile
/legacy_api/v1/APIIVRCallMenu,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestAPIIVRCallMenu
/legacy_api/v1/APIIVRCommHub,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestAPIIVRCommHub
/legacy_api/v1/APIIVRExtensionGroup,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteFromExtensionList/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateExtensionList/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestAPIIVRExtensionGroup
/legacy_api/v1/APIIVRPolicyBean,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestAPIIVRPolicyBean
/legacy_api/v1/APIIVRProfileBean,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestAPIIVRProfileBean
/legacy_api/v1/APIIVRRestrictionProfile,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestAPIIVRRestrictionProfile
/legacy_api/v1/APITaskRunnerBean,PRIVATE,no,no,PUT-/Add/{taskID},com.kronos.wfc.rest.services.api.IRestAPITaskRunnerBean
/legacy_api/v1/AccessProfile,PRIVATE,no,no,"GET-/IsPermitted,GET-/Load,GET-/LoadAllAccessControlPointNames,GET-/LoadAllActionNames,GET-/LoadAllSuiteProductNames,GET-/LoadAllowedPermissions",com.kronos.wfc.rest.services.api.IRestAccessProfile
/legacy_api/v1/AdHocLaborAccount,PRIVATE,no,no,POST-/CreateAdHoc,com.kronos.wfc.rest.services.api.IRestAdHocLaborAccount
/legacy_api/v1/ApiEventManagerNEW,PRIVATE,no,no,"DELETE-/Delete,GET-/Info,POST-/Insert,PUT-/Disable/{taskId},PUT-/Enable/{taskId},PUT-/Update",com.kronos.wfc.rest.services.api.IRestApiEventManagerNEW
/legacy_api/v1/ApprovalStatus,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestApprovalStatus
/legacy_api/v1/AuditTrail,PRIVATE,no,no,"GET-/Retrieve,GET-/RetrieveAuditTrail",com.kronos.wfc.rest.services.api.IRestAuditTrail
/legacy_api/v1/AuthenticationType,PRIVATE,no,no,GET-/RetrieveAll,com.kronos.wfc.rest.services.api.IRestAuthenticationType
/legacy_api/v1/AvailPatternTemplate,PRIVATE,no,no,"DELETE-/Delete/{availabilityPatternName},GET-/Load/{availabilityPatternName},GET-/LoadAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{availabilityPatternName},POST-/Update",com.kronos.wfc.rest.services.api.IRestAvailPatternTemplate
/legacy_api/v1/AvgPayrateSet,PRIVATE,no,no,"DELETE-/Delete/{categoryName},GET-/Retrieve/{categoryName},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{categoryName},GET-/RetrieveForUpdateFactory/{categoryName},POST-/AddOnly,PUT-/Update/{categoryName}",com.kronos.wfc.rest.services.api.IRestAvgPayrateSet
/legacy_api/v1/BadgeAssignment,PRIVATE,no,no,POST-/LoadBadgeAssignments,com.kronos.wfc.rest.services.api.IRestBadgeAssignment
/legacy_api/v1/BaseWageRate,PRIVATE,no,no,POST-/LoadBaseWageRateHistory,com.kronos.wfc.rest.services.api.IRestBaseWageRate
/legacy_api/v1/BatchActionSet,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestBatchActionSet
/legacy_api/v1/BatchController,PRIVATE,no,no,"DELETE-/Delete,GET-/Retrieve,GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{instanceKey : .+},PUT-/Update",com.kronos.wfc.rest.services.api.IRestBatchController
/legacy_api/v1/BatchEvent,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/RemoveTask/{taskName},GET-/GetTasks,GET-/Load/{name},GET-/LoadAll,POST-/AddOnly,POST-/AddTask,POST-/Add_Only,PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestBatchEvent
/legacy_api/v1/BatchEventTask,PRIVATE,no,no,"DELETE-/Delete,GET-/Load,POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestBatchEventTask
/legacy_api/v1/BatchPerformancePurgeEvent,PRIVATE,no,no,DELETE-/PurgePerformanceRecords,com.kronos.wfc.rest.services.api.IRestBatchPerformancePurgeEvent
/legacy_api/v1/BatchRequestByEvent,PRIVATE,no,no,"DELETE-/Delete,GET-/load,POST-/AddOnly,PUT-/GenerateBatchRequest,PUT-/RunNow,PUT-/Update",com.kronos.wfc.rest.services.api.IRestBatchRequestByEvent
/legacy_api/v1/BatchRequestStatus,PRIVATE,no,no,POST-/Load,com.kronos.wfc.rest.services.api.IRestBatchRequestStatus
/legacy_api/v1/BatchService,PRIVATE,no,no,"POST-/handleRestCalls,POST-/{ApiName}/dependencies,POST-/{ApiName}/{ActionName}",com.kronos.wfc.rest.services.impl.RestAPIBatchRequest
/legacy_api/v1/BatchTask,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/RemoveItem,GET-/Load/{name},GET-/LoadAll,POST-/AddItem,POST-/AddItems,POST-/AddOnly,PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestBatchTask
/legacy_api/v1/BudgetEngineOptionAssignment,PRIVATE,no,no,"DELETE-/Delete/{forecastCategory},GET-/Retrieve/{forecastCategory},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{forecastCategory},POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestBudgetEngineOptionAssignment
/legacy_api/v1/BudgetingVolumeDriverRelationSetting,PRIVATE,no,no,"GET-/Retrieve/{volumeDriverRelationSettingName},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{volumeDriverRelationSettingName},PUT-/Update",com.kronos.wfc.rest.services.api.IRestBudgetingVolumeDriverRelationSetting
/legacy_api/v1/CalendarProfileAPIFacade,PRIVATE,no,no,"GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestCalendarProfileAPIFacade
/legacy_api/v1/Certification,PRIVATE,no,no,"DELETE-/Delete,GET-/Load/{certificationElementName},GET-/LoadAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestCertification
/legacy_api/v1/ColumnSet,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveAllShallow,GET-/RetrieveForUpdate/{name},GET-/RetrieveForUpdateFactory/{name},POST-/Update",com.kronos.wfc.rest.services.api.IRestColumnSet
/legacy_api/v1/CombinedEventDef,PRIVATE,no,no,,com.kronos.wfc.rest.services.impl.RestCombinedEvent
/legacy_api/v1/CombinedLaborDistribution,PRIVATE,no,no,"DELETE-/Delete,GET-/Load,GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate,POST-/AddOnly,POST-/Update",com.kronos.wfc.rest.services.api.IRestCombinedLaborDistribution
/legacy_api/v1/Comment,PRIVATE,no,no,"GET-/LoadActiveComments,GET-/LoadCommentCategories",com.kronos.wfc.rest.services.api.IRestComment
/legacy_api/v1/ContextCreator,PRIVATE,no,no,"GET-/MakeContext/{queryString},GET-/RetrieveContext/{queryString}",com.kronos.wfc.rest.services.api.IRestContextCreator
/legacy_api/v1/CoverageSettings,PRIVATE,no,no,"DELETE-/Delete/{settingsName},DELETE-/DeleteOnly/{settingsName},GET-/Retrieve/{settingsName},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{settingsName},POST-/Add,POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestCoverageSettings
/legacy_api/v1/CurrentUser,PRIVATE,no,no,GET-/LoadIdentities,com.kronos.wfc.rest.services.api.IRestCurrentUser
/legacy_api/v1/CustomKPI,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllInternalUseOnly,GET-/RetrieveAllNames,GET-/RetrieveAllShallow,GET-/RetrieveForUpdate/{name},GET-/RetrieveForUpdateFactory/{name},POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestCustomKPI
/legacy_api/v1/DBViewData,PRIVATE,no,no,GET-/Retrieve/{databaseViewName},com.kronos.wfc.rest.services.api.IRestDBViewData
/legacy_api/v1/DataAccessGroup,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteAllGDAPItem/{name},GET-/GetGDAPItemTypeList/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddGDAPItem,POST-/AddOnly,POST-/DeleteGDAPItem,PUT-/Duplicate/{name},PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestDataAccessGroup
/legacy_api/v1/DataElementAPIFacade,PRIVATE,no,no,"GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestDataElementAPIFacade
/legacy_api/v1/DataSetAPIFacade,PRIVATE,no,no,"GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestDataSetAPIFacade
/legacy_api/v1/DatabaseStatus,PRIVATE,no,no,"GET-/GetCurrentDbStatus,GET-/GetLatestSchemaReconciliationReportDate,POST-/RunSchemaReconciliationReport",com.kronos.wfc.rest.services.api.IRestDatabaseStatus
/legacy_api/v1/DayTypeEquivalenceValues,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestDayTypeEquivalenceValues
/legacy_api/v1/DayTypeRule,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestDayTypeRule
/legacy_api/v1/DayTypeRuleSet,PRIVATE,no,no,"DELETE-/DeleteOnly/{name},DELETE-/delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestDayTypeRuleSet
/legacy_api/v1/DefaultBatchRequest,PRIVATE,no,no,"GET-/Load,POST-/AddOnly,POST-/Delete,PUT-/GenerateBatchRequest,PUT-/Update",com.kronos.wfc.rest.services.api.IRestDefaultBatchRequest
/legacy_api/v1/Delegate,PRIVATE,no,no,"GET-/GetDelegatesByPersonNumber,GET-/GetDelegatesWithMismatchedPriorities",com.kronos.wfc.rest.services.api.IRestDelegate
/legacy_api/v1/DelegateAuthorityProxyTask,PRIVATE,no,no,GET-/RetrieveByProxyMgrPersonNumber,com.kronos.wfc.rest.services.api.IRestDelegateAuthorityProxyTask
/legacy_api/v1/DelegateAuthorityTask,PRIVATE,no,no,"DELETE-/MarkDelete/{taskId},GET-/RetrieveByPersonNumber,GET-/RetrieveByProxyMgrPersonNumber,POST-/AddOnly,POST-/AddOnlyV2,PUT-/MarkAccept/{taskId},PUT-/MarkCancel/{taskId},PUT-/MarkReject/{taskId}",com.kronos.wfc.rest.services.api.IRestDelegateAuthorityTask
/legacy_api/v1/DelegateProfile,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteEntryList/{name},GET-/Retrieve/{name},GET-/RetrieveAll,POST-/AddOnly,POST-/Duplicate,PUT-/UpdateEntryList/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestDelegateProfile
/legacy_api/v1/DelegationProxySummary,PRIVATE,no,no,GET-/RetrieveByDelegatee,com.kronos.wfc.rest.services.api.IRestDelegationProxySummary
/legacy_api/v1/DelegatorKBO,PRIVATE,no,no,GET-/RetrieveDelegators,com.kronos.wfc.rest.services.api.IRestDelegatorKBO
/legacy_api/v1/DeviceGroup,PRIVATE,no,no,"GET-/Load/{deviceGroupName},GET-/LoadAllDeviceGroups,GET-/LoadById/{deviceGroupId}",com.kronos.wfc.rest.services.api.IRestDeviceGroup
/legacy_api/v1/Document,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestDocument
/legacy_api/v1/DocumentStatus,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestDocumentStatus
/legacy_api/v1/DomainEventNotification,PRIVATE,no,no,"GET-/RetrieveAllNames,GET-/RetrieveForUpdate,PUT-/Update",com.kronos.wfc.rest.services.api.IRestDomainEventNotification
/legacy_api/v1/DynamicSchedulerOptionSet,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/addOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestDynamicSchedulerOptionSet
/legacy_api/v1/EligibilityRequirement,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestEligibilityRequirement
/legacy_api/v1/EmployeeGroupProfile,PRIVATE,no,no,"GET-/Load/{employeeGroupProfileName},GET-/LoadAllEmployeeGroupProfiles,GET-/LoadById/{employeeGroupProfileId}",com.kronos.wfc.rest.services.api.IRestEmployeeGroupProfile
/legacy_api/v1/EmployeeMessage,PRIVATE,no,no,"DELETE-/Delete,GET-/LoadAllMessages,POST-/Load,POST-/Send",com.kronos.wfc.rest.services.api.IRestEmployeeMessage
/legacy_api/v1/EmployeeRuleSet,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Update,PUT-/SetActivation",com.kronos.wfc.rest.services.api.IRestEmployeeRuleSet
/legacy_api/v1/EmploymentStatus,PRIVATE,no,no,POST-/LoadEmploymentStatuses,com.kronos.wfc.rest.services.api.IRestEmploymentStatus
/legacy_api/v1/EngOptionSetDap,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestEngOptionSetDap
/legacy_api/v1/FailedXMLTransaction,PRIVATE,no,no,"DELETE-/Delete,GET-/Load,POST-/AddOnly",com.kronos.wfc.rest.services.api.IRestFailedXMLTransaction
/legacy_api/v1/FiscalCalendar,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/Update",com.kronos.wfc.rest.services.api.IRestFiscalCalendar
/legacy_api/v1/FiscalCalendarAssignment,PRIVATE,no,no,"DELETE-/Delete,GET-/Retrieve/{forecastCategory},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{forecastCategory},POST-/Update",com.kronos.wfc.rest.services.api.IRestFiscalCalendarAssignment
/legacy_api/v1/ForecastCategory,PRIVATE,no,no,"DELETE-/Delete/{forecastCategoryPath},GET-/Load/{forecastCategoryPath},GET-/LoadAll,GET-/LoadDescendantSites/{forecastCategoryPath},GET-/LoadDescendants,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{forecastCategoryPath},POST-/AddOnly,POST-/Copy,POST-/Move,POST-/Update",com.kronos.wfc.rest.services.api.IRestForecastCategory
/legacy_api/v1/ForecastCategoryCurrencyAssignment,PRIVATE,no,no,"GET-/Retrieve/{forecastCategoryPath},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{categoryPathWithDates},PUT-/Update",com.kronos.wfc.rest.services.api.IRestForecastCategoryCurrencyAssignment
/legacy_api/v1/ForecastCategoryProperty,PRIVATE,no,no,"DELETE-/Delete/{forecastCategoryPropertyName},GET-/Load/{forecastCategoryPropertyName},GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{forecastCategoryPropertyName},POST-/AddOnly,POST-/Update",com.kronos.wfc.rest.services.api.IRestForecastCategoryProperty
/legacy_api/v1/ForecastCategoryPropertyAssignment,PRIVATE,no,no,"DELETE-/Delete,GET-/Load,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{forecastCategoryPath : .+},POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestForecastCategoryPropertyAssignment
/legacy_api/v1/ForecastEvent,PRIVATE,no,no,"DELETE-/Delete,GET-/Load,GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate,POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestForecastEvent
/legacy_api/v1/ForecastEventType,PRIVATE,no,no,"DELETE-/Delete/{eventTypeName},GET-/Load/{eventTypeName},GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{eventTypeName},POST-/AddOnly,PUT-/Update/{eventTypeName}",com.kronos.wfc.rest.services.api.IRestForecastEventType
/legacy_api/v1/ForecastMapDap,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestForecastMapDap
/legacy_api/v1/FunctionAccessProfile,PRIVATE,no,no,"GET-/Load/{functionAccessProfileName},GET-/LoadAllFunctionAccessProfiles,GET-/LoadById/{functionAccessProfileId}",com.kronos.wfc.rest.services.api.IRestFunctionAccessProfile
/legacy_api/v1/GDAPAssignment,PRIVATE,no,no,"DELETE-/Delete,GET-/Load,GET-/RetrieveByGDAPName/{gDAPName},GET-/RetrieveByPersonId,GET-/RetrieveByPersonIdAndRole,POST-/AddOnly,PUT-/ExpireNow,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestGDAPAssignment
/legacy_api/v1/GDAPAssignmentDefaultSwitch,PRIVATE,no,no,PUT-/Update,com.kronos.wfc.rest.services.api.IRestGDAPAssignmentDefaultSwitch
/legacy_api/v1/GenericEmployeeCalendarAPIFacade,PRIVATE,no,no,"GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestGenericEmployeeCalendarAPIFacade
/legacy_api/v1/GenericManagerCalendarAPIFacade,PRIVATE,no,no,"GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name}",com.kronos.wfc.rest.services.api.IRestGenericManagerCalendarAPIFacade
/legacy_api/v1/GenericNotification,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/LoadAllGenericNotifications,GET-/LoadById/{id},GET-/RetrieveAllForUpdate/{name},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestGenericNotification
/legacy_api/v1/GenericNotificationTrigger,PRIVATE,no,no,POST-/Trigger,com.kronos.wfc.rest.services.api.IRestGenericNotificationTrigger
/legacy_api/v1/HalfDayPCEBoundary,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestHalfDayPCEBoundary
/legacy_api/v1/HalfDayRule,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestHalfDayRule
/legacy_api/v1/HalfDayShiftBoundary,PRIVATE,no,no,"DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,POST-/Delete,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestHalfDayShiftBoundary
/legacy_api/v1/HeartBeat,PRIVATE,no,no,GET-/RetrieveResponseTime/{retrieveFor},com.kronos.wfc.rest.services.api.IRestHeartBeat
/legacy_api/v1/HoursCategory,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestHoursCategory
/legacy_api/v1/HoursCategorySet,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestHoursCategorySet
/legacy_api/v1/HoursOfOperation,PRIVATE,no,no,"DELETE-/Delete,GET-/Load,GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestHoursOfOperation
/legacy_api/v1/HoursOfOperationAssignment,PRIVATE,no,no,"DELETE-/Delete,GET-/Load/{categoryPath},GET-/LoadAll/{categoryPath},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{categoryPath},POST-/AddOnly,PUT-/Update/{categoryPath}",com.kronos.wfc.rest.services.api.IRestHoursOfOperationAssignment
/legacy_api/v1/HyperFindProfile,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Duplicate/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/UpdateEntryList,POST-/UpdateOnly,PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestHyperFindProfile
/legacy_api/v1/HyperFindQuery,PRIVATE,no,no,"GET-/GetQueryCount,GET-/LoadAllQueries/{visibilityCode},GET-/RunQuery",com.kronos.wfc.rest.services.api.IRestHyperFindQuery
/legacy_api/v1/HyperFindQueryImport,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestHyperFindQueryImport
/legacy_api/v1/ImportBatchStatus,PRIVATE,no,no,"GET-/LoadAllBatchNames,POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestImportBatchStatus
/legacy_api/v1/Indicator,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestIndicator
/legacy_api/v1/JobAssignment,PRIVATE,no,no,POST-/Load,com.kronos.wfc.rest.services.api.IRestJobAssignment
/legacy_api/v1/JobGroup,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestJobGroup
/legacy_api/v1/KTimeZone,PRIVATE,no,no,"GET-/Load/{kTimeZoneName},GET-/LoadAllKTimeZones,GET-/LoadById/{kTimeZoneId},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate,PUT-/Update",com.kronos.wfc.rest.services.api.IRestKTimeZone
/legacy_api/v1/LaborAccount,PRIVATE,no,no,"GET-/Load,GET-/UpdateAndLoad/{laborAccountName},PUT-/Update/{laborAccountName}",com.kronos.wfc.rest.services.api.IRestLaborAccount
/legacy_api/v1/LaborAccountSet,PRIVATE,no,no,"GET-/Load/{laborAccountSetName},GET-/LoadLaborAccountData/{laborAccountSetKey},GET-/LoadLaborAccountUpdatedData,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{laborAccountSetName},POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly/{laborAccountSetName}",com.kronos.wfc.rest.services.api.IRestLaborAccountSet
/legacy_api/v1/LaborAccountSetProfile,PRIVATE,no,no,"GET-/Load/{laborAccountSetProfileName},GET-/LoadAllLaborAccountSetProfiles,GET-/LoadById/{laborAccountSetProfileID}",com.kronos.wfc.rest.services.api.IRestLaborAccountSetProfile
/legacy_api/v1/LaborForecastLimit,PRIVATE,no,no,"DELETE-/Delete,GET-/Load/{name},GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestLaborForecastLimit
/legacy_api/v1/LaborLevelDefinition,PRIVATE,no,no,"GET-/GetNumberOfLaborLevels,GET-/LoadAllDefinitions",com.kronos.wfc.rest.services.api.IRestLaborLevelDefinition
/legacy_api/v1/LaborLevelEntry,PRIVATE,no,no,"GET-/GetCountByLaborLevelWithFilter,GET-/GetCountsByLaborLevel,GET-/Load,GET-/LoadByWildcard,POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestLaborLevelEntry
/legacy_api/v1/LaborStandard,PRIVATE,no,no,"DELETE-/Delete,GET-/Load,GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate,POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestLaborStandard
/legacy_api/v1/LeaveCategory,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestLeaveCategory
/legacy_api/v1/LeaveDocumentTemplate,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestLeaveDocumentTemplate
/legacy_api/v1/LeaveProfile,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestLeaveProfile
/legacy_api/v1/LeaveReason,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveByCategory/{category},GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestLeaveReason
/legacy_api/v1/LeaveRule,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestLeaveRule
/legacy_api/v1/LeaveType,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestLeaveType
/legacy_api/v1/LicenseType,PRIVATE,no,no,"GET-/Load/{licenseTypeName},GET-/LoadAssignable",com.kronos.wfc.rest.services.api.IRestLicenseType
/legacy_api/v1/LocalePolicy,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestLocalePolicy
/legacy_api/v1/LocationProfileSettings,PRIVATE,no,no,"DELETE-/Delete/{settingsName},DELETE-/DeleteOnly/{settingsName},GET-/Retrieve/{settingsName},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{settingsName},POST-/Add,POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestLocationProfileSettings
/legacy_api/v1/LocationRuleSet,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/TerminateAssignment,POST-/Update,PUT-/SetActivation",com.kronos.wfc.rest.services.api.IRestLocationRuleSet
/legacy_api/v1/LocationRuleSetAssignments,PRIVATE,no,no,"GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{orgRuleSetName},POST-/Delete,POST-/Update",com.kronos.wfc.rest.services.api.IRestRuleSetLocationAssign
/legacy_api/v1/LogonProfile,PRIVATE,no,no,"GET-/Load/{logonProfileName},GET-/LoadAllLogonProfiles,GET-/LoadById/{logonProfileId}",com.kronos.wfc.rest.services.api.IRestLogonProfile
/legacy_api/v1/LostTimeEventDef,PRIVATE,no,no,,com.kronos.wfc.rest.services.impl.RestLostTimeEventDef
/legacy_api/v1/Menu,PRIVATE,no,no,GET-/Load,com.kronos.wfc.rest.services.api.IRestMenu
/legacy_api/v1/MessagingService,PRIVATE,no,no,GET-/RetrieveTrustStore/{tsPassword},com.kronos.wfc.rest.services.api.IRestMessagingService
/legacy_api/v1/MetricsSettings,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestMetricsSettings
/legacy_api/v1/MinorRule,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Addonly,POST-/Update",com.kronos.wfc.rest.services.api.IRestMinorRule
/legacy_api/v1/NavProfile,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},GET-/RetrieveForUpdateFactory/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestNavProfile
/legacy_api/v1/NotificationMessageService,PRIVATE,no,no,"GET-/RetrieveByMessageId,GET-/RetrieveByPersonId,GET-/RetrieveByPersonNumber,GET-/RetrieveByUserName,GET-/RetrieveMessageUUID,PUT-/DismissMessageByPersonId,PUT-/DismissMessageByPersonNumber,PUT-/DismissMessageByUserName",com.kronos.wfc.rest.services.api.IRestNotificationMessageService
/legacy_api/v1/NotificationProfile,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/LoadAllNotificationProfiles,GET-/LoadById/{id},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestNotificationProfile
/legacy_api/v1/OrgDirect_IndirectPct,PRIVATE,no,no,GET-/load,com.kronos.wfc.rest.services.api.IRestOrgDirect_InDirectPct
/legacy_api/v1/OrgLocationsImport,PRIVATE,no,no,"POST-/Retrieve,PUT-/Update",com.kronos.wfc.rest.services.api.IRestOrgLocationsImport
/legacy_api/v1/OrgMapIntegration,PRIVATE,no,no,"GET-/LoadJobLocations,GET-/RetrieveAll/{kdate},GET-/RetrievePartial",com.kronos.wfc.rest.services.api.IRestOrgMapIntegration
/legacy_api/v1/OrgNodeCopier,PRIVATE,no,no,"GET-/DisplayStatus/{jobId},POST-/Copy,PUT-/Stop/{jobId}",com.kronos.wfc.rest.services.api.IRestOrgNodeCopier
/legacy_api/v1/OrgNodeMove,PRIVATE,no,no,"GET-/DisplayStatus/{jobId},PUT-/Move/{jobId},PUT-/stop/{jobId}",com.kronos.wfc.rest.services.api.IRestOrgNodeMove
/legacy_api/v1/OrgNodeProvisionSettings,PRIVATE,no,no,"GET-/Retrieve,GET-/RetrieveAllNames,GET-/RetrieveForUpdate,PUT-/Update",com.kronos.wfc.rest.services.api.IRestOrgNodeProvisionSettings
/legacy_api/v1/OrgSet,PRIVATE,no,no,"POST-/AddOnly,POST-/Load,POST-/Update,POST-/UpdateEntryList,PUT-/UpdateOnly/{orgSetName}",com.kronos.wfc.rest.services.api.IRestOrgSet
/legacy_api/v1/OverrideHoursOfOperation,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOverrideItems/{name},GET-/Load/{name},GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestOverrideHoursOfOperation
/legacy_api/v1/Pattern,PRIVATE,no,no,,com.kronos.wfc.rest.services.impl.RestPattern
/legacy_api/v1/PayCodeValuesProfile,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestPayCodeValuesProfile
/legacy_api/v1/PerfectAttendanceDef,PRIVATE,no,no,,com.kronos.wfc.rest.services.impl.RestPerfectAttendanceDef
/legacy_api/v1/PersonAccessAssignment,PRIVATE,no,no,POST-/LoadPersonAccessAssignments,com.kronos.wfc.rest.services.api.IRestPersonAccessAssignment
/legacy_api/v1/PersonCommonId,PRIVATE,no,no,"DELETE-/Delete,POST-/Update",com.kronos.wfc.rest.services.api.IRestPersonCommonId
/legacy_api/v1/PersonIdentity,PRIVATE,no,no,GET-/LoadIdentities,com.kronos.wfc.rest.services.api.IRestPersonIdentity
/legacy_api/v1/PersonInformation,PRIVATE,no,no,"GET-/Load,GET-/LoadAllBadgeAssignments,GET-/LoadAllEmploymentStatus,GET-/LoadAllHomeAccounts,GET-/LoadAllUserAccountStatus",com.kronos.wfc.rest.services.api.IRestPersonInformation
/legacy_api/v1/Personality,PRIVATE,no,no,"DELETE-/Delete/{personNumber},DELETE-/DeleteOnly/{personNumber},GET-/Load,POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.commonapp.people.api.personality.RestAPIPersonalityBean
/legacy_api/v1/PointType,PRIVATE,no,no,,com.kronos.wfc.rest.services.impl.RestPointType
/legacy_api/v1/PolicyAction,PRIVATE,no,no,,com.kronos.wfc.rest.services.impl.RestPolicyAction
/legacy_api/v1/PreferenceProfile,PRIVATE,no,no,"GET-/Load/{preferenceProfileName},GET-/LoadAllPreferenceProfiles,GET-/LoadById/{preferenceProfileId}",com.kronos.wfc.rest.services.api.IRestPreferenceProfile
/legacy_api/v1/PrimaryLaborAccount,PRIVATE,no,no,POST-/LoadPrimaryLaborAccounts,com.kronos.wfc.rest.services.api.IRestPrimaryLaborAccount
/legacy_api/v1/Procedure,PRIVATE,no,no,"DELETE-/Delete/{procedureName},DELETE-/DeleteOnly/{procedureName},GET-/Retrieve/{procedureName},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{procedureName},POST-/Add,POST-/AddOnly,PUT-/Update/{procedureName},PUT-/UpdateOnly/{procedureName}",com.kronos.wfc.rest.services.api.IRestProcedure
/legacy_api/v1/ProcedureSet,PRIVATE,no,no,"DELETE-/Delete/{procedureSetName},DELETE-/DeleteOnly/{procedureSetName},GET-/Retrieve/{procedureSetName},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{procedureSetName},POST-/Add,POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestProcedureSet
/legacy_api/v1/Process,PRIVATE,no,no,"DELETE-/DeleteOnly/{processId},GET-/Retrieve/{processId},POST-/Add,POST-/AddWithWebForm,PUT-/UpdateOnly/{processId}",com.kronos.wfc.rest.services.api.IRestProcess
/legacy_api/v1/ProcessTask,PRIVATE,no,no,"GET-/Retrieve/{taskId},GET-/RetrieveAllByProcessId/{processId},GET-/RetrieveAllByResourceId/{resourceId},PUT-/UpdateOnly/{taskId}",com.kronos.wfc.rest.services.api.IRestProcessTask
/legacy_api/v1/ProcessTemplate,PRIVATE,no,no,"DELETE-/DeleteOnly/{adminTemplateId},GET-/Retrieve/{adminTemplateId},GET-/RetrieveAll,GET-/RetrieveAllByCategory,GET-/RetrieveAllByResourceId,GET-/RetrieveAllForEmployee,GET-/RetrieveAllForManager,GET-/RetrieveByDisplayName/{displayName},GET-/RetrieveByUniqueKey/{kProcessUniqueKey},POST-/Add,POST-/AddBusinessProcess,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestProcessTemplate
/legacy_api/v1/ProficiencyLevel,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestProficiencyLevel
/legacy_api/v1/RequestApprovalSettings,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestRequestApprovalSettings
/legacy_api/v1/RequestApprovalStepSettings,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestRequestApprovalStepSettings
/legacy_api/v1/RequestReviewerList,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestRequestReviewerList
/legacy_api/v1/RequestReviewerPurpose,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestRequestReviewerPurpose
/legacy_api/v1/RequestSubtype,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestRequestSubtype
/legacy_api/v1/RoleProfileKBO,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},GET-/RetrieveRoleProfiles,POST-/AddOnly,PUT-/Duplicate/{name},PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestRoleProfileKBO
/legacy_api/v1/SchedGenSettings,PRIVATE,no,no,"DELETE-/Delete/{settingsName},DELETE-/DeleteOnly/{settingsName},GET-/Retrieve/{settingsName},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{settingsName},POST-/Add,POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestSchedGenSettings
/legacy_api/v1/SchedRule,PRIVATE,no,no,"DELETE-/Delete/{ruleName},DELETE-/DeleteOnly/{ruleName},GET-/Retrieve/{ruleName},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{ruleName},POST-/Add,POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestSchedRule
/legacy_api/v1/SchedRuleSet,PRIVATE,no,no,"DELETE-/Delete/{ruleSetName},DELETE-/DeleteOnly/{ruleSetname},GET-/Retrieve/{ruleSetName},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{ruleSetName},GET-/RetrievesAllForUpdate,POST-/Add,POST-/AddOnly,PUT-/Update/{ruleSetName},PUT-/UpdateOnly/{ruleSetName}",com.kronos.wfc.rest.services.api.IRestSchedRuleSet
/legacy_api/v1/ScheduleAssistantCriteria,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/SetActivation,PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestScheduleAssistantCriteria
/legacy_api/v1/ScheduleAssistantCriteriaSet,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Update",com.kronos.wfc.rest.services.api.IRestScheduleAssistantCriteriaSet
/legacy_api/v1/SchedulePattern,PRIVATE,no,no,"DELETE-/Delete/{schedulePatternName},GET-/Load/{schedulePatternName},GET-/LoadAllPatterns,GET-/LoadTemplatePatterns,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{schedulePatternName},POST-/AddPatternElements,POST-/Update",com.kronos.wfc.rest.services.api.IRestSchedulePattern
/legacy_api/v1/SchedulePeriod,PRIVATE,no,no,"DELETE-/Delete/{schedulePeriodName},GET-/Load/{schedulePeriodName},GET-/LoadAllPeriods,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{schedulePeriodName},PUT-/Update",com.kronos.wfc.rest.services.api.IRestSchedulePeriod
/legacy_api/v1/ScheduleTemplate,PRIVATE,no,no,"DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/AddRows,POST-/Update,PUT-/RetractRollout,PUT-/RollOut",com.kronos.wfc.rest.services.api.IRestScheduleTemplate
/legacy_api/v1/ScheduleZoneSet,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/LoadByWildCard,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestScheduleZoneSet
/legacy_api/v1/SchoolCalendar,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/RolloutCalendar,PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestSchoolCalendar
/legacy_api/v1/SeasonalPattern,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/Update",com.kronos.wfc.rest.services.api.IRestSeasonalPattern
/legacy_api/v1/SecurityAlert,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForInsertOrUpdate/{name},GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestSecurityAlert
/legacy_api/v1/SegmentDetails,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Update",com.kronos.wfc.rest.services.api.IRestSegmentDetails
/legacy_api/v1/ServerTime,PRIVATE,no,no,GET-/GetServerTime,com.kronos.wfc.rest.services.api.IRestServerTime
/legacy_api/v1/ShiftCode,PRIVATE,no,no,"DELETE-/Delete/{shiftCodeName},GET-/Load/{shiftCodeName},GET-/LoadAllShiftCodes,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{shiftCodeName},POST-/Update",com.kronos.wfc.rest.services.api.IRestShiftCode
/legacy_api/v1/ShiftProfile,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestShiftProfile
/legacy_api/v1/ShiftProfileSet,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/Update",com.kronos.wfc.rest.services.api.IRestShiftProfileSet
/legacy_api/v1/ShiftSetAssignment,PRIVATE,no,no,"GET-/Load,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{shiftSetName},POST-/TerminateAssignment,POST-/Update",com.kronos.wfc.rest.services.api.IRestShiftSetAssignment
/legacy_api/v1/Signature,PRIVATE,no,no,"DELETE-/Delete/{signatureName},DELETE-/DeleteOnly/{signatureName},GET-/Retrieve/{signatureName},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{signatureName},POST-/Add,POST-/AddOnly,PUT-/Update/{signatureName},PUT-/UpdateOnly/{signatureName}",com.kronos.wfc.rest.services.api.IRestSignature
/legacy_api/v1/Skill,PRIVATE,no,no,"GET-/LoadAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/Delete,POST-/Load,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestSkill
/legacy_api/v1/SkillCertificationProfile,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestSkillCertificationProfile
/legacy_api/v1/SocialProvisioner,PRIVATE,no,no,GET-/ProvisionSocialEntities/{socialProvisioner},com.kronos.wfc.rest.services.api.IRestSocialProvisioner
/legacy_api/v1/StandardShiftSet,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/LoadByWildcard,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name},PUT-/UpdatePartial/{name}",com.kronos.wfc.rest.services.api.IRestStandardShiftSet
/legacy_api/v1/StaticDriver,PRIVATE,no,no,"DELETE-/Delete/{staticDriverName},GET-/Load,GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{staticDriverName},POST-/AddOnly,PUT-/Update/{staticDriverName}",com.kronos.wfc.rest.services.api.IRestStaticDriver
/legacy_api/v1/SuiteProduct,PRIVATE,no,no,"GET-/LoadAll,GET-/LoadByName/{suiteProductName}",com.kronos.wfc.rest.services.api.IRestSuiteProduct
/legacy_api/v1/SwitchRoleEngine,PRIVATE,no,no,"PUT-/SwitchBackToMyself,PUT-/SwitchRole",com.kronos.wfc.rest.services.api.IRestSwitchRoleEngine
/legacy_api/v1/SystemSettings,PRIVATE,no,no,GET-/Load,com.kronos.wfc.rest.services.api.IRestSystemSettings
/legacy_api/v1/Task,PRIVATE,no,no,"DELETE-/Delete,GET-/Load,GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate,POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestTask
/legacy_api/v1/TaskActionType,PRIVATE,no,no,GET-/Load_All,com.kronos.wfc.rest.services.api.IRestTaskActionType
/legacy_api/v1/TaskGroup,PRIVATE,no,no,"DELETE-/UnassignJobs,GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate,POST-/AddOnly,PUT-/Update,PUT-/UpdateJobs",com.kronos.wfc.rest.services.api.IRestTaskGroup
/legacy_api/v1/TimeEntry,PRIVATE,no,no,"GET-/Load/{timeEntryName},GET-/LoadAllTimeEntries,GET-/LoadById/{timeEntryID}",com.kronos.wfc.rest.services.api.IRestTimeEntry
/legacy_api/v1/TimeFramePeriod,PRIVATE,no,no,"GET-/LoadAllTimeFrames,GET-/LoadDateRange",com.kronos.wfc.rest.services.api.IRestTimeFramePeriod
/legacy_api/v1/TokeRule,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/Update",com.kronos.wfc.rest.services.api.IRestTokeRule
/legacy_api/v1/TransitionAssignment,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Update",com.kronos.wfc.rest.services.api.IRestTransitionAssignment
/legacy_api/v1/TreeNodeType,PRIVATE,no,no,"DELETE-/delete/{name},GET-/RetrieveAllNames,GET-/checkForReferences/{name},GET-/retrieveAllForUpdate,GET-/retrieveForUpdate/{name},POST-/addOnly,PUT-/update/{name}",com.kronos.wfc.rest.services.api.IRestTreeNodeType
/legacy_api/v1/User,PRIVATE,no,no,"GET-/Load,GET-/LoadUserAccount,POST-/devices/reset",com.kronos.wfc.rest.services.api.IRestUser
/legacy_api/v1/UserAccountStatus,PRIVATE,no,no,POST-/LoadUserAccountStatuses,com.kronos.wfc.rest.services.api.IRestUserAccountStatus
/legacy_api/v1/UserDefinedDriver,PRIVATE,no,no,"DELETE-/Delete,GET-/Load,GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{encodedName},POST-/AddOnly,PUT-/Update",com.kronos.wfc.rest.services.api.IRestUserDefinedDriver
/legacy_api/v1/ValidLaborAccount,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestValidLaborAccount
/legacy_api/v1/VolumeDriver,PRIVATE,no,no,"DELETE-/Delete/{volumeDriverName},GET-/Load/{volumeDriverName},GET-/LoadAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{volumeDriverName},POST-/AddOnly,PUT-/Update/{volumeDriverName}",com.kronos.wfc.rest.services.api.IRestVolumeDriver
/legacy_api/v1/WATPolicy,PRIVATE,no,no,"DELETE-/delete/{name},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},GET-/load/{name},POST-/addOnly,PUT-/update/{name},PUT-/updateOnly/{name}",com.kronos.wfc.rest.services.api.IRestWATPolicy
/legacy_api/v1/WATProfile,PRIVATE,no,no,"GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWatProfile
/legacy_api/v1/WFCGenie,PRIVATE,no,no,"GET-/LoadAllGenies,GET-/LoadMetadata/{genieName},POST-/Load",com.kronos.wfc.rest.services.api.IRestWFCGenie
/legacy_api/v1/WFLCustomData,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestWFLCustomData
/legacy_api/v1/WLGAcuitySet,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWLGAcuitySet
/legacy_api/v1/WLGStaffingMatrix,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWLGStaffingMatrix
/legacy_api/v1/WSAAvailabilityPatternDAP,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAAvailabilityPatternDAP
/legacy_api/v1/WSACfgAccrualCode,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSACfgAccrualCode
/legacy_api/v1/WSACfgContactTypeList,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate,PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSACfgContactTypeList
/legacy_api/v1/WSACfgCustomDataList,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate,PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSACfgCustomDataList
/legacy_api/v1/WSAComment,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{text},PUT-/Update/{text}",com.kronos.wfc.rest.services.api.IRestWSAComment
/legacy_api/v1/WSACustomDate,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSACustomDate
/legacy_api/v1/WSACustomUrl,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{displayName},PUT-/Update/{displayName}",com.kronos.wfc.rest.services.api.IRestWSACustomUrl
/legacy_api/v1/WSACustomUrlProfile,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSACustomUrlProfile
/legacy_api/v1/WSADetail,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSADetail
/legacy_api/v1/WSADetailColumnSet,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSADetailColumnSet
/legacy_api/v1/WSADisplayProfile,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update",com.kronos.wfc.rest.services.api.IRestWSADisplayProfile
/legacy_api/v1/WSAFunctionAccessProfile,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAFunctionAccessProfile
/legacy_api/v1/WSAGrant,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAGrant
/legacy_api/v1/WSAIsrProfile,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Update",com.kronos.wfc.rest.services.api.IRestWSAIsrProfile
/legacy_api/v1/WSALaborLevel,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{encodedName},PUT-/Update/{encodedName}",com.kronos.wfc.rest.services.api.IRestWSALaborLevel
/legacy_api/v1/WSALaborLevelEntry,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{encodedName},PUT-/Update/{encodedName}",com.kronos.wfc.rest.services.api.IRestWSALaborLevelEntry
/legacy_api/v1/WSALeaveCaseDetail,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSALeaveCaseDetail
/legacy_api/v1/WSALeaveCaseDetailColumnSet,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSALeaveCaseDetailColumnSet
/legacy_api/v1/WSALimit,PRIVATE,no,no,"DELETE-/Delete,GET-/Retrieve,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSALimit
/legacy_api/v1/WSALogonProfile,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSALogonProfile
/legacy_api/v1/WSAMajorityRule,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAMajorityRule
/legacy_api/v1/WSAPatternTemplateDAP,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAPatternTemplateDAP
/legacy_api/v1/WSAProbationPeriod,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Delete/{name},POST-/Update",com.kronos.wfc.rest.services.api.IRestWSAProbationPeriod
/legacy_api/v1/WSAQuickFind,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAQuickFind
/legacy_api/v1/WSAScheduleAssistant,PRIVATE,no,no,"GET-/Retrieve,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAScheduleAssistant
/legacy_api/v1/WSAScheduleAssistantColumnSet,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAScheduleAssistantColumnSet
/legacy_api/v1/WSAScheduleEditor,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Update",com.kronos.wfc.rest.services.api.IRestWSAScheduleEditor
/legacy_api/v1/WSAScheduleEditorColumnSet,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAScheduleEditorColumnSet
/legacy_api/v1/WSAScheduleGroupDAP,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAScheduleGroupDAP
/legacy_api/v1/WSAScheduleGroupDetail,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAScheduleGroupDetail
/legacy_api/v1/WSAScheduleGroupDetailColumnSet,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAScheduleGroupDetailColumnSet
/legacy_api/v1/WSAScheduleGroupProfileItem,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name}",com.kronos.wfc.rest.services.api.IRestWSAScheduleGroupProfileItem
/legacy_api/v1/WSAScheduleGroupRollup,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAScheduleGroupRollup
/legacy_api/v1/WSAScheduleGroupRollupColumnSet,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAScheduleGroupRollupColumnSet
/legacy_api/v1/WSASchedulePlanner,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,POST-/RetrieveForUpdate,POST-/Update",com.kronos.wfc.rest.services.api.IRestWSASchedulePlanner
/legacy_api/v1/WSASchedulePlannerColumnSet,PRIVATE,no,no,"GET-/Retrieve/{name},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSASchedulePlannerColumnSet
/legacy_api/v1/WSAScheduledHoursType,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAScheduledHoursType
/legacy_api/v1/WSAShiftTemplateDAP,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAShiftTemplateDAP
/legacy_api/v1/WSAULLE,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAULLE
/legacy_api/v1/WSAWorkedAccountRollupColumnSet,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAWorkedAccountRollupColumnSet
/legacy_api/v1/WSAWorkforceGenieProfile,PRIVATE,no,no,"GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWSAWorkforceGenieProfile
/legacy_api/v1/WTDGlobal,PRIVATE,no,no,"DELETE-/Delete/{id},DELETE-/DeleteOnly/{id},GET-/Retrieve/{id},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update,PUT-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestWTDGlobal
/legacy_api/v1/WTDRule,PRIVATE,no,no,"DELETE-/Delete/{ruleId},DELETE-/DeleteOnly/{ruleId},GET-/Retrieve/{ruleId},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update/{ruleId},PUT-/UpdateOnly/{ruleId}",com.kronos.wfc.rest.services.api.IRestWTDRule
/legacy_api/v1/WTKEmployee,PRIVATE,no,no,"GET-/GetPayPeriodDates,GET-/Load",com.kronos.wfc.rest.services.api.IRestWTKEmployee
/legacy_api/v1/WageProfile,PRIVATE,no,no,"GET-/Load/{wageProfileName},GET-/LoadAllWageProfiles,GET-/LoadById/{wageProfileID}",com.kronos.wfc.rest.services.api.IRestWageProfile
/legacy_api/v1/WatAttendanceEvent,PRIVATE,no,no,,com.kronos.wfc.rest.services.impl.RestAttendanceEvent
/legacy_api/v1/WatDiscipline,PRIVATE,no,no,,com.kronos.wfc.rest.services.impl.RestWatDiscipline
/legacy_api/v1/WatTrackingPrd,PRIVATE,no,no,,com.kronos.wfc.rest.services.impl.RestWatTrackingPrd
/legacy_api/v1/WebServiceConfiguration,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/Update",com.kronos.wfc.rest.services.api.IRestWebServiceConfiguration
/legacy_api/v1/WeekDay,PRIVATE,no,no,"GET-/Load,GET-/LoadAll",com.kronos.wfc.rest.services.api.IRestWeekDay
/legacy_api/v1/WfcJob,PRIVATE,no,no,"DELETE-/Delete/{wfcJobName},GET-/LoadByName/{wfcJobName},POST-/AddOnly,PUT-/UpdateOnly/{wfcJobName}",com.kronos.wfc.rest.services.api.IRestWfcJob
/legacy_api/v1/WorkRuleProfile,PRIVATE,no,no,"GET-/Load/{workruleProfileName},GET-/LoadAllWorkRuleProfiles,GET-/LoadById/{workruleProfileID}",com.kronos.wfc.rest.services.api.IRestWorkRuleProfile
/legacy_api/v1/WorkSet,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWorkSet
/legacy_api/v1/WorkSetType,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWorkSetType
/legacy_api/v1/WorkWeek,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/GetOutsideDates,GET-/Retrieve/{name},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},PUT-/Update/{name}",com.kronos.wfc.rest.services.api.IRestWorkWeek
/legacy_api/v1/WorkWeekAssignments,PRIVATE,no,no,"DELETE-/Delete,GET-/Retrieve/{workWeekName},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{workWeekName},PUT-/Update",com.kronos.wfc.rest.services.api.IRestWorkWeekAssignments
/legacy_api/v1/WorkerType,PRIVATE,no,no,"DELETE-/Delete/{workerTypeElement},GET-/Load/{workerTypeElement},GET-/LoadAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,POST-/Update,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestWorkerType
/legacy_api/v1/WorkflowAccessAssignment,PRIVATE,no,no,"GET-/Load,POST-/UpdateOnly",com.kronos.wfc.rest.services.api.IRestWorkflowAccessAssignment
/legacy_api/v1/WorkflowTemplateCategory,PRIVATE,no,no,GET-/RetrieveAllCategories,com.kronos.wfc.rest.services.api.IRestWorkflowTemplateCategory
/legacy_api/v1/WorkloadPlanner,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/LoadByWildCard,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestWorkloadPlanner
/legacy_api/v1/WorkloadPlannerProfile,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/Load/{name},GET-/LoadAll,GET-/LoadByWildcard/{wildcard},GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestWorkloadPlannerProfile
/legacy_api/v1/XmppAuthentication,PRIVATE,no,no,GET-/Validate,com.kronos.wfc.rest.services.api.IRestXmppAuthentication
/legacy_api/v1/ZoneCategory,PRIVATE,no,no,"DELETE-/Delete/{name},DELETE-/DeleteOnly/{name},GET-/Retrieve/{name},GET-/RetrieveAll,GET-/RetrieveAllForUpdate,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{name},POST-/Add,POST-/AddOnly,PUT-/Update/{name},PUT-/UpdateOnly/{name}",com.kronos.wfc.rest.services.api.IRestZoneCategory
/legacy_api/v1/ZoneSetAssignment,PRIVATE,no,no,"GET-/Load,GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{zoneSetName},POST-/Update,PUT-/TerminateAssignment",com.kronos.wfc.rest.services.api.IRestZoneSetAssignment
/legacy_api/v1/schedulegroup,PRIVATE,no,no,"DELETE-/Delete/{name},GET-/RetrieveAllNames,GET-/RetrieveForUpdate/{scheduleGroupName},POST-/Update",com.kronos.wfc.rest.services.api.IRestScheduleGroup
/organization/v1/job_node,PRIVATE,no,no,GET-/{node_id},com.kronos.commonapp.orgmap.setup.impl.rest.WfcJobOrgService
/organization/v1/nodes,PRIVATE,no,no,"GET-/{context},GET-/{context}/generic_job,GET-/{context}/node_type,GET-/{context}/root_nodes,GET-/{context}/search,GET-/{context}/{node_id},GET-/{context}/{node_id}/ancestors,GET-/{context}/{node_id}/children,GET-/{context}/{node_id}/descendants,GET-/{context}/{node_id}/effective,GET-/{context}/{node_id}/generic_job,GET-/{context}/{node_id}/node_type,GET-/{context}/{node_id}/parent,POST-/{context}/multi_ascendants_by_location_category_type,POST-/{context}/multi_cost_center,POST-/{context}/multi_descendants_by_location_category_type,POST-/{context}/multi_descendants_by_types,POST-/{context}/multi_generic_job,POST-/{context}/multi_node_type",com.kronos.commonapp.orgmap.traversal.impl.rest.OrgMapService
/timekeeping/v1,PRIVATE,no,no,GET-/comments,com.kronos.timekeeping.service.commentsetup.impl.CommentServiceForRest
/timekeeping/v1/audits,PRIVATE,no,no,POST-/multi_read,com.kronos.timekeeping.service.audit.impl.rest.AuditServiceForRest
/timekeeping/v1/audits/after_signoff,PRIVATE,no,no,POST-/multi_read,com.kronos.timekeeping.service.audit.impl.rest.AfterSignOffAuditServiceForRest
/v1/analytics/raw_volume,PRIVATE,no,no,POST-/multi_upsert,com.kronos.analytics.rawvolume.rest.IRawVolumeForRestV1
/v1/apiVersioning,PRIVATE,no,no,"-/data/{key},DELETE-/{id},GET-/get/{param},GET-/get/{pp1}/{pp2}/{pp3},GET-/get/{pp1}/{pp2}/{pp3}$$api.1.1,GET-/test/{id},GET-/test1/{id},GET-/test2/{id},GET-/test3/{id},GET-/test4/{id},HEAD-/{id},POST-/create,POST-/createIds/{param1}/{param2},PUT-/update/{id}",com.kronos.versioning.refapi.service.IRestApiVersioning
/v1/attendance,PUBLIC,yes,yes,POST-/profiles_assignments/multi_read,com.kronos.attendance.service.api.rest.profile.ProfileRestService
/v1/attendance/actions,PUBLIC,yes,yes,"GET-/{id},GET-/{id}/documents,POST-/complete,POST-/documents/multi_read,POST-/multi_read,POST-/{id}",com.kronos.attendance.service.api.rest.action.ActionTransactionRestService
/v1/attendance/attendance_profiles,PUBLIC,yes,yes,"GET-/,GET-/{id},POST-/multi_read",com.kronos.attendance.service.api.rest.attendanceprofile.AttendanceProfileRest
/v1/attendance/audit_records,PUBLIC,yes,yes,POST-/multi_read,com.kronos.attendance.service.audit.api.rest.AttendanceAuditRestService
/v1/attendance/balance_adjustments,PUBLIC,yes,yes,"POST-/,POST-/multi_read,POST-/multi_update,POST-/{id}/mark_deleted",com.kronos.attendance.service.api.rest.balance.BalanceAdjustmentRestService
/v1/attendance/balance_expirations,PUBLIC,yes,yes,POST-/multi_read,com.kronos.attendance.service.api.rest.balance.BalanceExpirationRestService
/v1/attendance/balance_resets,PUBLIC,yes,yes,"POST-/,POST-/multi_read,POST-/multi_update,POST-/{id}/mark_deleted",com.kronos.attendance.service.api.rest.balance.BalanceResetRestService
/v1/attendance/balances,PUBLIC,yes,yes,POST-/multi_read,com.kronos.attendance.service.api.rest.balance.PointBalanceRestService
/v1/attendance/calendars_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.attendance.service.api.rest.calendar.AttendanceCalendarSettingsSDMRestService
/v1/attendance/discipline_levels,PUBLIC,yes,yes,POST-/multi_read,com.kronos.attendance.service.api.rest.discipline.DisciplineLevelTransactionRestService
/v1/attendance/events,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/types,GET-/{type}/{id},POST-/multi_create,POST-/multi_read,POST-/{id},POST-/{id}/mark_deleted",com.kronos.attendance.service.api.rest.event.EventRestService
/v1/attendance/markers,PUBLIC,yes,yes,"POST-/multi_create,POST-/multi_delete,POST-/multi_read",com.kronos.attendance.service.api.rest.empprocinfo.EmployeeProcInfoRestService
/v1/attendance/perfect_attendance,PUBLIC,yes,yes,POST-/multi_read,com.kronos.attendance.service.api.rest.perfectattendance.PerfectAttendanceRestService
/v1/attendance/perfect_attendance_deductions,PUBLIC,yes,yes,POST-/multi_read,com.kronos.attendance.service.api.rest.perfectattendance.PerfectAttendanceDeductionRestService
/v1/attendance/processor_requests,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.attendance.service.api.rest.procrequest.ProcessorRequestRestService
/v1/attendance/processor_requests_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.attendance.service.api.rest.procrequest.ProcessorRequestSDMRestService
/v1/attendance/rules,PUBLIC,yes,yes,POST-/execute,com.kronos.attendance.service.api.rest.rule.RuleRestService
/v1/audit/audititem,PRIVATE,no,no,"GET-/auditrecords,POST-/auditrecord,POST-/auditrecords",com.kronos.auditing.api.rest.services.audititem.AuditItemRestService
/v1/auditreport/records,PRIVATE,no,no,POST-,com.kronos.auditreport.api.rest.service.AuditingReportRestService
/v1/auth/connect,PUBLIC,yes,yes,GET-,com.kronos.auth.oidc.authentication.api.rest.OIDCAuthenticationRestProvider
/v1/auth/logout,PUBLIC,yes,yes,POST-/,com.kronos.auth.logout.api.rest.LogoutRestProvider
/v1/branding/presets,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/active,GET-/logo/{version},GET-/version,GET-/wallpaper/{version},GET-/{id},POST-,PUT-/active/{id},PUT-/{id}",com.kronos.branding.rest.PresetRestService
/v1/branding/presets/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.branding.rest.sdm.SdmPresetRest
/v1/common/setup_items,PRIVATE,no,no,"GET-/{setup_item_id},GET-/{setup_item_id}/dependents",com.kronos.sdm.api.rest.services.setupitem.SetupItemRestService
/v1/commonbiz/action,PRIVATE,no,no,"DELETE-/cancel/{handle},POST-/execute_at",com.kronos.commonbusiness.action.impl.rest.ActionServiceForRest
/v1/commonbiz/action,PRIVATE,no,no,POST-/execute,com.kronos.commonbusiness.action.impl.rest.InternalActionServiceForRest
/v1/commons,PUBLIC,yes,yes,POST-/payroll/lock/apply_update,com.kronos.timekeeping.payrollmanagement.api.rest.IPayrollLockRest
/v1/commons/access_control_points,PUBLIC,yes,yes,GET-,com.kronos.fap.api.rest.ACPRestService
/v1/commons/access_method_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/access_methods,GET-/role_profiles,GET-/{id},POST-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.kronos.commonbusiness.accessmethodprofile.setup.api.rest.AMPublicRestAPI
/v1/commons/access_method_profiles/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/{dependencies},PUT-/{key}",com.kronos.commonbusiness.accessmethodprofile.setup.api.rest.AMPSDMRest
/v1/commons/average_pay_rate_sets,PUBLIC,yes,yes,"DELETE-/{id},GET-,POST-,POST-/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_update,PUT-/{id}",com.kronos.commonbusiness.avgpayrate.rest.AveragePayRateSetServiceForRestV1
/v1/commons/average_pay_rates,PUBLIC,yes,yes,POST-/apply_read,com.kronos.commonbusiness.avgpayrate.rest.AveragePayRateServiceForRestV1
/v1/commons/biometric_consent_history,PUBLIC,yes,yes,POST-/multi_read,com.kronos.datacollection.udm.api.BiometricConsentHistoryService
/v1/commons/business_structure/SDM,PRIVATE,no,no,"GET-/generic_jobs,GET-/generic_jobs/keys,GET-/location_types,GET-/location_types/keys,GET-/locations/import_status/{executionKey},POST-/locations/dependencies,POST-/locations/export,POST-/locations/import",com.kronos.commonapp.orgmap.rest.impl.OrgMapSDMService
/v1/commons/comments,PUBLIC,yes,yes,"GET-/,GET-/employee_comments",com.kronos.commonbusiness.setup.comments.restservice.CommentRestService
/v1/commons/control_center_profile,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/categories,GET-/controlcenterProfiles,GET-/metadata,GET-/using_category_count,GET-/{id},POST-,PUT-/{id}",com.kronos.controlcenter.api.restservice.profile.ControlCenterProfileRestService
/v1/commons/control_center_profiles,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.controlcenter.api.restservice.profile.ControlCenterProfRestService
/v1/commons/cost_centers,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update",com.kronos.commonapp.laborcategory.setup.impl.rest.CostCenterServiceForRest
/v1/commons/currency/conversions,PUBLIC,yes,yes,POST-/apply_read,com.kronos.commonbusiness.exchangerate.api.rest.ExchangeRateRestService
/v1/commons/currency/definitions,PUBLIC,yes,yes,"GET-,GET-/{id}",com.kronos.commonbusiness.currencypolicy.api.rest.CurrencyDefinitionRestService
/v1/commons/currency/policies,PUBLIC,yes,yes,"GET-,GET-/base_currency,GET-/{id},POST-,POST-/apply_update,POST-/multi_read,POST-/multi_upsert",com.kronos.commonbusiness.currencypolicy.api.rest.CurrencyPolicyRestService
/v1/commons/currency_assignments,PRIVATE,no,no,"GET-/employees/{id},GET-/locations/{id},GET-/logged_in_user,POST-/employees/multi_read,POST-/locations/multi_read",com.kronos.commonbusiness.exchangerate.api.rest.CurrencyAssignmentRestService
/v1/commons/currency_policies/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.commonbusiness.currencypolicy.api.rest.SDMCurrencyPolicyRestService
/v1/commons/custom_tiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_read,PUT-/{id}",com.kronos.commonapp.customtile.rest.CustomTileRestService
/v1/commons/data,PUBLIC,yes,yes,POST-/multi_read,com.kronos.ia.informationaccess.api.rest.DataController
/v1/commons/data_dictionary,PUBLIC,yes,yes,"GET-/data_elements,GET-/location_types,GET-/metadata,GET-/relative_time_periods,GET-/time_increments,POST-/data_elements/multi_read,POST-/data_elements/multi_upsert",com.kronos.ia.datadictionary.rest.DataDictionaryController
/v1/commons/dataview_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_read,PUT-/{id}",com.kronos.ia.dataview.rest.DataViewProfileController
/v1/commons/dataview_profiles/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.ia.dataview.rest.sdm.SdmDataViewProfileRest
/v1/commons/dataviews,PUBLIC,yes,yes,"DELETE-/{id},DELETE-/{id}/personalization,GET-,GET-/{id},POST-,POST-/multi_read,POST-/{id}/personalization,PUT-/{id},PUT-/{id}/personalization",com.kronos.ia.dataview.rest.DataViewController
/v1/commons/dataviews/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.ia.dataview.rest.sdm.SdmDataViewRest
/v1/commons/delegate_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/async,GET-/async/{executionKey}/response,GET-/async/{executionKey}/status,GET-/{id},POST-,POST-/apply_upsert,POST-/apply_upsert/async,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,POST-/multi_upsert/async,PUT-/{id}",com.kronos.delegateprofile.api.restservice.DelegateProfilesRestService
/v1/commons/device_groups,PUBLIC,yes,yes,GET-,com.kronos.datacollection.udm.api.DeviceGroupsService
/v1/commons/display_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.configapp.setup.displayprofile.impl.rest.DisplayProfileServiceRestV1
/v1/commons/dsaas,PRIVATE,no,no,"DELETE-/apps/auditor/results/{id},GET-/apps/auditor/results,POST-/apps/auditor/results,POST-/apps/auditor/results/{id},POST-/apps/auditor/results/{id}/by_employee,POST-/apps/auditor/results/{id}/by_location,POST-/apps/auditor/results/{id}/by_pattern",com.kronos.commonbusiness.dsaas.apps.auditor.metrics.ingestion.impl.rest.AuditorIngestionForRest
/v1/commons/employee_glance_settings,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.kronos.people.glance.rest.EmployeeGlanceSettingsServiceForRestV1
/v1/commons/employee_glance_settings/setup,PUBLIC,yes,yes,GET-/custom_fields,com.kronos.people.glance.rest.EmployeeGlanceSettingsResourceServiceForRestV1
/v1/commons/employee_glance_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.people.glance.rest.EmployeeGlanceSettingsSDMServiceForRestV1
/v1/commons/employee_groups,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_create,POST-/multi_read,POST-/multi_update",com.kronos.commonapp.employeegroup.setup.impl.rest.EmployeeGroupServiceForRest
/v1/commons/employee_transfers,PUBLIC,yes,yes,POST-/apply_read,com.kronos.commonapp.transfer.aggregation.api.IAggregatedTransferRestService
/v1/commons/entity_definitions,PUBLIC,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_read,POST-/multi_upsert,PUT-/multi_update,PUT-/{id}",com.kronos.ia.datadictionary.rest.EntityDefinitionRestService
/v1/commons/event_categories,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.kronos.unifiedalerts.api.restservice.DomainEventCategoryRest
/v1/commons/event_navigations,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.kronos.unifiedalerts.api.restservice.DomainEventNavigationRest
/v1/commons/event_notifications,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.kronos.genericnotification.api.rest.DomainEventNotificationMappingRestService
/v1/commons/event_notifications,PUBLIC,yes,yes,POST-/notify,com.kronos.controlcenter.api.restservice.notifications.ExternalNotificationEventRestService
/v1/commons/event_type_sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-,PUT-/{key}",com.kronos.genericnotification.api.rest.sdm.DomainEventForSDMRestService
/v1/commons/event_types,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.kronos.genericnotification.api.rest.DomainEventRestService
/v1/commons/events,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/domains,GET-/metadata,GET-/navigations,GET-/notifications,GET-/notifications/{id},GET-/permissions,GET-/types,GET-/{id},POST-,PUT-/{id}",com.kronos.controlcenter.api.restservice.events.EventConfigRestService
/v1/commons/exports,PUBLIC,yes,yes,"GET-/,GET-/{id}/file,GET-/{id}/status,POST-/async,POST-/multi_delete",com.kronos.commonbusiness.bulkdownload.impl.rest.DownloadRestService
/v1/commons/feature_switch,PUBLIC,yes,yes,"GET-/{name},POST-/multi_read,POST-/multi_update",com.kronos.releasetoggle.rest.FeatureSwitchRestAPI
/v1/commons/fiscal_calendars,PUBLIC,yes,yes,"POST-/import,POST-/multi_delete",com.kronos.commonapp.timezone.fiscalcalendar.rest.api.IFiscalCalendaForRestV1
/v1/commons/formatter,PRIVATE,no,no,POST-/,com.kronos.ia.formatter.api.rest.FormatterRestService
/v1/commons/function_access_profiles,PUBLIC,yes,yes,"DELETE-/{fapId},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.fap.api.rest.FAPRestService
/v1/commons/generic_data_access_profiles,PUBLIC,yes,yes,"GET-/setup/gdap_items,POST-/apply_upsert,POST-/multi_read",com.kronos.genericdataaccessprofiles.api.restservice.GenericDataAccessprofilesRestService
/v1/commons/generic_locations,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-/,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.ukg.genericlocations.services.setup.api.rest.IOrgGenericLocationRestService
/v1/commons/generic_notifications,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.kronos.genericnotification.api.rest.GenericNotificationRestService
/v1/commons/group_types,PRIVATE,no,no,GET-,com.kronos.commonapp.orgmap.rest.impl.OrgMapGroupTypeService
/v1/commons/home_pages,PUBLIC,yes,yes,"DELETE-/{id},DELETE-/{id}/personalization,GET-,GET-/{id},POST-,POST-/multi_read,POST-/{id}/personalization,PUT-/{id}",com.kronos.ia.dashboard.api.rest.HomeRestService
/v1/commons/homepages/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.ia.dashboard.api.rest.HomeSdmController
/v1/commons/hours_operation,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.commonbusiness.hoursofoperation.rest.HoursOfOperationServiceForRestV1
/v1/commons/hours_operation_assignments,PUBLIC,yes,yes,"GET-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert",com.kronos.commonbusiness.hoursofoperation.rest.HoursOfOperationAssignmentServiceForRestV1
/v1/commons/hours_operation_override,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.commonbusiness.hoursofoperation.rest.HoursOfOperationOverrideServiceForRestV1
/v1/commons/hours_operation_override_assignments,PUBLIC,yes,yes,"GET-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert",com.kronos.commonbusiness.hoursofoperation.rest.HoursOfOperationOverrideAssignmentServiceForRestV1
/v1/commons/hyperfind,PUBLIC,yes,yes,"GET-/,GET-/constraint,GET-/filters,GET-/personal_inactives,GET-/public,GET-/{id},POST-/execute,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,POST-/personal_inactives/multi_delete,POST-/positions/execute",com.kronos.commonapp.hyperfindquery.restservice.HyperFindExecutorRestService
/v1/commons/hyperfind_profiles,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.hyperfindprofiles.api.rest.HyperfindProfileRestService
/v1/commons/hyperfindquery,PRIVATE,no,no,"DELETE-/deleteHyperfindAssociation,DELETE-/{id},GET-/,GET-/getListOfPrivateHyperfind,GET-/getPrivateHyperfindCount,GET-/getPrivateQueryUserIdsByQueryId,GET-/public,GET-/{id},POST-/,POST-/assignHyperfind,POST-/executePositionAdapter,PUT-/{id}",com.kronos.commonapp.hyperfindquery.restservice.HyperfindQueryRestService
/v1/commons/integration_processes,PRIVATE,no,no,"POST-/async/status/{statusId},POST-/async/{processId},POST-/{processId}",com.kronos.hcm.integration.framework.service.rest.IntegrationRestService
/v1/commons/internal/generic_locations,PRIVATE,no,no,"GET-/count,GET-/generic_location_restore,GET-/generic_location_restore/{glid},GET-/org_node/count,GET-/sbs/migrationrefs,GET-/validate_suite_customer,POST-/resolve,POST-/sbs/resetrefs",com.ukg.genericlocations.services.setup.api.rest.internal.IOrgGenericLocationInternalRestService
/v1/commons/jobs,PUBLIC,yes,yes,"DELETE-,DELETE-/{jobId},GET-,GET-/external_ids/{externalId},GET-/{jobId},POST-,POST-/apply_read,POST-/external_ids/multi_read,POST-/multi_read,POST-/persistent_ids/multi_read,POST-/{jobId},PUT-/{jobId}",com.kronos.commonapp.orgmap.rest.impl.OrgMapJobsForRestV1
/v1/commons/known_ip_address,PRIVATE,no,no,"GET-,GET-/ipformats,POST-,POST-/bypass,POST-/bypass_users,POST-/flattenip",com.kronos.knownipaddress.api.rest.KnownIPAddressRest
/v1/commons/known_ip_address/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.knownipaddress.api.rest.SDMKnownIPRestService
/v1/commons/known_ip_addresses,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_upsert,PUT-/{id}",com.kronos.knownipaddress.api.rest.KnownIPPublicRestAPI
/v1/commons/known_places,PUBLIC,yes,yes,"DELETE-,DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-,PUT-/{id}",com.kronos.commonapp.knownplaces.impl.rest.KnownPlaceServiceForRest
/v1/commons/labor_categories,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_create,POST-/multi_delete,POST-/multi_update",com.kronos.commonapp.laborcategory.setup.impl.rest.LaborCategoryServiceForRest
/v1/commons/labor_category_list_assignments,PUBLIC,yes,yes,"POST-/multi_create,POST-/multi_delete,POST-/multi_delete/ids,POST-/multi_read,POST-/multi_update",com.kronos.commonapp.laborcategory.setup.impl.rest.LaborCategoryListAssignmentServiceForRest
/v1/commons/labor_category_lists,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_update,POST-/multi_upsert",com.kronos.commonapp.laborcategory.setup.impl.rest.LaborCategoryListServiceForRest
/v1/commons/labor_category_profiles,PUBLIC,yes,yes,"DELETE-,DELETE-/{id},GET-,GET-/current_user,GET-/{id},POST-/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/multi_upsert",com.kronos.commonapp.laborcategory.setup.impl.rest.LaborCategoryProfileServiceForRest
/v1/commons/labor_entries,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update",com.kronos.commonapp.laborcategory.setup.impl.rest.LaborCategoryEntryServiceForRest
/v1/commons/labor_entry_lists,PUBLIC,yes,yes,POST-/multi_read,com.kronos.commonapp.laborcategory.entrylist.impl.rest.LaborEntryListAccessServiceForRest
/v1/commons/labortype/sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,PUT-/{key}",com.kronos.commonapp.labortype.api.rest.sdm.LaborTypeSDMRestService
/v1/commons/locale_policies,PUBLIC,yes,yes,"DELETE-/multi_delete,DELETE-/{id},GET-/,GET-/{id},POST-,POST-/apply_update,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/{id},PUT-/apply_update,PUT-/multi_update,PUT-/{id}",com.kronos.commonapp.localepolicy.rest.impl.LocalePolicyForRestV1
/v1/commons/locale_policies/SDM,PRIVATE,no,no,"GET-/locale_policy,GET-/locale_policy/keys,POST-/locale_policy,PUT-/locale_policy/{key}",com.kronos.commonapp.localepolicy.rest.impl.LocalePolicySDMService
/v1/commons/location_attribute_assignments,PUBLIC,yes,yes,POST-/apply_read,com.kronos.commonapp.locationattributes.setup.rest.LocationAttributeAssignmentServiceForRestV1
/v1/commons/location_attributes,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.kronos.commonapp.locationattributes.setup.rest.LocationAttributeServiceForRestV1
/v1/commons/location_attributes/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.locationattributes.setup.rest.sdm.LocationAttributeSDMServiceForRestV1
/v1/commons/location_selector,PRIVATE,no,no,"GET-/search,GET-/user_roots,GET-/{id},GET-/{id}/ancestors,GET-/{id}/children,POST-/multi_ancestors,POST-/multi_children",com.kronos.commonapp.orgmap.traversal.impl.rest.OrgMapSelectorServiceSDM
/v1/commons/location_sets,PUBLIC,yes,yes,"GET-,GET-/{id},POST-,POST-/apply_upsert,POST-/multi_delete,POST-/multi_read,POST-/{groupId},PUT-/{groupId}",com.kronos.commonapp.orgmap.rest.impl.OrgMapLocationSetsForRestV1
/v1/commons/location_sets/SDM,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.orgmap.rest.impl.OrgMapGroupSDMService
/v1/commons/location_types,PUBLIC,yes,yes,"DELETE-,DELETE-/{id},GET-,GET-/external_ids/{externalId},GET-/{id},POST-,POST-/apply_read,POST-/external_ids/multi_read,POST-/multi_read,POST-/persistent_ids/multi_read,POST-/{id},PUT-/{id}",com.kronos.commonapp.orgmap.rest.impl.OrgMapLocationTypesForRestV1
/v1/commons/locations,PUBLIC,yes,yes,"DELETE-/multi_delete,GET-,GET-/external_ids/{external_id},GET-/status/async/{executionKey},GET-/{id},POST-,POST-/apply_create,POST-/apply_update,POST-/apply_update/async,POST-/external_ids/multi_read,POST-/multi_create,POST-/multi_create/async,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/multi_update/async,POST-/persistent_ids/multi_read,POST-/{id},PUT-/multi_update,PUT-/{id}",com.kronos.commonapp.orgmap.rest.impl.OrgMapLocationsForRestV1
/v1/commons/migration/internal,PRIVATE,no,no,"GET-/get_exception_users,POST-/add_exception_users,POST-/disable_bgp_access,POST-/disable_user_access,POST-/enable_bgp_access",com.ukg.genericlocations.services.reset.api.rest.internal.IPreMigrationUserAccessResetInternalService
/v1/commons/node_type_categories,PRIVATE,no,no,GET-,com.kronos.commonapp.orgmap.rest.impl.OrgNodeTypeCategoryQueryService
/v1/commons/notifications,PUBLIC,yes,yes,"GET-,GET-/config,GET-/count,GET-/item,GET-/{message_id},POST-/apply_read,POST-/multi_delete,POST-/multi_read,POST-/multi_review",com.kronos.controlcenter.api.restservice.notifications.NotificationRestService
/v1/commons/notifications/actions/execute,PUBLIC,yes,yes,POST-,com.kronos.inbox.action.api.rest.DomainExecuteActionsRestService
/v1/commons/org_mappings,PRIVATE,no,no,GET-/,com.ukg.genericlocations.services.sbssyncmappings.api.rest.IOrgSyncMappingRestService
/v1/commons/org_settings,PUBLIC,yes,yes,"DELETE-/{id},GET-/,POST-/,PUT-/{externalId}",com.ukg.genericlocations.services.sbssyncsettings.api.rest.IOrgSyncSettingsRestService
/v1/commons/pay_period,PUBLIC,yes,yes,GET-/,com.kronos.commonbusiness.payperiod.restservice.PayPeriodRestService
/v1/commons/payroll/staging,PUBLIC,yes,yes,"GET-/{requestId}/details,GET-/{requestId}/status,POST-/async",com.kronos.payroll.extract.staging.restservice.api.PayrollStagingRestService
/v1/commons/person_profiles,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.configapp.commonbusiness.peopleinfo.restservice.PeopleInfoProfileRestService
/v1/commons/persons,PUBLIC,yes,yes,"GET-/{person_id}/attestation_profile_assignments,POST-/{person_id}/attestation_profile_assignments",com.kronos.persons.rest.assignments.service.PersonAttestationProfileAssignmentRestService
/v1/commons/persons,PUBLIC,yes,yes,"DELETE-/{personId},GET-/current_user_delegations,GET-/current_user_info,GET-/extensions,GET-/punch_interpretation_rules,GET-/{extensionType},GET-/{personId},POST-,POST-/apply_read,POST-/base_persons/multi_read,POST-/extensions/multi_read,POST-/multi_create,POST-/multi_delete,POST-/multi_update,POST-/multi_upsert,POST-/refs/multi_read,PUT-/{personId}",com.kronos.persons.rest.service.IRestPersonService
/v1/commons/persons/adjustment_rule,PUBLIC,yes,yes,"DELETE-/,GET-/,GET-/multi_read,GET-/{personId},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/",com.kronos.persons.rest.assignments.service.IRestEmployeeAdjustmentRuleAssignment
/v1/commons/persons/attendance_admin,PUBLIC,yes,yes,"DELETE-/,GET-/,GET-/{personId},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/",com.kronos.persons.rest.assignments.service.IRestPersonAttendanceAdminAssignment
/v1/commons/persons/attendance_profile,PUBLIC,yes,yes,"GET-/,GET-/{personId},POST-/multi_read,POST-/multi_update,PUT-/",com.kronos.persons.rest.assignments.service.IRestPersonAttendanceProfile
/v1/commons/persons/attestation_profile_assignments,PUBLIC,yes,yes,"GET-,POST-,POST-/multi_read,POST-/multi_update",com.kronos.persons.rest.assignments.service.PersonNumberAttestationProfileAssignmentRestService
/v1/commons/persons/brazil_employee_assignments,PUBLIC,yes,yes,"GET-,GET-/{personId},POST-/multi_read,POST-/multi_upsert",com.kronos.persons.rest.assignments.service.IBrazilAssignmentRestService
/v1/commons/persons/cascade_profile,PUBLIC,yes,yes,"DELETE-/,GET-/,GET-/{personId},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/",com.kronos.persons.rest.assignments.service.IRestPersonCascadeProfileAssignment
/v1/commons/persons/certifications,PUBLIC,yes,yes,"GET-,GET-/{personId},POST-/apply_delete,POST-/multi_read,POST-/multi_upsert,PUT-/{personId}",com.kronos.scheduling.setup.skillcertification.impl.rest.assignment.PersonCertificationAssignmentServiceForRestV1
/v1/commons/persons/employee_tags,PUBLIC,yes,yes,"GET-,GET-/{person_id},POST-,POST-/apply_delete,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-",com.kronos.scheduling.setup.tagdefinition.rest.impl.EmployeeTagAssignmentsServiceForRestV1
/v1/commons/persons/employment_terms,PUBLIC,yes,yes,"GET-,GET-/{personId},POST-,POST-/apply_delete,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-",com.kronos.scheduling.business.employeegroupschedule.rest.impl.EmploymentTermsEmployeeAssignmentPeopleImportServiceForRestV1
/v1/commons/persons/extension_processor,PRIVATE,no,no,"DELETE-/,GET-/,GET-/{personId},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_update,PUT-/",com.kronos.persons.rest.assignments.service.IRestProcessorToEmployeeAssignment
/v1/commons/persons/external_id,PUBLIC,yes,yes,"DELETE-/,GET-/,GET-/{personId},POST-/apply_read,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-",com.kronos.persons.rest.assignments.service.IRestPersonCommonId
/v1/commons/persons/external_identifiers,PUBLIC,yes,yes,"GET-/{personId},POST-/multi_read,POST-/multi_upsert,PUT-",com.kronos.people.externalidentifier.rest.IRestPersonExternalIdAssignment
/v1/commons/persons/forecasting_category_profiles,PUBLIC,yes,yes,"DELETE-,GET-,GET-/{personKey},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-",com.kronos.forecasting.peoplecategoryprofile.impl.rest.CategoryProfilePersonAssignmentServiceForRestV1
/v1/commons/persons/insights,PRIVATE,no,no,GET-,com.kronos.commonbusiness.insights.business.impl.rest.EmployeeInsightsServiceForRestV1
/v1/commons/persons/job_preferences,PUBLIC,yes,yes,"GET-/,GET-/{personId},POST-/multi_read,POST-/multi_update,PUT-/",com.kronos.persons.rest.assignments.service.IRestEmployeeJobPreferences
/v1/commons/persons/leave_admin,PUBLIC,yes,yes,"DELETE-/,GET-/,GET-/{personId},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/",com.kronos.persons.rest.assignments.service.IRestPersonLeaveAdminAssignment
/v1/commons/persons/leave_profile,PUBLIC,yes,yes,"DELETE-/,GET-/,GET-/{personId},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/",com.kronos.persons.rest.assignments.service.IRestPersonLeaveProfileAssignment
/v1/commons/persons/manager_role_assignments,PUBLIC,yes,yes,"GET-/,GET-/{person_id},POST-/apply_upsert,POST-/multi_read,POST-/multi_update",com.kronos.commonbusiness.role.assignment.impl.rest.RoleAssignmentRestService
/v1/commons/persons/pattern_template_profiles,PUBLIC,yes,yes,"DELETE-/{personId},GET-,GET-/{personId},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{personId}",com.kronos.scheduling.setup.patterntemplate.impl.rest.PatternTemplateProfileAssignmentForRestV1
/v1/commons/persons/percentage_allocation_rules,PUBLIC,yes,yes,"DELETE-/,GET-/,GET-/multi_read,GET-/{personId},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/",com.kronos.persons.rest.assignments.service.IRestPercentageAllocationRuleAssignment
/v1/commons/persons/process_profiles,PUBLIC,yes,yes,"GET-/,GET-/{personId},POST-/multi_read,POST-/multi_update,PUT-/update",com.kronos.persons.rest.assignments.service.IRestPersonProcessProfileAssignment
/v1/commons/persons/profile_photos,PUBLIC,yes,yes,POST-/multi_read,com.kronos.persons.rest.service.IRestPersonEmployeePhotoService
/v1/commons/persons/schedule_group_profiles,PUBLIC,yes,yes,"DELETE-/{personId},GET-,GET-/{personId},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{personId}",com.kronos.scheduling.setup.schedulegroupprofile.impl.rest.ScheduleGroupProfileAssignmentForRestV1
/v1/commons/persons/schedule_groups,PUBLIC,yes,yes,"GET-,GET-/{personId},POST-,POST-/apply_delete,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-",com.kronos.scheduling.business.employeegroupschedule.rest.impl.ScheduleGroupEmployeeAssignmentPeopleImportServiceForRestV1
/v1/commons/persons/schedule_rule_sets,PUBLIC,yes,yes,"GET-,POST-/multi_read,POST-/multi_upsert,PUT-/{personId}",com.kronos.scheduling.setup.scheduleruleset.impl.rest.RuleSetEmployeeAssignmentsServiceForRestV1
/v1/commons/persons/scheduling_preferences,PRIVATE,no,no,"DELETE-,DELETE-/{personId},GET-,GET-/{personId},POST-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert",com.kronos.scheduling.business.employeepreferences.impl.rest.EmployeePreferenceManagerBusinessServiceForRestV1
/v1/commons/persons/school_calendar_profiles,PUBLIC,yes,yes,"DELETE-/{personId},GET-,GET-/{personId},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{personId}",com.kronos.scheduling.setup.schoolcalendarprofile.impl.rest.SchoolCalendarProfileAssignmentServiceForRestV1
/v1/commons/persons/shift_template_profiles,PUBLIC,yes,yes,"DELETE-/{personId},GET-,GET-/{personId},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{personId}",com.kronos.scheduling.setup.shifttemplateprofile.impl.rest.ShiftTemplateProfileAssignmentServiceForRestV1
/v1/commons/persons/skills,PUBLIC,yes,yes,"GET-,GET-/{personId},POST-/apply_delete,POST-/multi_read,POST-/multi_upsert,PUT-/{personId}",com.kronos.scheduling.setup.skillcertification.impl.rest.assignment.PersonSkillAssignmentServiceForRestV1
/v1/commons/persons/supervisor,PUBLIC,yes,yes,"GET-/{personId},POST-/multi_read,POST-/multi_upsert",com.kronos.persons.rest.assignments.service.IRestPersonAssignmentSupervisor
/v1/commons/persons/telestaff_assignments,PUBLIC,yes,yes,"DELETE-/{personId},GET-/,GET-/{personId},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{personId}",com.kronos.persons.wfts.rest.services.api.IPersonWftsAssignmentRestService
/v1/commons/persons/wage_work_rules,PUBLIC,yes,yes,"POST-/multi_read,POST-/multi_upsert",com.kronos.persons.rest.service.IEmployeeWageWorkRuleService
/v1/commons/pge/notifications,PRIVATE,no,no,POST-/notify,com.kronos.commonbusiness.dsaas.apps.pge.notification.api.rest.PgeNotificationForRest
/v1/commons/pge/setup/behaviors/configurations,PUBLIC,yes,yes,"GET-/,GET-/{id},PUT-/{id}",com.kronos.commonbusiness.dsaas.apps.pge.config.api.rest.PgeBehaviorConfigurationsForRest
/v1/commons/pge/setup/behaviors/configurations/items,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-/,PUT-/{id}",com.kronos.commonbusiness.dsaas.apps.pge.config.api.rest.PgeBehaviorConfigurationItemsForRest
/v1/commons/platform/event_actions,PRIVATE,no,no,"GET-,GET-/verify_link",com.kronos.controlcenter.api.restservice.notifications.NotificationEventRestService
/v1/commons/preupgrade-checklist,PUBLIC,no,no,GET-/status,com.kronos.wfc.platform.preupgradechecklist.rest.PreupgradeChecklistPublicRestApi
/v1/commons/pro_integration/people_monitoring,PUBLIC,yes,yes,GET-/count,com.ukg.peoplesync.monitoring.api.rest.ProIntegrationPeopleMonitoringPublicRest
/v1/commons/pro_integration/people_settings,PUBLIC,no,no,"-/{id},GET-,GET-/{id},POST-/,PUT-/{id}",com.ukg.peoplesync.config.api.rest.IProIntegrationPeopleConfigRest
/v1/commons/pro_integration/people_settings/sdm,PRIVATE,no,no,"GET-/,GET-/keys,PUT-/{key}",com.ukg.peoplesync.config.api.rest.IProIntegrationPeopleConfigSDMRest
/v1/commons/profiles/people_direct_assignments,PUBLIC,yes,yes,GET-,com.kronos.people.profile.business.rest.PeopleDirectAssignmentRestService
/v1/commons/profiles/people_profiles,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.people.profile.business.rest.PeopleProfileRestService
/v1/commons/profiles/people_profiles/assignments,PRIVATE,no,no,POST-/,com.kronos.people.profile.mapping.business.rest.PeopleProfileAssignmentRestService
/v1/commons/profiles/profile_field_mappings,PUBLIC,yes,yes,"GET-,POST-/multi_read,POST-/multi_upsert",com.kronos.people.profile.mapping.business.rest.ProfileTemplateMappingRestService
/v1/commons/profiles/profile_templates,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.people.profile.business.rest.ProfileTemplateRestService
/v1/commons/profiles/sdm/profile_field_mappings,PRIVATE,no,no,"GET-/{persistentId},GET-/{persistentId}/keys,POST-/{persistentId},PUT-/{persistentId}/{key}",com.kronos.people.profile.mapping.business.rest.ProfileTemplateMappingSDMRestService
/v1/commons/reviewer_lists,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/reviewers,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_update,PUT-/{id}",com.kronos.reviewers.api.restservice.ReviewerListRestService
/v1/commons/reviewer_lists/reviewers,PUBLIC,yes,yes,POST-/apply_read,com.kronos.genericrequest.multiapprover.impl.rest.ReviewersEvaluateServiceForRestV1
/v1/commons/reviewer_purpose,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.reviewers.api.restservice.ReviewerPurposeRestService
/v1/commons/role_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_delete,POST-/multi_upsert,PUT-/{id}",com.kronos.commonbusiness.accessmethodprofile.setup.api.rest.RoleProfileRestService
/v1/commons/sbs/internal/migration_reset,PRIVATE,no,no,"GET-/validate_wfm_or_adp_support_user,POST-/events/disable,POST-/events/enable,POST-/probackupstatus/,POST-/prorollbackstatus/,POST-/reset_status,POST-/schema/clone_schema",com.ukg.genericlocations.services.reset.api.rest.internal.IOrgMigrationRestInternalService
/v1/commons/sbs/jobs,PUBLIC,no,no,POST-,com.kronos.orgmap.sbs.api.rest.IBusinessStructureJobsRest
/v1/commons/sbs/locations,PUBLIC,no,no,"GET-/master_root_node,POST-/multi_create,POST-/multi_read,POST-/multi_update",com.kronos.orgmap.sbs.api.rest.IBusinessStructureLocationsRest
/v1/commons/sbs/migration_reset,PUBLIC,no,no,"PUT-/backup_status,PUT-/rollback_status,PUT-/status/{batchId}",com.ukg.genericlocations.services.migration.api.rest.IOrgMigrationRestService
/v1/commons/sbs_mappings,PRIVATE,no,no,GET-/,com.ukg.genericlocations.services.sbssyncmappings.api.rest.IOrgSbsSyncMappingRestService
/v1/commons/sbs_reset/internal,PRIVATE,no,no,"DELETE-/proStatusHardReset/{id},GET-/proStatusHardReset,POST-/hardReset,POST-/hardResetRefs,POST-/resetPro,POST-/softReset,POST-/stopSync",com.ukg.genericlocations.services.reset.api.rest.internal.ISBSResetInternalService
/v1/commons/sbs_settings,PUBLIC,no,no,"DELETE-/{id},GET-/,POST-/,PUT-/{externalId}",com.ukg.genericlocations.services.sbssyncsettings.api.rest.IOrgSbsSyncSettingsRestService
/v1/commons/sdm/entity_definitions,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.ia.datadictionary.rest.EntityDefinitionSdmRestService
/v1/commons/sdm/event_categories,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.unifiedalerts.api.restservice.DomainEventCategorySDMRest
/v1/commons/sdm/event_navigations,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.unifiedalerts.api.restservice.DomainEventNavigationSDMRest
/v1/commons/selected_locations,PRIVATE,no,no,"DELETE-/user/{id},GET-/user,GET-/user/{type_id},POST-/user,PUT-/user/{id}",com.kronos.commonapp.orgmap.transaction.impl.rest.SelectedLocationService
/v1/commons/setup/holidays,PUBLIC,yes,yes,"GET-/,POST-/multi_upsert,POST-/years/apply_upsert",com.ukg.timekeeping.setup.holiday.api.restservice.HolidaySetupRestService
/v1/commons/setup/timezones,PUBLIC,yes,yes,"GET-/,GET-/{id}",com.kronos.commonapp.timezone.rest.impl.TimeZoneSetupForRestV1
/v1/commons/start_day_weeks,PUBLIC,yes,yes,POST-/apply_read,com.kronos.commonapp.locationattributes.startdayofweek.rest.StartDayOfWeekServiceForRestV1
/v1/commons/symbolic_purposes,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.reviewers.api.restservice.SymbolicPurposeRestService
/v1/commons/symbolicperiod,PUBLIC,yes,yes,"GET-,GET-/period_types,GET-/{periodTypeId},POST-/multi_read,POST-/read,POST-/types",com.kronos.commonapp.timezone.symbolicperiods.rest.impl.SymbolicPeriodForRestV1
/v1/commons/symbolicperiod/analytics_payperiods,PRIVATE,no,no,"POST-/apply_read,POST-/multi_read",com.kronos.commonapp.timezone.symbolicperiods.rest.impl.AnalyticsPeriodForRestV1
/v1/commons/tags,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/apply_update,PUT-/{id}",com.kronos.commonapp.qrcodes.impl.rest.TagServiceForRest
/v1/commons/teletime_ip/user_profiles,PUBLIC,yes,yes,GET-,com.kronos.datacollection.udm.api.TTIPUserProfilesService
/v1/commons/tiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.ia.dashboard.tile.api.rest.TileRestService
/v1/commons/time_increments,PUBLIC,yes,yes,POST-/multi_read,com.kronos.ia.timeseries.rest.TimeIncrementResolverRest
/v1/commons/timecard_approval_methods,PUBLIC,yes,yes,GET-,com.kronos.persons.rest.assignments.service.IRestApprovalMethodService
/v1/commons/timezones,PRIVATE,no,no,"GET-/,GET-/name,GET-/now/employee/{employeeId},GET-/now/location/{id},GET-/now/person,GET-/now/tenant/,GET-/now/user/,GET-/timezone/employee/{employeeId},GET-/timezone/location/{id},GET-/timezone/person,GET-/timezone/tenant,GET-/timezone/user,GET-/today/employee/{employeeId},GET-/today/location/{id},GET-/today/person,GET-/today/tenant,GET-/today/user,GET-/{id}",com.kronos.commonapp.timezone.rest.impl.TimeZoneForRestInternalV1
/v1/commons/timezones/apply_update,PUBLIC,yes,yes,PUT-/,com.kronos.commonapp.timezone.rest.impl.TimeZoneForRestV1
/v1/commons/transaction_assistant,PUBLIC,yes,yes,POST-/multi_read,com.kronos.commonapp.transactionassistant.IRestTransactionAssistantService
/v1/commons/transfer_display_profile/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.transfer.setup.rest.TransferDisplaySDMServiceRest
/v1/commons/transfer_display_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.commonapp.transfer.setup.rest.TransferDisplayServiceRest
/v1/commons/user_preferences,PUBLIC,yes,yes,"GET-/,GET-/locale_policy",com.kronos.commonapp.userpreferences.impl.rest.UserPreferencesRestServiceV1
/v1/commons/wifi_access_points,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_delete,POST-/multi_upsert,PUT-/{id}",com.kronos.commonapp.wifiaccesspoints.impl.rest.WifiAccessPointsServiceForRest
/v1/commons/wifi_access_points_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.wifiaccesspoints.impl.rest.SDMWifiAccessPointsServiceForRest
/v1/commons/wifi_networks,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_delete,POST-/multi_upsert,PUT-/{id}",com.kronos.commonapp.wifinetworks.impl.rest.WifiNetworksServiceForRest
/v1/commons/wifi_networks_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.wifinetworks.impl.rest.SDMWifiNetworksServiceForRest
/v1/commons/work_groups,PUBLIC,no,no,"GET-,GET-/job_code_mappings,POST-/job_code_mappings/multi_upsert,POST-/multi_upsert",com.kronos.analytics.workgroup.rest.api.service.IWorkGroupRestService
/v1/commons/work_units,PUBLIC,yes,yes,"GET-,POST-/multi_upsert",com.kronos.analytics.workunit.rest.api.service.WorkUnitForRestV1
/v1/commons/work_units/locations,PUBLIC,yes,yes,POST-/apply_read,com.kronos.analytics.workunit.traversal.rest.api.service.IWorkUnitLocationsForRestV1
/v1/commons/work_units/widget,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/report,GET-/{id},POST-,PUT-/{id}",com.kronos.analytics.workunit.rest.api.service.WorkUnitUIForRestV1
/v1/commons/work_weeks,PRIVATE,no,no,GET-/,com.kronos.commonbusiness.payperiod.restservice.WorkWeekRestService
/v1/config/display_profiles,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/alert_profiles,GET-/hyperfind_profiles,GET-/permissions,GET-/tmsShowPunchTileMobileDisplayProfileConfig,GET-/{id},POST-,PUT-/{id}",com.kronos.configapp.displayprofile.restservice.DisplayProfileRestService
/v1/config/display_profiles/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.configapp.displayprofile.restservice.DisplayProfileSDMRest
/v1/config/person_profiles,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.configapp.commonbusiness.peopleinfo.restservice.PeopleInfoRestService
/v1/config/person_profiles/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.configapp.commonbusiness.peopleinfo.restservice.PeopleInfoSDMRestService
/v1/config/person_settings,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.configapp.commonbusiness.peopleinfo.restservice.PeopleSettingRestService
/v1/config/person_settings/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.configapp.commonbusiness.peopleinfo.restservice.PeopleSettingSDMRestService
/v1/config_app/knownplaces,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.commonapp.knownplaces.impl.rest.KnownPlacesServiceForRest
/v1/config_app/sdm_cost_center,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,PUT-/{key}",com.kronos.commonapp.laborcategory.setup.impl.rest.SDMCostCenterServiceForRest
/v1/config_app/sdm_employee_group,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.employeegroup.setup.impl.rest.SDMEmployeeGroupServiceForRest
/v1/config_app/sdm_exception_categories,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.timekeeping.exception.tile.impl.rest.SDMExceptionCategoryRestService
/v1/config_app/sdm_exception_tiles,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.timekeeping.exception.tile.impl.rest.SDMExceptionTileRestService
/v1/config_app/sdm_knownplaces,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.knownplaces.impl.rest.SDMKnownPlacesServiceForRest
/v1/config_app/sdm_labor_category,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,PUT-/{key}",com.kronos.commonapp.laborcategory.setup.impl.rest.SDMLaborCategoryServiceForRest
/v1/config_app/sdm_labor_entry,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.laborcategory.setup.impl.rest.SDMLaborCategoryEntryServiceForRest
/v1/config_app/sdm_labor_list,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.laborcategory.setup.impl.rest.SDMLaborCategoryEntryListServiceForRest
/v1/config_app/sdm_labor_profile,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.laborcategory.setup.impl.rest.SDMLaborCategoryProfileServiceForRest
/v1/config_app/sdm_qrcodes,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.qrcodes.impl.rest.SDMTagServiceForRest
/v1/config_app/sdm_timecard_addon_profiles,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.timekeeping.setup.timecardconfiguration.impl.rest.SDMTimecardAddonProfileRestService
/v1/config_app/sdm_timecard_setting,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.timekeeping.setup.timecardconfiguration.impl.rest.SDMTimecardSettingRestService
/v1/control_center_profile/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.controlcenter.api.restservice.profile.ControlCenterProfileSDMRestService
/v1/core/asynchronous,PUBLIC,yes,yes,"GET-/result/{key},GET-/status/{key}",com.kronos.api.commoncomponent.IRestPollingService
/v1/event_manager,PRIVATE,no,no,"DELETE-/event/{id},GET-/event/information/{id},GET-/event/{id},POST-/event,POST-/systemevent/{id}/run,PUT-/event/disable/{id},PUT-/event/enable/{id},PUT-/event/update_system_events,PUT-/event/{id}",com.kronos.eventmgrwrapper.restservices.api.EventMgrWrapperRest
/v1/flow,PRIVATE,no,no,POST-/notification,com.kronos.commonapp.flexflowintegration.FlexRestService
/v1/forecasting/accuracy_basic_engine,PUBLIC,yes,yes,"POST-/apply_create,POST-/apply_delete",com.kronos.forecasting.accuracyengine.rest.AccuracyBasicEngineServiceForRestV1
/v1/forecasting/accuracy_metrics_engine,PUBLIC,yes,yes,POST-/apply_read,com.kronos.forecasting.accuracyengine.rest.AccuracyMetricsServiceForRestV1
/v1/forecasting/actual_volume,PUBLIC,yes,yes,"POST-/apply_read,POST-/import,POST-/multi_read",com.kronos.forecasting.actualvolume.rest.ActualVolumeServiceForRestV1
/v1/forecasting/actual_volume_details,PRIVATE,no,no,POST-/multi_read,com.kronos.forecasting.actualvolume.rest.ActualVolumeDetailsServiceForRestV1
/v1/forecasting/adjustment_driver_settings,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-/,POST-/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.adjustmentdriver.impl.rest.AdjustmentDriverSettingsServiceForRestV1
/v1/forecasting/adjustment_driver_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.forecasting.setup.adjustmentdriver.impl.rest.sdm.AdjustmentDriverSettingsSdmServiceForRestV1
/v1/forecasting/adjustment_drivers,PUBLIC,yes,yes,"POST-/apply_upsert,POST-/multi_read,POST-/multi_upsert",com.kronos.forecasting.adjustmentdriver.rest.AdjustmentDriverServiceForRestV1
/v1/forecasting/audit_records,PUBLIC,yes,yes,POST-/multi_read,com.kronos.forecasting.auditing.rest.ForecastingAuditingServiceForRestV1
/v1/forecasting/budget_distribution_types,PUBLIC,yes,yes,GET-,com.kronos.forecasting.masterdata.budgetdistributiontype.rest.BudgetDistributionTypeServiceForRestV1
/v1/forecasting/category_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.categoryprofile.rest.CategoryProfileRestService
/v1/forecasting/category_property_set_assignments,PUBLIC,yes,yes,"GET-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert",com.kronos.forecasting.setup.categorypropertyset.rest.CategoryPropertySetAssignmentServiceForRestV1
/v1/forecasting/category_property_sets,PUBLIC,yes,yes,"GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_read",com.kronos.forecasting.setup.categorypropertyset.rest.CategoryPropertySetServiceForRestV1
/v1/forecasting/combined_distribution_offset_types,PUBLIC,yes,yes,GET-,com.kronos.forecasting.masterdata.combineddistributiontype.rest.CombinedDistributionOffsetTypeServiceForRestV1
/v1/forecasting/combined_distribution_types,PUBLIC,yes,yes,GET-,com.kronos.forecasting.masterdata.combineddistributiontype.rest.CombinedDistributionTypeServiceForRestV1
/v1/forecasting/combined_labor_distributions,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.combineddistribution.rest.CombinedLaborDistributionServiceForRestV1
/v1/forecasting/consolidated_labor_forecast,PUBLIC,yes,yes,POST-/apply_read,com.kronos.forecasting.laborforecast.rest.ConsolidatedLaborForecastServiceForRestV1
/v1/forecasting/custom_driver_assignments,PUBLIC,yes,yes,"GET-,POST-/apply_read,POST-/multi_read",com.kronos.forecasting.setup.labordriver.rest.CustomDriverAssignmentServiceForRestV1
/v1/forecasting/custom_driver_values,PUBLIC,yes,yes,"POST-/apply_upsert,POST-/multi_read,POST-/multi_upsert",com.kronos.forecasting.customdriver.rest.CustomDriverServiceForRestV1
/v1/forecasting/custom_drivers,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.labordriver.rest.CustomDriverServiceForRestV1
/v1/forecasting/daily_actual_volume,PUBLIC,yes,yes,POST-/multi_create,com.kronos.forecasting.actualvolume.rest.DailyActualVolumeServiceForRestV1
/v1/forecasting/earned_hours_engine,PUBLIC,yes,yes,POST-/apply_create,com.kronos.forecasting.laborengine.rest.EarnedHoursEngineServiceForRestV1
/v1/forecasting/engine_statuses,PUBLIC,yes,yes,POST-/apply_read,com.kronos.forecasting.enginestatus.rest.EngineStatusServiceForRestV1
/v1/forecasting/engine_statuses_action,PRIVATE,no,no,"POST-/content,POST-/resource",com.kronos.forecasting.enginestatus.rest.EngineStatusWidgetServiceForRestV1
/v1/forecasting/forecast_planner_profile_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.forecasting.setup.forecastplanner.impl.rest.sdm.ForecastPlannerProfileSdmServiceForRestV1
/v1/forecasting/forecast_planner_profiles,PUBLIC,yes,yes,"DELETE-,DELETE-/{id},GET-,GET-/{id},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.forecastplanner.impl.rest.ForecastPlannerProfileServiceForRestV1
/v1/forecasting/forecast_planner_settings,PUBLIC,yes,yes,"DELETE-,DELETE-/{id},GET-,GET-/resources/factor_types,GET-/resources/steps,GET-/resources/volume_drivers,GET-/resources/volume_forecast_intervals,GET-/{id},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.forecastplanner.impl.rest.ForecastPlannerSettingsServiceForRestV1
/v1/forecasting/forecast_planner_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.forecasting.setup.forecastplanner.impl.rest.sdm.ForecastPlannerSettingsSdmServiceForRestV1
/v1/forecasting/forecast_week,PUBLIC,yes,yes,GET-,com.kronos.forecasting.setup.forecastweek.rest.ForecastWeekServiceForRestV1
/v1/forecasting/forecast_week,PUBLIC,yes,yes,"GET-/default_start_day,GET-/start_day,POST-/start_days",com.kronos.forecasting.rest.ForecastWeekServiceForRestV1
/v1/forecasting/forecasted_jobs,PUBLIC,yes,yes,POST-/apply_read,com.kronos.forecasting.setup.orgjob.rest.OrgJobServiceForRestV1
/v1/forecasting/generic_categories,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.forecasting.setup.genericcategory.rest.GenericCategoryServiceForRestV1
/v1/forecasting/generic_departments,PUBLIC,yes,yes,"GET-,GET-/{id}",com.kronos.forecasting.setup.genericdepartment.rest.GenericDepartmentServiceForRestV1
/v1/forecasting/hours_operation_assignments,PUBLIC,yes,yes,"GET-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert",com.kronos.forecasting.hoursofoperation.rest.HoursOfOperationAssignmentServiceForRestV1
/v1/forecasting/hours_operation_override,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.hoursofoperation.rest.HoursOfOperationOverrideServiceForRestV1
/v1/forecasting/hours_operation_override_assignments,PUBLIC,yes,yes,"GET-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert",com.kronos.forecasting.hoursofoperation.rest.HoursOfOperationOverrideAssignmentServiceForRestV1
/v1/forecasting/labor_budget,PUBLIC,yes,yes,"POST-/apply_read,POST-/apply_update,POST-/import,POST-/multi_read",com.kronos.forecasting.laborbudget.rest.LaborBudgetServiceForRestV1
/v1/forecasting/labor_constraint_engine,PUBLIC,yes,yes,POST-/apply_create,com.kronos.forecasting.laborconstraintengine.rest.LaborConstraintEngineServiceForRestV1
/v1/forecasting/labor_constraint_profile_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.forecasting.setup.laborconstraint.impl.rest.sdm.LaborConstraintProfileSdmServiceForRestV1
/v1/forecasting/labor_constraint_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-/,POST-/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.laborconstraint.impl.rest.LaborConstraintProfileServiceForRestV1
/v1/forecasting/labor_constraint_settings,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/setup/adjustment_types,GET-/setup/control_types,GET-/setup/labor_forecast_distribution_method_types,GET-/setup/period_distributions,GET-/{id},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.laborconstraint.impl.rest.LaborConstraintSettingsServiceForRestV1
/v1/forecasting/labor_constraint_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.forecasting.setup.laborconstraint.impl.rest.sdm.LaborConstraintSettingsSdmServiceForRestV1
/v1/forecasting/labor_distribution_group_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-/,POST-/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.labordistributiongroup.impl.rest.LaborDistributionGroupProfileServiceForRestV1
/v1/forecasting/labor_distribution_group_profiles_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.forecasting.setup.labordistributiongroup.impl.rest.sdm.LaborDistributionGroupProfileSdmServiceForRestV1
/v1/forecasting/labor_distribution_groups,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/setup/allocation_method_types,GET-/setup/borrow_hours_types,GET-/{id},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.labordistributiongroup.impl.rest.LaborDistributionGroupServiceForRestV1
/v1/forecasting/labor_distribution_groups_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.forecasting.setup.labordistributiongroup.impl.rest.sdm.LaborDistributionGroupSdmServiceForRestV1
/v1/forecasting/labor_forecast,PUBLIC,yes,yes,"POST-,POST-/apply_read,POST-/import,POST-/multi_create,POST-/multi_read,POST-/multi_update",com.kronos.forecasting.laborforecast.rest.LaborForecastServiceForRestV1
/v1/forecasting/labor_forecast_analyzer,PUBLIC,yes,yes,POST-/apply_create,com.kronos.forecasting.labordiagnostic.rest.LaborForecastAnalyzerServiceForRestV1
/v1/forecasting/labor_forecast_limit_assignments,PUBLIC,yes,yes,"POST-/multi_delete,POST-/multi_read,POST-/multi_upsert",com.kronos.forecasting.setup.laborforecastlimit.rest.LaborForecastLimitAssignmentServiceForRestV1
/v1/forecasting/labor_forecast_limits,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.laborforecastlimit.rest.LaborForecastLimitServiceForRestV1
/v1/forecasting/labor_forecaster,PUBLIC,yes,yes,POST-/apply_create,com.kronos.forecasting.laborengine.rest.LaborForecastEngineServiceForRestV1
/v1/forecasting/labor_period_overrides,PUBLIC,yes,yes,"POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,POST-/overridable_department_tasks/apply_read",com.kronos.forecasting.laborperiodoverride.rest.LaborPeriodOverrideServiceForRestV1
/v1/forecasting/labor_poa_types,PUBLIC,yes,yes,GET-,com.kronos.forecasting.masterdata.poa.rest.LaborPeriodOfApplicationTypeServiceForRestV1
/v1/forecasting/labor_standard_tasks,PUBLIC,yes,yes,"POST-/import,POST-/purge",com.kronos.forecasting.laborstandard.tasks.rest.LaborStandardServiceTaskForRestV1
/v1/forecasting/labor_standards,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/labor_driver_operations,GET-/{id},POST-,POST-/apply_update,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update",com.kronos.forecasting.laborstandard.rest.LaborStandardServiceForRestV1
/v1/forecasting/linked_category,PUBLIC,yes,yes,"GET-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update",com.kronos.forecasting.setup.linkedcategory.rest.LinkedCategoryServiceForRestV1
/v1/forecasting/linked_category_action_types,PUBLIC,yes,yes,GET-/,com.kronos.forecasting.setup.linkedcategory.rest.LinkedCategoryActionTypeServiceForRestV1
/v1/forecasting/machine_learning_model_features,PUBLIC,yes,yes,"GET-,GET-/{id}",com.kronos.forecasting.setup.machinelearning.rest.MachineLearningFeatureLocalizedNameForRestV1
/v1/forecasting/machine_learning_models,PUBLIC,no,no,,com.kronos.forecasting.setup.machinelearning.rest.MachineLearningModelServiceForRestV1
/v1/forecasting/machine_learning_models_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.forecasting.setup.machinelearning.rest.MachineLearningSDMServiceForRestV1
/v1/forecasting/operational_metric_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.forecasting.setup.operationalmetric.rest.OperationalMetricSdmServiceForRestV1
/v1/forecasting/peak_hours,PUBLIC,yes,yes,POST-/apply_read,com.kronos.forecasting.peakhours.rest.PeakHoursServiceForRestV1
/v1/forecasting/shap_values,PUBLIC,yes,yes,"POST-/metrics/feature_dependence/apply_read,POST-/metrics/feature_importance/apply_read,POST-/metrics/local_explainer/apply_read",com.kronos.forecasting.engine.explainer.impl.rest.ShapValuesMetricsServiceForRestV1
/v1/forecasting/special_event_assignments,PUBLIC,yes,yes,"GET-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read",com.kronos.forecasting.setup.specialevent.impl.rest.SpecialEventAssignmentServiceForRestV1
/v1/forecasting/special_events,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.specialevent.impl.rest.SpecialEventServiceForRestV1
/v1/forecasting/static_driver_assignments,PUBLIC,yes,yes,"GET-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update",com.kronos.forecasting.setup.labordriver.rest.StaticDriverAssignmentServiceForRestV1
/v1/forecasting/static_drivers,PUBLIC,yes,yes,"GET-,GET-/{id}",com.kronos.forecasting.setup.labordriver.rest.StaticDriverServiceForRestV1
/v1/forecasting/task_groups,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.taskgroup.rest.TaskGroupServiceForRestV1
/v1/forecasting/tasks,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.task.rest.TaskServiceForRestV1
/v1/forecasting/traffic_pattern_engine,PUBLIC,yes,yes,POST-/apply_create,com.kronos.forecasting.trafficpatternengine.rest.TrafficPatternEngineServiceForRestV1
/v1/forecasting/traffic_patterns,PUBLIC,yes,yes,"POST-/multi_create,POST-/multi_delete,POST-/multi_read",com.kronos.forecasting.trafficpattern.rest.TrafficPatternServiceForRestV1
/v1/forecasting/training_model,PRIVATE,no,no,"POST-,POST-/multi_read",com.kronos.forecasting.engine.trainingmodel.impl.rest.TrainingModelForRestV1
/v1/forecasting/volume_analyzer,PUBLIC,yes,yes,"GET-/analysis_data_sample_sizes,GET-/anomaly_scores,GET-/history_years,POST-/apply_create",com.kronos.forecasting.volumeanalyzer.rest.VolumeAnalyzerServiceForRestV1
/v1/forecasting/volume_budget,PUBLIC,yes,yes,"POST-/import,POST-/multi_read",com.kronos.forecasting.volumebudget.rest.VolumeBudgetServiceForRestV1
/v1/forecasting/volume_driver_assignments,PUBLIC,yes,yes,"GET-,POST-/multi_read,POST-/multi_upsert",com.kronos.forecasting.setup.volumedriver.rest.VolumeDriverAssignmentServiceForRestV1
/v1/forecasting/volume_drivers,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.volumedriver.rest.VolumeDriverServiceForRestV1
/v1/forecasting/volume_forecast,PUBLIC,yes,yes,"POST-/import,POST-/multi_read,POST-/restore,PUT-/",com.kronos.forecasting.volumeforecast.rest.VolumeForecastServiceForRestV1
/v1/forecasting/volume_forecast_intervals_engine,PUBLIC,yes,yes,POST-/apply_create,com.kronos.forecasting.volumeforecastintervals.rest.VolumeForecastIntervalsEngineServiceForRestV1
/v1/forecasting/volume_forecast_model_types,PUBLIC,yes,yes,GET-,com.kronos.forecasting.masterdata.volumeforecastmodeltype.rest.VolumeForecastModelTypeServiceForRestV1
/v1/forecasting/volume_forecaster,PUBLIC,yes,yes,"POST-/apply_create,POST-/apply_read",com.kronos.forecasting.volumeengine.rest.VolumeForecastEngineServiceForRestV1
/v1/forecasting/volume_forecasts,PUBLIC,yes,yes,POST-/multi_read,com.kronos.forecasting.volumeforecast.rest.VolumeForecastsServiceForRestV1
/v1/forecasting/week_symbolic_periods,PUBLIC,yes,yes,GET-,com.kronos.forecasting.setup.weeksymbolicperiod.rest.WeekSymbolicPeriodServiceForRestV1
/v1/genericrequest/employee_submission_priority_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.requestemployeepriority.impl.sdm.rest.SDMRequestEmployeePriorityServiceForRestV1
/v1/genericrequest/requestitems,PRIVATE,no,no,"DELETE-/{id},POST-,POST-/automatic_action,POST-/bulk_create,POST-/bulk_delete,POST-/changeStatus,POST-/reevaluate_approvers,PUT-/bulk_update,PUT-/{id}",com.kronos.genericrequest.impl.rest.GenericRequestItemRestServiceImpl
/v1/hca,PRIVATE,no,no,GET-/workunit_hyperfinds,com.kronos.analytics.workunit.hyperfind.api.rest.service.IWorkUnitHyperfindRestService
/v1/hca/agency/recalculate,PUBLIC,yes,yes,"GET-/{id}/status,POST-/async",com.kronos.analytics.labortype.agency.api.rest.AgencyRecalcRestService
/v1/hca/notification,PRIVATE,no,no,POST-/apply_upsert,com.kronos.hca.notification.services.rest.api.IHCANotificationRestV1
/v1/hca/payroll,PUBLIC,yes,yes,"GET-/agency_process/employees,GET-/agency_process/employees/{execution_key}/status,POST-/agency_process/apply_read,POST-/agency_process/employees/async,POST-/agency_process/status/apply_update",com.kronos.analytics.labortype.agency.api.rest.LaborTypeAgencyExportRestV1
/v1/hca/payroll,PUBLIC,yes,yes,POST-/apply_read,com.kronos.hca.payrollconfig.services.rest.api.IPayrollDataEnricherRestV1
/v1/hca/payroll/department_maps,PUBLIC,yes,yes,"GET-,POST-/multi_upsert",com.kronos.hca.payrollconfig.services.rest.api.IPayrollDepartmentWURestV1
/v1/hca/payroll/employment_status_maps,PUBLIC,yes,yes,"GET-,POST-/multi_upsert",com.kronos.hca.payrollconfig.services.rest.api.IPayrollEmploymentStatusLaborTypeMappingRestV1
/v1/hca/payroll/job_maps,PUBLIC,yes,yes,"GET-,POST-/multi_upsert",com.kronos.hca.payrollconfig.services.rest.api.IPayrollJobGenericJobMappingRestV1
/v1/hca/raw_volume_errors,PUBLIC,yes,yes,"GET-/,POST-/multi_upsert",com.kronos.analytics.rawvolume.rest.IRawVolumeDataErrorsForRestV1
/v1/hca/volume,PUBLIC,yes,yes,GET-/{executionKey}/status,com.kronos.analytics.rawvolume.rest.VolumeProcessStatusForRestV1
/v1/hca/volume/billing_department_maps,PUBLIC,yes,yes,"GET-,POST-/multi_upsert",com.kronos.volumedriver.rest.IBillingDepartmentWUForRestV1
/v1/hca/volume/billing_department_maps/sync_mappings,PRIVATE,no,no,POST-,com.kronos.volumedriver.rest.IBillingDepartmentWUSyncMappingForRestV1
/v1/hca/volume/charge_masters,PUBLIC,yes,yes,"GET-,POST-/multi_upsert,POST-/volume_labels/multi_read",com.kronos.volumedriver.chargemaster.rest.IChargeMasterForRestV1
/v1/hca/volume/raw_volume,PUBLIC,yes,yes,POST-/multi_upsert,com.kronos.analytics.rawvolume.rest.IRawVolumeForHCARestV1
/v1/hca/volume/recalculate,PUBLIC,yes,yes,POST-/async,com.kronos.analytics.volume.recalc.rest.IVolumeRecalcForRestV1
/v1/hca/volume/status,PRIVATE,no,no,POST-/,com.kronos.analytics.rawvolume.rest.IVolumeProcessRestStatusV1
/v1/hca/volume/volume_copy_maps,PUBLIC,yes,yes,"GET-,POST-/multi_upsert",com.kronos.volumedriver.volumecopyoverride.rest.IVolumeDeptCopyForRestV1
/v1/hca/volume/volume_copy_override_maps,PUBLIC,yes,yes,"GET-,POST-/multi_upsert",com.kronos.volumedriver.volumecopyoverride.rest.IVolumeCopyOverrideForRestV1
/v1/hca/work_unit_hyperfind_profiles,PUBLIC,yes,yes,"GET-/,POST-/multi_upsert",com.kronos.analytics.workunit.hyperfind.api.rest.service.IWorkUnitHyperfindProfileServiceForRestV1
/v1/hca/work_unit_hyperfinds,PUBLIC,yes,yes,"GET-/,POST-/apply_read,POST-/multi_upsert",com.kronos.analytics.workunit.hyperfind.api.rest.service.IWorkUnitHyperfindServiceForRestV1
/v1/hcm/payroll/data/apply_read,PUBLIC,yes,yes,POST-/,com.kronos.payroll.aggregation.restservices.api.HCMPayrollAggregationRest
/v1/hcmconfig/paycode_payroll_map/sdm,PRIVATE,no,no,"GET-/,GET-/keys,PUT-/{key}",com.kronos.payroll.restservices.api.HCMPaycodeSDMRest
/v1/hcmconfig/payroll/sdm,PRIVATE,no,no,"GET-/,GET-/keys,PUT-/{key}",com.kronos.payroll.restservices.api.HCMPayrollSDMRest
/v1/hyperfindquery,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.commonapp.hyperfindquery.restservice.HyperfindSDMRestService
/v1/ihub/callback,PRIVATE,no,no,PUT-,com.kronos.integrationhub.restservices.api.IhubCallBackRestService
/v1/ihub/integration_migrate,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.integrationhub.processmigration.api.IhubProcessMigrationRestService
/v1/ihub/list_parameter_migrate,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.integrationhub.processmigration.api.IhubListParameterMigrationRestService
/v1/ihub/sequence_migrate,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.integrationhub.processmigration.api.IhubSequenceMigrationRestService
/v1/leave/audit_records,PUBLIC,yes,yes,POST-/multi_read,com.kronos.leave.service.audit.impl.rest.LeaveAuditRestService
/v1/leave/calendars_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.leave.service.setup.api.rest.masterdata.LeaveCalendarSettingsSDMRestService
/v1/leave/case_statuses,PUBLIC,yes,yes,GET-/,com.kronos.leave.service.common.api.rest.referencedata.LeaveCaseStatusRestService
/v1/leave/custom_fields,PUBLIC,yes,yes,GET-/,com.kronos.leave.service.setup.api.rest.masterdata.LeaveCustomDataRestService
/v1/leave/events,PUBLIC,yes,yes,POST-/,com.kronos.leave.calendar.api.rest.LeaveEventRestService
/v1/leave/frequencies,PUBLIC,yes,yes,GET-/,com.kronos.leave.service.common.api.rest.referencedata.LeaveCaseFrequencyRestService
/v1/leave/leave_cases,PUBLIC,yes,yes,"GET-/defaults,GET-/{id}/defaults,PUT-/defaults",com.kronos.leave.service.leavecase.impl.rest.LeaveCaseDefaultsRestServiceImpl
/v1/leave/leave_cases,PUBLIC,yes,yes,"GET-/grant_data,GET-/total_taking,POST-/grant",com.kronos.leave.service.leavecase.api.rest.LeaveCaseLeaveTypeRestService
/v1/leave/leave_cases,PUBLIC,yes,yes,"DELETE-/,GET-/,GET-/{id},POST-/,POST-/multi_create,POST-/multi_read,PUT-/,PUT-/{id}/leave_types",com.kronos.leave.service.leavecase.api.rest.LeaveCaseRestService
/v1/leave/leave_cases/case_rules,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-/,POST-/{id}",com.kronos.leave.service.leavecase.api.rest.LeaveCaseRuleRestService
/v1/leave/leave_cases/certifications,PUBLIC,yes,yes,"GET-/,POST-",com.kronos.leave.service.leavecase.api.rest.LeaveCaseCertificationRestService
/v1/leave/leave_cases/custom_fields,PUBLIC,yes,yes,"GET-/,POST-/multi_update",com.kronos.leave.service.leavecase.api.rest.LeaveCaseCustomDataRestService
/v1/leave/leave_cases/documents,PUBLIC,yes,yes,"GET-/,POST-/generate,POST-/multi_update",com.kronos.leave.service.leavecase.api.rest.document.LeaveCaseDocumentRestService
/v1/leave/leave_cases/eligibility_requirements,PUBLIC,yes,yes,"GET-/,POST-/",com.kronos.leave.service.leavecase.api.rest.LeaveCaseEligibilityRequirementRestService
/v1/leave/leave_cases/notes,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-/,POST-/{id}",com.kronos.leave.service.leavecase.api.rest.LeaveCaseNoteRestService
/v1/leave/leave_cases/notifications,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-/,POST-/{id},POST-/{id}/activate,POST-/{id}/copy,POST-/{id}/deactivate",com.kronos.leave.service.leavecase.api.rest.notification.LeaveCaseNotificationRestService
/v1/leave/leave_cases/positions,PRIVATE,no,no,GET-/{person_id},com.kronos.leave.service.leavecase.api.rest.LeaveCasePositionRestService
/v1/leave/leave_edits,PUBLIC,yes,yes,"POST-/,POST-/multi_delete,POST-/multi_read",com.kronos.leave.service.time.api.rest.StatelessLeaveEditRestService
/v1/leave/leave_profiles,PUBLIC,yes,yes,"GET-,GET-/{id}",com.kronos.leave.profile.api.rest.LeaveProfileRestService
/v1/leave/leave_reminder_templates_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.leave.service.setup.impl.rest.ReminderTemplateSDMRestServiceImpl
/v1/leave/leave_request_configuration,PUBLIC,yes,yes,"GET-/,GET-/request_subtypes",com.kronos.leave.request.api.rest.LeaveRequestConfigurationRestService
/v1/leave/leave_requests,PUBLIC,yes,yes,"POST-/,POST-/multi_action,POST-/multi_create,POST-/multi_read,POST-/{id}",com.kronos.leave.request.api.rest.LeaveRequestRestService
/v1/leave/rules,PUBLIC,yes,yes,GET-/,com.kronos.leave.service.setup.api.rest.masterdata.LeaveRuleRestService
/v1/leave/schedules,PUBLIC,yes,yes,GET-,com.kronos.leave.calendar.api.rest.EmployeeScheduleRestService
/v1/leave/setup/leave_categories,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.leave.service.setup.api.rest.masterdata.LeaveSetupCategoryRestService
/v1/leave/setup/leave_reasons,PUBLIC,yes,yes,"GET-/,GET-/{id},POST-/multi_read",com.kronos.leave.service.setup.api.rest.masterdata.LeaveSetupReasonRestService
/v1/licensing,PUBLIC,yes,yes,"GET-/entitlement_history,GET-/entitlement_summary",com.kronos.licensing.api.rest.services.EntitlementSummaryRestService
/v1/locale_policies/widget,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/country_codes,GET-/currency/negative_pattern,GET-/currency/positive_pattern,GET-/current_user,GET-/date_time/day_month,GET-/date_time/long_date,GET-/date_time/month_year,GET-/date_time/short_date,GET-/date_time/time,GET-/days_of_week,GET-/decimal_symbols,GET-/grouping_symbols,GET-/language_codes,GET-/number/grouping_patterns,GET-/number/negative_pattern,GET-/session_locale/all_locale,GET-/{id},POST-,POST-/currency/preview,POST-/date/preview,POST-/number/preview,PUT-/{id},PUT-/{id}/tenant_default",com.kronos.commonapp.localepolicy.widget.setup.impl.rest.LocalePolicyWidgetRestService
/v1/mobile,PRIVATE,no,no,"GET-/mobile_user_details,GET-/mobile_user_details_configuration",com.kronos.commonbusiness.mobiledevice.impl.rest.MobileUserDetailsService
/v1/mobile,PRIVATE,no,no,GET-/oauth_client_credentials,com.kronos.commonbusiness.mobiledevice.impl.rest.MobileOAuthService
/v1/mobile,PRIVATE,no,no,"POST-/mobileSetup,POST-/mobileappcontext",com.kronos.commonbusiness.mobiledevice.impl.rest.MobileAppContextRestService
/v1/mobile-gateway,PRIVATE,no,no,PUT-/mobilelogonstate,com.kronos.commonbusiness.mobileextendedauth.impl.rest.LogonStateRestServiceImpl
/v1/mobile-gateway,PRIVATE,no,no,GET-/properties,com.kronos.commonbusiness.mobilegateway.impl.rest.mobileproperty.MobilePropertyRestServiceImpl
/v1/mobile-gateway,PRIVATE,no,no,GET-/authtokenstate,com.kronos.commonbusiness.mobileextendedauth.impl.rest.ExtendedAuthStateImpl
/v1/mobile/api_gateway_mobile_client_key,PRIVATE,no,no,"DELETE-/{developerName},GET-/{developerName},POST-,PUT-",com.kronos.commonbusiness.mobiledevice.impl.rest.MobileApiGatewayService
/v1/mobile/api_gateway_talk_client_key,PRIVATE,no,no,"DELETE-/{developerName},GET-/{developerName},POST-,PUT-",com.kronos.commonbusiness.mobiledevice.impl.rest.CommunicationsApiGatewayService
/v1/mobile/communications_configuration,PRIVATE,no,no,GET-,com.kronos.commonbusiness.mobiledevice.impl.rest.CommunicationsConfigurationService
/v1/mobile/devices,PRIVATE,no,no,"DELETE-,GET-,POST-,PUT-",com.kronos.commonbusiness.mobiledevice.impl.rest.DeviceRegistrationService
/v1/mobile/push_tokens,PRIVATE,no,no,"DELETE-/{resourceId},GET-/{pushTokenValue},POST-,PUT-",com.kronos.commonbusiness.mobiledevice.impl.rest.TokenRegistrationService
/v1/organization/SDM/generic_locations,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,POST-/dependencies,PUT-/{key}",com.ukg.genericlocations.services.sdm.api.rest.IOrgGenericLocationSDMRestService
/v1/payroll_extract/accrual_code_mapping/sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-/dependencies,PUT-/{key}",com.kronos.payroll.extract.restservices.sdm.api.AccrualCodeMappingRestSDMService
/v1/payroll_extract/accrual_transaction_mapping/sdm,PRIVATE,no,no,"GET-/,GET-/keys,PUT-/{key}",com.kronos.payroll.extract.restservices.sdm.api.AccrualTransactionMappingRestSDMService
/v1/payroll_extract/gl_mapping/sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-/dependencies,PUT-/{key}",com.kronos.payroll.extract.restservices.sdm.api.GeneralLedgerCodeMappingRestSDMService
/v1/payroll_extract/payroll_paycode_mapping/sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,POST-/dependencies,PUT-/{key}",com.kronos.payroll.extract.restservices.sdm.api.PayrollPaycodeMappingRestSDMService
/v1/permissions,PRIVATE,no,no,"GET-,GET-/property,GET-/{name}",com.kronos.commonapp.authz.api.services.permission.PermissionsService
/v1/platform,PRIVATE,no,no,"GET-/contexts,GET-/countries,GET-/languages,GET-/properties,GET-/properties/TranslationTimestamp,GET-/properties/{nodePath}/sdm,GET-/properties/{nodePath}/sdm/keys,GET-/swimlanes,POST-/environment/properties,POST-/environment/properties/multi_delete,POST-/properties,POST-/properties/export,POST-/properties/multi_delete,POST-/properties/{nodePath}/sdm,PUT-/properties/{nodePath}/sdm/{key}",com.kronos.commonapp.kronosproperties.api.restservice.KPropertiesRestService
/v1/platform/analytics/mapping_categories,PUBLIC,yes,yes,"GET-/,GET-/{id},POST-/,PUT-/{id}",com.kronos.kpifrm.builder.business.api.rest.services.mappingcategory.RestMappingCategoryService
/v1/platform/analytics/mapping_category_types,PUBLIC,yes,yes,"GET-/,GET-/{id}",com.kronos.kpifrm.builder.business.api.rest.services.mappingcategory.RestMappingCategoryTypeService
/v1/platform/announcements/action_templates,PUBLIC,yes,yes,"GET-,GET-/{id}",com.kronos.commonbusiness.announcement.actiontemplate.api.rest.IActionTemplateRestService
/v1/platform/announcements/employee_announcements,PUBLIC,yes,yes,"GET-/{id},POST-/apply_update",com.kronos.commonbusiness.announcement.api.rest.IEmployeeAnnouncementRestService
/v1/platform/announcements/manager_announcements,PUBLIC,yes,yes,"GET-/{id},POST-,POST-/apply_read",com.kronos.commonbusiness.announcement.api.rest.IManagerAnnouncementRestService
/v1/platform/api_integrations,PUBLIC,yes,yes,"GET-,GET-/{id}/status,GET-/{key},POST-/execute/{key}",com.kronos.integrationhub.restservices.api.IhubRealTimeIntegrationRestService
/v1/platform/batch_processing/batch_events,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},GET-/{id}/batch_tasks,POST-/apply_update,POST-/multi_create,POST-/multi_delete,PUT-/{id}",com.kronos.wfc.rest.services.api.BatchEventRestService
/v1/platform/batch_processing/batch_events/schedule,PUBLIC,yes,yes,POST-/multi_create,com.kronos.wfc.rest.services.api.BatchEventScheduleRestService
/v1/platform/batch_processing/batch_groups,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},GET-/{id}/batch_events,POST-/apply_update,POST-/multi_create,POST-/multi_delete,PUT-/{id}",com.kronos.wfc.rest.services.api.BatchGroupRestService
/v1/platform/batch_processing/batch_job_status,PUBLIC,yes,yes,POST-/multi_read,com.kronos.wfc.rest.services.api.BatchJobStatusRestService
/v2/platform/batch_processing/batch_job_status,PUBLIC,yes,yes,POST-/multi_read,com.kronos.wfc.rest.services.api.BatchJobStatusRestServiceV2
/v1/platform/batch_processing/batch_tasks,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/setup/action_types,GET-/{id},POST-/apply_update,POST-/multi_create,POST-/multi_delete,PUT-/{id}",com.kronos.wfc.rest.services.api.BatchTaskRestService
/v1/platform/data_objects,PRIVATE,no,no,"DELETE-/{id},GET-,POST-,PUT-/{id}",com.kronos.report.dataobject.restservice.DataObjectRestService
/v1/platform/document_templates/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.docmanagement.service.api.rest.template.TemplateSDMRestService
/v1/platform/employee_profile_image,PRIVATE,no,no,"DELETE-,GET-/has_upload_permission,POST-",com.kronos.commonbusiness.photoupload.restservice.EmpProfilePhotoUploadRestService
/v1/platform/event_manager,PUBLIC,yes,yes,"GET-,POST-/multi_delete",com.kronos.eventmgrwrapper.restservices.api.EventManagerRest
/v1/platform/file_uploads,PRIVATE,no,no,"GET-/{id},POST-",com.kronos.commonbusiness.fileupload.api.rest.FileUploadRestService
/v1/platform/integration_executions,PUBLIC,yes,yes,"GET-,GET-/{id},GET-/{id}/file,GET-/{id}/filelist,POST-/multi_read,POST-/{id}/additional_details",com.kronos.integrationhub.publishable.restservices.api.IhubIntgExecPublishableRestService
/v1/platform/integration_list_parameters,PUBLIC,yes,yes,"GET-/{name},POST-/apply_update",com.kronos.integrationhub.setup.publishable.restservices.api.IhubIntegrationListParameterRestService
/v1/platform/integration_schedules,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},PUT-/{id}",com.kronos.integrationhub.publishable.restservices.api.IhubScheduleInstancePublishableRestService
/v1/platform/integrations,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/{id}/execute,POST-/{id}/schedule,PUT-/{id}",com.kronos.integrationhub.publishable.restservices.api.IhubFalconProcessReqPublishableRestService
/v1/platform/integrations/account_info,PUBLIC,no,no,GET-,com.kronos.integrationhub.publishable.restservices.api.IhubAccountEnvReqPublishableRestService
/v1/platform/integrations/configuration,PRIVATE,no,no,"GET-,POST-/multi_read,POST-/multi_update",com.kronos.ihubproperties.setup.restservice.api.IhubIntegrationConfigurationRestService
/v1/platform/integrations/sftp_connections,PUBLIC,yes,yes,"GET-,GET-/{id},GET-/{id}/connection_status,POST-/apply_update",com.kronos.integrationhub.publishable.restservices.api.IhubSFTPManagementPublishableRestService
/v1/platform/integrations/update_status,PUBLIC,yes,yes,POST-,com.kronos.integrationhub.publishable.restservices.api.IhubProcessCallBackRestService
/v1/platform/kpi/batches,PRIVATE,no,no,"GET-/{id},GET-/{id}/increments,POST-/,POST-/multi_read",com.kronos.kpifrm.tools.api.rest.BatchStatusRestService
/v1/platform/kpi/config,PRIVATE,no,no,"GET-/get,POST-/refreshConfigData,POST-/update",com.kronos.kpifrm.tools.api.rest.KPISytemConfigService
/v1/platform/kpi/data_correction,PRIVATE,no,no,"POST-/apply_update,POST-/apply_update/async",com.kronos.kpifrm.tools.api.rest.DataCorrectionRest
/v1/platform/kpi/event_log,PRIVATE,no,no,POST-/multi_read,com.kronos.kpifrm.tools.api.rest.EventLogRestService
/v1/platform/kpi/event_processes,PRIVATE,no,no,"GET-/status,POST-/apply_update",com.kronos.kpifrm.tools.api.rest.KPIEventStatusService
/v1/platform/kpi/processes,PRIVATE,no,no,GET-/{tenantShortName},com.kronos.kpifrm.tools.api.rest.DataCorrectionRestStatus
/v1/platform/kpis/,PRIVATE,no,no,POST-/apply_update,com.kronos.kpifrm.builder.business.api.rest.services.KPIConfigRestService
/v1/platform/messaging/generic_notifications,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/notify,POST-/{id}/notify",com.kronos.controlcenter.api.restservice.notifications.GenericNotificationRestService
/v1/platform/metrics,PRIVATE,no,no,POST-/apply_update,com.kronos.kpifrm.builder.business.api.rest.services.MetricConfigRestService
/v1/platform/payCodeCategories,PRIVATE,no,no,"GET-/,POST-/paycode/multi_read",com.kronos.kpifrm.builder.business.api.rest.services.PayCodeCategoryRestService
/v1/platform/reference_tables,PUBLIC,yes,yes,POST-/apply_update,com.kronos.integrationhub.crt.management.api.rest.publishable.IhubCrtManagementPublishableRestService
/v1/platform/report_categories,PUBLIC,yes,yes,"GET-,GET-/{name}",com.kronos.report.management.restservice.ReportCategoryRestService
/v1/platform/report_dataobjects,PUBLIC,yes,yes,"DELETE-/{dId},GET-,GET-/{id},POST-,POST-/dataelements,POST-/{name}/data,POST-/{name}/data/generate_request_batches,PUT-/{id}",com.kronos.report.dataobject.restservice.ReportingDataObjectRestService
/v1/platform/report_dataobjects/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.report.dataobject.restservice.ReportingDataObjectSDMRestService
/v1/platform/report_designs,PUBLIC,yes,yes,"DELETE-/{name},GET-,GET-/{name},GET-/{name}/parameters",com.kronos.report.management.restservice.ReportDesignRestService
/v1/platform/report_executions,PUBLIC,yes,yes,"GET-,GET-/{id},GET-/{id}/file,POST-/apply_read",com.kronos.report.framework.restservice.ReportExecutionHistoryRestService
/v1/platform/report_map_reduce,PUBLIC,yes,yes,"GET-/all_home_location,POST-/all_home_location,POST-/hyperfind,POST-/org/people_ids,POST-/org_ids",com.kronos.report.dataobject.restservice.ReportingMapReduceRestService
/v1/platform/report_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.report.dap.restservice.ReportDAPRestService
/v1/platform/report_profiles/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.report.dap.restservice.ReportDAPSDMRestService
/v1/platform/reporting_contents,PRIVATE,no,no,POST-/apply_update,com.kronos.report.tenant.management.api.restservice.DomainReportPublishRollbackRestService
/v1/platform/reports,PUBLIC,yes,yes,"GET-/import/async/{executionKey}/status,GET-/{name}/file,POST-/import/async",com.kronos.report.framework.restservice.ReportTransferRestService
/v1/platform/reports,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/{id},POST-/{name}/execute",com.kronos.report.framework.restservice.PublishRunReportRestService
/v1/platform/reports/execution_history,PRIVATE,no,no,POST-/stats,com.kronos.report.framework.restservice.REHRuntimeStatsRestService
/v1/platform/reports/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.report.framework.restservice.PublishReportSDMRestService
/v1/platform/scheduled_reports,PUBLIC,yes,yes,"DELETE-/{id},GET-,POST-,POST-/apply_read,PUT-/{id}",com.kronos.report.framework.restservice.ReportRequestRestService
/v1/platform/standby_db_lag,PRIVATE,no,no,GET-/,com.kronos.dynamicrouting.impl.rest.StandbyDbLagRestService
/v1/platform/switch_employment,PUBLIC,yes,yes,POST-,com.kronos.configapp.supportuser.switchemployment.restservice.ISwitchEmploymentRestService
/v1/platform/target_thresholds,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/export,GET-/short_names,GET-/{id},POST-/,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.kronos.kpifrm.builder.business.api.rest.services.TargetThresholdRestService
/v1/platform/target_thresholds/eval_period,PUBLIC,yes,yes,GET-/,com.kronos.kpifrm.builder.business.api.rest.services.TargetThresholdEvaluationPeriodRestService
/v1/platform/target_thresholds/indicator_texts,PUBLIC,yes,yes,GET-/,com.kronos.kpifrm.builder.business.api.rest.services.TargetThresholdIndicatorTextsRestService
/v1/platform/target_thresholds/metric_sets,PUBLIC,yes,yes,GET-/,com.kronos.kpifrm.builder.business.api.rest.services.TargetMetricSetRestService
/v1/platform/target_thresholds/operators,PUBLIC,yes,yes,GET-/,com.kronos.kpifrm.builder.business.api.rest.services.TargetThresholdOperatorsRestService
/v1/platform/target_thresholds/sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,PUT-/{key}",com.kronos.kpifrm.builder.business.api.rest.sdm.TargetThresholdSDMRestService
/v1/platform/tenant,PUBLIC,yes,yes,GET-/info,com.kronos.wfc.platform.trs.TRSAuthNTenantPublicRestService
/v1/platform/ui/account_configuration,PRIVATE,no,no,"GET-,GET-/{id},GET-/{id}/environment_configuration,PUT-/{id}",com.kronos.integrationhub.restservices.api.IhubAccountConfigRestService
/v1/platform/ui/environment_configuration,PRIVATE,no,no,"GET-/{id},PUT-/{id}",com.kronos.integrationhub.restservices.api.IhubEnvConfigRestService
/v1/platform/ui/integration_actions,PRIVATE,no,no,GET-,com.kronos.integrationhub.restservices.fap.api.IhubIntegrationsActionsRestService
/v1/platform/ui/integration_connection_definitions,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/{id}/test,PUT-/{id}",com.kronos.integrationhub.restservices.api.IhubConnectionDefinitionRestService
/v1/platform/ui/integration_connection_types,PRIVATE,no,no,GET-,com.kronos.integrationhub.restservices.api.IhubConnectionTypeRestService
/v1/platform/ui/integration_definitions,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/actions,GET-/permissions,GET-/{id},POST-,PUT-/{id}",com.kronos.integrationhub.restservices.api.IhubFalconProcessRestService
/v1/platform/ui/integration_executions,PRIVATE,no,no,GET-/{id},com.kronos.integrationhub.restservices.api.IhubMonitoringSlatViewRestService
/v1/platform/ui/integration_executions,PRIVATE,no,no,"GET-,GET-/transaction_assistant_status/{id},GET-/{id}/additional_details_status,POST-/{id}/additional_details/multi_read",com.kronos.integrationhub.restservices.api.IhubMonitoringSlatViewUIRestService
/v1/platform/ui/integration_file,PRIVATE,no,no,"GET-/download/{id}/data,GET-/download/{id}/multi_file",com.kronos.integrationhub.restservices.api.IhubFileUploadDownloadRestService
/v1/platform/ui/integration_instances,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/{id}",com.kronos.integrationhub.restservices.api.IhubProcessRequestRestService
/v1/platform/ui/integration_instances,PRIVATE,no,no,"POST-/execute,POST-/rerun/{falconExecutionId}",com.kronos.integrationhub.restservices.api.IhubProcessInstanceRestService
/v1/platform/ui/integration_instances,PRIVATE,no,no,"POST-/execute_on_schedule,POST-/{name}/execute",com.kronos.integrationhub.restservices.api.IhubProcessInstanceUIRestService
/v1/platform/ui/integration_instances/schedule,PRIVATE,no,no,POST-,com.kronos.integrationhub.restservices.api.IhubScheduleInstanceRestService
/v1/platform/ui/integration_list_parameters,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.integrationhub.setup.restservices.api.IhubListParameterRestService
/v1/platform/ui/integration_set_definitions,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.integrationhub.restservices.api.IhubSequenceDefinitionRestService
/v1/platform/ui/integration_set_instances,PRIVATE,no,no,POST-/execute_on_schedule,com.kronos.integrationhub.restservices.api.IhubIntgSequenceInstanceUIRestService
/v1/platform/ui/integration_set_instances,PRIVATE,no,no,POST-/execute,com.kronos.integrationhub.restservices.api.IhubIntgSequenceInstanceRestService
/v1/platform/ui/integration_set_instances/schedule,PRIVATE,no,no,POST-,com.kronos.integrationhub.restservices.api.IhubScheduleSequenceRequestRestService
/v1/platform/ui/integration_sets,PRIVATE,no,no,GET-,com.kronos.integrationhub.restservices.api.IhubIntegrationSequenceRestService
/v1/platform/ui/integration_sets,PRIVATE,no,no,POST-/validate,com.kronos.integrationhub.restservices.api.IhubIntegrationSeqUIRestService
/v1/platform/ui/integration_templates,PRIVATE,no,no,"GET-,POST-/refresh",com.kronos.integrationhub.restservices.api.IhubProcessRestService
/v1/platform/ui/integration_types,PRIVATE,no,no,GET-,com.kronos.integrationhub.restservices.api.IhubIntegrationTypeRestService
/v1/platform/ui/integrations,PRIVATE,no,no,GET-/{id}/reference_tables,com.kronos.integrationhub.crt.management.api.rest.IhubFalconProcessRequestUIRestService
/v1/platform/ui/integrations,PRIVATE,no,no,GET-,com.kronos.integrationhub.restservices.api.IhubFalconProcessRequestRestService
/v1/platform/ui/parameter_type,PRIVATE,no,no,GET-,com.kronos.integrationhub.restservices.api.IhubMasterParamRestService
/v1/platform/ui/reference_tables,PRIVATE,no,no,"GET-/{integration_id}/{id}/data,POST-/{integration_id}/data/downloadAll",com.kronos.integrationhub.crt.management.api.rest.IhubReferenceTableRestService
/v1/platform/ui/sftp_files,PRIVATE,no,no,"GET-/download/{id}/file,GET-/list/connection/{id},GET-/list/connections,POST-/create/directory/{id}",com.kronos.integrationhub.restservices.api.IhubSFTPUploadedFilesReadRestService
/v1/platform/ui/skip_options,PRIVATE,no,no,GET-,com.kronos.integrationhub.restservices.api.IhubSkipOptionsRestService
/v1/platform/ui/template_parameter,PRIVATE,no,no,GET-,com.kronos.integrationhub.restservices.api.IhubTemplateParamRestService
/v1/platform/user_interface,PUBLIC,yes,yes,GET-/links,com.kronos.falcon.api.rest.services.menuitem.MenuLinkDimensionRestService
/v1/platform/workflow/business_processes,PUBLIC,yes,yes,"GET-,POST-/execute,POST-/tasks/forms",com.kronos.bpm.bpmmanager.api.restservice.WorkflowBPRestService
/v1/platform/workflow/database,PRIVATE,no,no,"GET-/designer,GET-/engine",com.kronos.bpm.tenantprovisioning.api.restservice.BpmWorkflowRestService
/v1/platform/workflow/designer,PRIVATE,no,no,GET-/status,com.kronos.configapp.processmanager.restservice.BPMDesignerStatusRestService
/v1/platform/workflow/designer,PRIVATE,no,no,"GET-/domain,GET-/path",com.kronos.configapp.processmanager.restservice.BPMDesignerRestService
/v1/platform/workflow/engine,PRIVATE,no,no,GET-/status,com.kronos.configapp.processmanager.restservice.BPMEngineStatusRestService
/v1/platform/workflow/models,PRIVATE,no,no,"DELETE-/{id},GET-/,GET-/categoryname/{categoryName},GET-/deploy/{id},GET-/{id},POST-/,POST-/{id},POST-/{sourceModelId}/copy",com.kronos.configapp.processmanager.restservice.ProcessModelRestService
/v1/platform/workflow/models/category,PRIVATE,no,no,"GET-/{categoryName}/sdm,GET-/{categoryName}/sdm/keys,POST-/{categoryName}/sdm,PUT-/{categoryName}/sdm/{key}",com.kronos.configapp.processmanager.sdm.ProcessModelByCategorySDMRestService
/v1/platform/workflow/models/fap,PRIVATE,no,no,"GET-/create,GET-/deploy,GET-/{accessControlPoint}/{action}",com.kronos.configapp.processmanager.restservice.FAPPermissionsValidationRestService
/v1/platform/workflow/models/sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,PUT-/{key}",com.kronos.configapp.processmanager.sdm.ProcessModelSDMRestService
/v1/platform/workflow/processprofiles/sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,POST-/dependencies,PUT-/{key}",com.kronos.configapp.processmanager.sdm.ProcessProfileSDMRestService
/v1/platform/workflow/tasks,PRIVATE,no,no,"GET-/,GET-/form,POST-/form",com.kronos.bpm.bpmmanager.api.restservice.WorkflowTaskRestService
/v1/platform/workflow/workflows,PRIVATE,no,no,"GET-/,GET-/actions,GET-/goto_list,GET-/tile_list,GET-/{id}/execute,POST-/clock/execute,POST-/{id}/execute",com.kronos.bpm.bpmmanager.api.restservice.WorkflowEngineRestService
/v1/platform/workflow/workflows/category,PRIVATE,no,no,"GET-/{categoryName}/sdm,GET-/{categoryName}/sdm/keys,POST-/{categoryName}/sdm,PUT-/{categoryName}/sdm/{key}",com.kronos.configapp.processmanager.sdm.BusinessProcessByCategorySDMRestService
/v1/platform/workflow/workflows/sdm,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,PUT-/{key}",com.kronos.configapp.processmanager.sdm.BusinessProcessSDMRestService
/v1/scheduling/audits,PUBLIC,yes,yes,POST-/multi_read,com.kronos.scheduling.business.audit.rest.impl.AuditServiceForRestV1
/v1/scheduling/call_logs,PUBLIC,yes,yes,"GET-/{id},POST-,POST-/multi_create,POST-/multi_read",com.kronos.scheduling.business.schedule.rest.impl.CallLogForRestV1
/v1/scheduling/certifications,PUBLIC,yes,yes,"DELETE-/{certificationId},GET-/,GET-/{certificationId},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{certificationId}",com.kronos.scheduling.setup.skillcertification.impl.rest.CertificationSetupServicesForRestV1
/v1/scheduling/cover_requests,PUBLIC,yes,yes,"GET-/{id},GET-/{id}/rule_violations,POST-/apply_update,POST-/multi_read",com.kronos.scheduling.cover.impl.rest.CoverRequestManagerServiceForRestV1
/v1/scheduling/coverage_counting_profiles,PUBLIC,yes,yes,"GET-,GET-/assignments,GET-/{id},POST-/assignments/multi_read,POST-/multi_read",com.kronos.scheduling.setup.coveragecounting.impl.rest.CoverageCountingProfilesServiceForRestV1
/v1/scheduling/day_type_equivalences,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.timeoff.impl.rest.DayTypeEquivalenceSetupServiceForRestV1
/v1/scheduling/employee_availability_pattern_requests,PUBLIC,yes,yes,"GET-/availability_types,GET-/request_subtypes,GET-/submission_periods,GET-/{availabilityPatternRequestId},POST-,POST-/apply_update",com.kronos.scheduling.availability.api.IAvailabilityPatternRequestEmployeeServiceForRestV1
/v1/scheduling/employee_availability_requests,PUBLIC,yes,yes,"GET-/availability_types,GET-/request_subtypes,GET-/submission_periods,GET-/{availabilityRequestId},POST-,POST-/apply_update,POST-/multi_read",com.kronos.scheduling.availability.api.IAvailabilityChangeRequestEmployeeServiceForRestV1
/v1/scheduling/employee_cover_requests,PUBLIC,yes,yes,"GET-/candidate_employees,GET-/request_subtypes,GET-/shifts,GET-/submission_periods,GET-/{id},POST-,POST-/apply_update,POST-/invalidate,POST-/multi_read",com.kronos.scheduling.cover.impl.rest.CoverRequestEmployeeServiceForRestV1
/v1/scheduling/employee_holiday_patterns,PRIVATE,no,no,"POST-/apply_create,POST-/apply_update,POST-/builder/apply_update,POST-/redo_rollout",com.kronos.scheduling.business.pattern.holiday.impl.rest.EmployeeHolidayPatternServiceForRestV1
/v1/scheduling/employee_open_shift_requests,PUBLIC,yes,yes,"GET-/employees,GET-/jobs,GET-/request_subtypes,GET-/submission_periods,GET-/{openShiftRequestId},POST-,POST-/apply_read,POST-/apply_update,POST-/multi_read,POST-/open_shifts/multi_read,POST-/resources",com.kronos.scheduling.ess.impl.openshift.rest.OpenShiftRequestEmployeeServiceForRestV1
/v1/scheduling/employee_preferences,PRIVATE,no,no,GET-,com.kronos.scheduling.business.employeepreferences.impl.rest.EmployeePreferenceEmployeeBusinessServiceForRestV1
/v1/scheduling/employee_preferences_requests,PRIVATE,no,no,"POST-,PUT-/{id}",com.kronos.scheduling.business.employeepreferences.api.IEmployeePreferencesRequestEmployeeServiceForRestV1
/v1/scheduling/employee_schedule,PUBLIC,yes,yes,GET-,com.kronos.scheduling.business.schedule.rest.impl.ScheduleForEmployeeForRestV1
/v1/scheduling/employee_schedule_patterns,PUBLIC,yes,yes,"DELETE-/{id},GET-/builder,GET-/{id},POST-/apply_create,POST-/apply_update,POST-/multi_read",com.kronos.scheduling.business.pattern.esp.impl.rest.EmployeeSchedulePatternServiceForRestV1
/v1/scheduling/employee_self_schedule_requests,PUBLIC,yes,yes,"GET-/employees,GET-/jobs,GET-/request_subtypes,GET-/submission_periods,GET-/{essRequestId},POST-,POST-/apply_read,POST-/apply_update,POST-/multi_read,POST-/open_shifts,POST-/open_shifts/multi_read,POST-/shift_templates/multi_read,PUT-",com.kronos.scheduling.ess.impl.selfschedule.rest.SelfScheduleRequestEmployeeServiceForRestV1
/v1/scheduling/employee_swap,PUBLIC,yes,yes,"GET-/{swapRequestId},POST-,POST-/apply_read,POST-/apply_update,POST-/multi_read",com.kronos.scheduling.swap.impl.rest.SwapRequestEmployeeServiceForRestV1
/v1/scheduling/employee_timeoff,PUBLIC,yes,yes,"GET-/accruals,GET-/request_periods,GET-/request_subtypes,GET-/symbolic_values,GET-/{timeOffRequestId},POST-,POST-/apply_update,POST-/multi_read",com.kronos.scheduling.business.request.rest.impl.TimeOffRequestEmployeeServiceForRestV1
/v1/scheduling/employment_term_schedule_patterns,PUBLIC,yes,yes,"DELETE-/{id},GET-/builder,GET-/{id},POST-/apply_create,POST-/apply_update,POST-/multi_read",com.kronos.scheduling.business.group.gsp.impl.rest.EmploymentTermSchedulePatternServiceForRestV1
/v1/scheduling/employment_terms_schedule,PUBLIC,yes,yes,"GET-,POST-/multi_read,POST-/multi_update,POST-/status/multi_read",com.kronos.scheduling.business.employeegroupschedule.rest.impl.EmploymentTermsScheduleForManagerServiceForRestV1
/v1/scheduling/employment_terms_schedule/assignments,PRIVATE,no,no,"POST-/apply_create,POST-/apply_delete",com.kronos.scheduling.business.employeegroupschedule.rest.impl.EmploymentTermsAssignmentBusinessServiceForRestV1
/v1/scheduling/employment_terms_schedule/pay_code_edits,PUBLIC,yes,yes,"GET-/{id},POST-/apply_create,POST-/apply_delete,POST-/apply_update",com.kronos.scheduling.business.employeegroupschedule.rest.impl.EmploymentTermsPayCodeEditBusinessServiceForRestV1
/v1/scheduling/employment_terms_schedule/shifts,PUBLIC,yes,yes,"GET-/{id},POST-/apply_create,POST-/apply_delete,POST-/apply_update",com.kronos.scheduling.business.employeegroupschedule.rest.impl.EmploymentTermsShiftBusinessServiceForRestV1
/v1/scheduling/ess_calendar_settings,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/schedule_insights_widget_entities,GET-/transfer_types,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,PUT-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.esscalendar.impl.rest.ESSCalendarSettingsServiceForRestV1
/v1/scheduling/formula_activations_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.setup.metrics.impl.sdm.rest.SDMFormulaActivationServiceForRestV1
/v1/scheduling/group_schedule/assignments,PUBLIC,yes,yes,"POST-/apply_create,POST-/apply_delete",com.kronos.scheduling.business.employeegroupschedule.rest.impl.EmployeeGroupAssignmentBusinessServiceForRestV1
/v1/scheduling/group_schedule_patterns,PUBLIC,yes,yes,"DELETE-/{id},GET-/builder,GET-/{id},POST-/apply_create,POST-/apply_update,POST-/multi_read",com.kronos.scheduling.business.group.gsp.impl.rest.GroupSchedulePatternServiceForRestV1
/v1/scheduling/holiday_request_settings,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.timeoff.impl.rest.HolidayRequestSettingsSetupServiceForRestV1
/v1/scheduling/hours_categories,PRIVATE,no,no,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.metrics.impl.rest.HoursCategoriesServiceForRestV1
/v1/scheduling/hours_category_sets,PRIVATE,no,no,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.metrics.impl.rest.HoursCategorySetsServiceForRestV1
/v1/scheduling/indicators_parameter_value_types,PRIVATE,no,no,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.metrics.impl.rest.ParameterTypesServiceForRestV1
/v1/scheduling/kpi,PRIVATE,no,no,POST-/reprocess,com.kronos.scheduling.business.kpi.impl.rest.SchedulingKPIReprocessingServiceForRestV1
/v1/scheduling/kpi,PRIVATE,no,no,"POST-/apply_update,POST-/multi_read",com.kronos.scheduling.business.kpi.impl.rest.SchedulingKPIServiceForRestV1
/v1/scheduling/location_profiles_option_set,PUBLIC,yes,yes,"GET-,GET-/assignments,GET-/{id},POST-/assignments/multi_read,POST-/multi_read",com.kronos.scheduling.setup.locationprofile.impl.rest.LocationProfileOptionSetServiceForRestV1
/v1/scheduling/manager_availability_pattern_requests,PUBLIC,yes,yes,"POST-/apply_update,POST-/multi_read",com.kronos.scheduling.availability.api.IAvailabilityPatternRequestManagerServiceForRestV1
/v1/scheduling/manager_availability_requests,PUBLIC,yes,yes,"POST-/apply_update,POST-/multi_read",com.kronos.scheduling.availability.api.IAvailabilityChangeRequestManagerServiceForRestV1
/v1/scheduling/manager_preferences_requests,PRIVATE,no,no,POST-/multi_read,com.kronos.scheduling.business.employeepreferences.api.IEmployeePreferencesRequestManagerServiceForRestV1
/v1/scheduling/manager_swap,PUBLIC,yes,yes,"GET-/{swapRequestId},POST-/apply_read,POST-/apply_update,POST-/multi_read",com.kronos.scheduling.swap.impl.rest.SwapRequestManagerServiceForRestV1
/v1/scheduling/manager_tiles,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.scheduling.setup.managertile.impl.rest.ManagerTileSettingsServiceForRestV1
/v1/scheduling/metrics,PRIVATE,no,no,POST-/multi_read,com.kronos.scheduling.business.metrics.rest.impl.DataViewMetricsServiceForRestV1
/v1/scheduling/metrics_operations,PRIVATE,no,no,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.metrics.impl.rest.OperationsServiceForRestV1
/v1/scheduling/metrics_settings,PRIVATE,no,no,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.metrics.impl.rest.MetricsServiceForRestV1
/v1/scheduling/metrics_strategies,PRIVATE,no,no,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.metrics.impl.rest.MetricStrategiesServiceForRestV1
/v1/scheduling/occurrence_definitions_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.occurencedefinition.impl.sdm.SDMOccurrenceDefinitionServiceForRest
/v1/scheduling/open_shift_requests,PUBLIC,yes,yes,"GET-/similar_requests,GET-/{openShiftRequestId},POST-/apply_update,POST-/multi_read",com.kronos.scheduling.ess.impl.openshift.rest.OpenShiftRequestManagerServiceForRestV1
/v1/scheduling/partialshift_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.setup.partialshiftsettings.impl.partialshiftsettings.sdm.SDMPartialShiftSettingsServiceForRest
/v1/scheduling/pattern_template_profiles,PUBLIC,yes,yes,GET-,com.kronos.scheduling.setup.patterntemplate.impl.rest.PatternTemplateProfileServiceForRestV1
/v1/scheduling/pay_code_value_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.timeoff.impl.rest.PayCodeValueProfilesServiceForRestV1
/v1/scheduling/period_definitions,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,PUT-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.perioddefinition.rest.impl.PeriodDefinitionServiceForRestV1
/v1/scheduling/period_definitions_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.setup.perioddefinition.impl.sdm.SDMPeriodDefinitionServiceForRest
/v1/scheduling/predictive_scheduling,PUBLIC,yes,yes,"POST-/apply_read,POST-/evaluate",com.kronos.scheduling.business.schedulecompliance.rest.PredictiveSchedulingServiceForRestV1
/v1/scheduling/predictive_scheduling_bonus_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.predsched.setup.predictiveschedulingbonuses.impl.sdm.rest.SDMPredictiveSchedulingBonusServiceForRest
/v1/scheduling/predictive_scheduling_rules_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.predschedrules.impl.sdm.rest.SDMPredictiveSchedulingRuleServiceForRestV1
/v1/scheduling/proficiency_levels,PUBLIC,yes,yes,"DELETE-/{proficiencyLevelId},GET-/,GET-/{id},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{proficiencyLevelId}",com.kronos.scheduling.setup.skillcertification.impl.rest.ProficiencyLevelSetupServiceForRestV1
/v1/scheduling/request_submission_periods,PUBLIC,yes,yes,"POST-/apply_read,POST-/employee_opt_outs",com.kronos.scheduling.business.schedule.rest.impl.RequestSubmissionPeriodForRestV1
/v1/scheduling/schedule,PUBLIC,yes,yes,"GET-,POST-,POST-/changes/multi_read,POST-/multi_read,POST-/multi_update,POST-/status/multi_read",com.kronos.scheduling.business.schedule.rest.impl.ScheduleForManagerServiceForRestV1
/v1/scheduling/schedule/apply_update,PUBLIC,yes,yes,"GET-/async,GET-/{id}/response,GET-/{id}/status,POST-/async",com.kronos.scheduling.business.rest.SchedulingEnginesServiceForRestV1
/v1/scheduling/schedule/availability,PUBLIC,yes,yes,"DELETE-,DELETE-/multi_delete,GET-,POST-,POST-/multi_update",com.kronos.scheduling.business.schedule.rest.impl.AvailabilityForRestV1
/v1/scheduling/schedule/daylocks,PUBLIC,yes,yes,"POST-/apply_create,POST-/multi_delete",com.kronos.scheduling.business.schedule.rest.impl.DayLockForRestV1
/v1/scheduling/schedule/groups,PUBLIC,yes,yes,"GET-,POST-/multi_read,POST-/multi_update,POST-/status/multi_read",com.kronos.scheduling.business.employeegroupschedule.rest.impl.EmployeeGroupScheduleForManagerServiceForRestV1
/v1/scheduling/schedule/groups/pay_code_edits,PUBLIC,yes,yes,"GET-/{id},POST-/apply_create,POST-/apply_delete,POST-/apply_update",com.kronos.scheduling.business.employeegroupschedule.rest.impl.EmployeeGroupPayCodeEditBusinessServiceForRestV1
/v1/scheduling/schedule/groups/shifts,PUBLIC,yes,yes,"GET-/{id},POST-/apply_create,POST-/apply_delete,POST-/apply_update",com.kronos.scheduling.business.employeegroupschedule.rest.impl.EmployeeGroupShiftBusinessServiceForRestV1
/v1/scheduling/schedule/leave_edits,PUBLIC,yes,yes,"DELETE-/{leId},GET-/{leId},POST-,POST-/apply_create,POST-/apply_delete,POST-/apply_update,POST-/{leId}",com.kronos.scheduling.business.schedule.rest.impl.LeaveEditForRestV1
/v1/scheduling/schedule/open_shifts,PUBLIC,yes,yes,POST-/apply_read,com.kronos.scheduling.business.schedule.rest.impl.OpenShiftForRestV1
/v1/scheduling/schedule/pay_code_edits,PUBLIC,yes,yes,"DELETE-/{pceId},GET-/{pceId},POST-,POST-/apply_create,POST-/apply_import,POST-/apply_update,POST-/import,POST-/multi_create,POST-/multi_delete,POST-/multi_update,POST-/{pceId}",com.kronos.scheduling.business.schedule.rest.impl.PayCodeEditForRestV1
/v1/scheduling/schedule/schedule_tags,PUBLIC,yes,yes,"DELETE-/{tagId},GET-/{tagId},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/{tagId}",com.kronos.scheduling.business.schedule.rest.impl.ScheduleTagForRestV1
/v1/scheduling/schedule/shifts,PUBLIC,yes,yes,"DELETE-/{shiftId},GET-/{shiftId},POST-,POST-/apply_create,POST-/apply_update,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/{shiftId}",com.kronos.scheduling.business.schedule.rest.impl.ShiftForRestV1
/v1/scheduling/schedule_builder_settings,PUBLIC,yes,yes,"GET-/,GET-/{id},PUT-/{id}",com.kronos.scheduling.setup.schedulebuilder.impl.rest.ScheduleBuilderServiceForRestV1
/v1/scheduling/schedule_builder_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,PUT-/{key}",com.kronos.scheduling.setup.schedulebuilder.impl.rest.ScheduleBuilderSDMServiceForRestV1
/v1/scheduling/schedule_change_criteria_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.predsched.setup.predictiveschedulingcriteria.impl.sdm.rest.SDMScheduleChangeCriteriaServiceForRest
/v1/scheduling/schedule_event_rules_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.scheduleeventrule.impl.sdm.SDMScheduleEventRuleServiceForRest
/v1/scheduling/schedule_events_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.scheduleevent.impl.sdm.SDMScheduleEventServiceForRest
/v1/scheduling/schedule_generation_profile,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{schedGenEngineSettingsProfileId},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{schedGenEngineSettingsProfileId}",com.kronos.scheduling.setup.schedgenenginesettings.impl.rest.SchedGenEngineSettingsProfileServiceForRestV1
/v1/scheduling/schedule_generation_setup,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/location_assignments,GET-/settings,GET-/settings/{paramName}/values,GET-/setups_assignments,GET-/{schedGenEngineSettingsId},POST-,POST-/location_assignments/multi_read,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/setups_assignments/multi_read,PUT-/{schedGenEngineSettingsId}",com.kronos.scheduling.setup.schedgenenginesettings.impl.rest.SchedGenEngineSettingsServiceForRestV1
/v1/scheduling/schedule_group_patterns,PUBLIC,yes,yes,"DELETE-/{id},GET-/builder,GET-/{id},POST-/apply_create,POST-/apply_update,POST-/multi_read",com.kronos.scheduling.business.group.gsp.impl.rest.ScheduleGroupPatternServiceForRestV1
/v1/scheduling/schedule_management_actions,PUBLIC,yes,yes,"POST-/apply_update,POST-/combined_notifications/apply_update,POST-/multi_read,POST-/notifications/apply_update",com.kronos.scheduling.business.schedule.rest.impl.PostScheduleServiceForRestV1
/v1/scheduling/schedule_metrics,PUBLIC,yes,yes,POST-/multi_read,com.kronos.scheduling.business.metrics.rest.impl.MetricsServiceForRestV1
/v1/scheduling/schedule_pattern_templates,PUBLIC,yes,yes,"DELETE-,DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,PUT-,PUT-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.patterntemplate.impl.rest.SchedulePatternsServiceForRestV1
/v1/scheduling/schedule_pattern_templates_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.setup.patterntemplate.impl.sdm.rest.SDMSchedulePatternsServiceForRestV1
/v1/scheduling/schedule_planner_profile,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,PUT-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.scheduleplanner.impl.rest.SchedulePeriodsServiceForRestV1Deprecated
/v1/scheduling/schedule_planner_settings,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,PUT-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.scheduleplanner.impl.rest.SchedulePlannerSettingsServiceForRestV1
/v1/scheduling/schedule_rule_sets/location_assignments,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-/apply_delete,POST-/multi_delete,POST-/multi_upsert,PUT-",com.kronos.scheduling.setup.scheduleruleset.impl.rest.RuleSetLocationAssignmentsServiceForRestV1
/v1/scheduling/schedule_zone_sets,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/assignments/multi_read,POST-/multi_read",com.kronos.scheduling.setup.workloadspan.impl.rest.ScheduleZoneSetForRestV1
/v1/scheduling/scheduling_group_profiles,PUBLIC,yes,yes,GET-,com.kronos.scheduling.setup.schedulegroupprofile.impl.rest.ScheduleGroupProfileServiceForRestV1
/v1/scheduling/school_calendar_profiles,PUBLIC,yes,yes,GET-,com.kronos.scheduling.setup.schoolcalendarprofile.impl.rest.SchoolCalendarProfileServiceForRestV1
/v1/scheduling/self_schedule_requests,PUBLIC,yes,yes,POST-/multi_read,com.kronos.scheduling.ess.impl.selfschedule.rest.SelfScheduleRequestManagerServiceForRestV1
/v1/scheduling/setup/availability_pattern_requirements,PUBLIC,no,no,"DELETE-/{id},GET-/name/{name},GET-/{id},POST-,POST-/multi_create,POST-/multi_read,PUT-/{id}",com.kronos.scheduling.availabilitypatternrequirements.impl.rest.AvailabilityPatternRequirementsServiceForRestV1
/v1/scheduling/setup/employee_visibility_periods,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/apply_read,POST-/multi_read",com.kronos.scheduling.setup.requestsubmission.impl.rest.EmployeeVisibilityPeriodsServiceForRestV1
/v1/scheduling/setup/employee_visibility_periods_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.requestsubmission.impl.sdm.rest.SDMEmployeeVisibilityPeriodsServiceForRestV1
/v1/scheduling/setup/indicators,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.metrics.impl.rest.IndicatorsServiceForRestV1
/v1/scheduling/setup/openshift_recommendations_messages,PRIVATE,no,no,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.ess.impl.openshiftrecommendationssettings.rest.OpenShiftRecommendationsMessagesServiceForRestV1
/v1/scheduling/setup/openshift_recommendations_settings,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_read,PUT-/{id}",com.kronos.scheduling.setup.ess.impl.openshiftrecommendationssettings.rest.OpenShiftRecommendationsSettingsServiceForRestV1
/v1/scheduling/setup/openshift_recommendations_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.setup.ess.impl.openshiftrecommendationssettings.sdm.rest.SDMOpenShiftRecommendationsSettingsServiceForRestV1
/v1/scheduling/setup/partial_shifts,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.partialshiftsettings.impl.partialshiftsettings.rest.PartialShiftSettingsServiceForRestV1
/v1/scheduling/setup/predictive_scheduling_rules,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.predschedrules.rest.impl.PredictiveSchedulingRulesServiceForRestV1
/v1/scheduling/setup/request_submission_trigger_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.requestsubmissiontrigger.impl.sdm.rest.SDMRequestSubmissionTriggerSettingsServiceForRestV1
/v1/scheduling/setup/request_subtypes,PUBLIC,yes,yes,"GET-/,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.requestsubtype.impl.rest.RequestSubTypesServiceForRestV1
/v1/scheduling/setup/schedule_event_rules,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.scheduleeventrule.impl.rest.ScheduleEventRuleServiceForRestV1
/v1/scheduling/setup/schedule_generation_engine_strategies,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/settings,GET-/strategies_assignments,GET-/{id},GET-/{strategy_id}/location_assignments,POST-,POST-/location_assignments/apply_update,POST-/location_assignments/multi_read,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/strategies_assignments/multi_read,PUT-/{strategy_id}",com.kronos.scheduling.setup.schedulegen.impl.rest.ScheduleGenerationEngineStrategiesServiceForRestV1
/v1/scheduling/setup/schedule_groups,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.schedulegroup.impl.rest.ScheduleGroupsServiceForRestV1
/v1/scheduling/setup/schedule_periods,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.scheduleperiod.impl.rest.SchedulePeriodsServiceForRestV1
/v1/scheduling/setup/schedule_planner_profiles,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.scheduleplanner.impl.rest.SchedulePlannerProfileServiceForRestV1
/v1/scheduling/setup/staffing_dashboard_settings,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/colorized_entities,GET-/employee_pool_additional_data_entities,GET-/schedule_change_notifications,GET-/unit_panel_additional_data_entities,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.staffingdashboard.impl.rest.StaffingDashboardSettingsServiceForRestV1
/v1/scheduling/setup/staffing_dashboard_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.staffingdashboard.impl.rest.sdm.SDMStaffingDashboardSettingsServiceForRestV1
/v1/scheduling/setup/staffing_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.staffingdashboard.impl.rest.StaffingDashboardProfileServiceForRestV1
/v1/scheduling/setup/staffing_profiles_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.staffingdashboard.impl.rest.sdm.SDMStaffingDashboardProfileServiceForRest
/v1/scheduling/setup/swap_recommendations_messages,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.swap.impl.swaprecommendationssettings.rest.SwapRecommendationsMessagesServiceForRestV1
/v1/scheduling/setup/swap_recommendations_settings,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_read,PUT-/{id}",com.kronos.scheduling.setup.swap.impl.swaprecommendationssettings.rest.SwapRecommendationsSettingsServiceForRestV1
/v1/scheduling/setup/swap_recommendations_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.setup.swap.impl.swaprecommendationssettings.sdm.rest.SDMSwapRecommendationsSettingsServiceForRestV1
/v1/scheduling/setup/tag_definitions,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_read,POST-/{id},PUT-/{id}",com.kronos.scheduling.setup.tagdefinition.rest.impl.TagDefinitionServiceForRestV1
/v1/scheduling/setup/team_definition,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_read,PUT-/{id}",com.kronos.scheduling.setup.teamdefinition.rest.impl.TeamDefinitionServiceForRestV1
/v1/scheduling/setup/team_definition/absence_quota_overrides,PUBLIC,yes,yes,"POST-/apply_read,POST-/apply_upsert",com.kronos.scheduling.setup.teamdefinition.rest.impl.AbsenceQuotaOverrideServiceForRestV1
/v1/scheduling/setup/team_definition_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.setup.teamdefinition.impl.sdm.rest.SdmTeamDefinitionServiceForRestV1
/v1/scheduling/setup/visibility_period_actions,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.requestsubmissiontrigger.impl.rest.RequestSubmissionTriggerSettingsServiceForRestV1
/v1/scheduling/setup/workload_planner_profiles,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.workloadplanner.impl.rest.WorkloadPlannerProfilesServiceForRest
/v1/scheduling/shift_profiles,PUBLIC,yes,yes,"DELETE-/{shiftProfileId},GET-,GET-/{shiftProfileId},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{shiftProfileId}",com.kronos.scheduling.setup.shiftprofile.impl.rest.ShiftProfilesServiceForRestV1
/v1/scheduling/shift_templates,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_read,PUT-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.shifttemplate.impl.rest.ShiftTemplatesServiceForRestV1
/v1/scheduling/shift_templates_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.shifttemplateprofile.impl.rest.ShiftTemplateProfileServiceForRestV1
/v1/scheduling/shift_templates_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.shifttemplate.impl.rest.ShiftTemplatesSDMServiceForRestV1
/v1/scheduling/skill_certification_profiles,PUBLIC,yes,yes,"DELETE-/{profileId},GET-,GET-/{id},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.skillcertification.impl.rest.SkillCertificationProfilesServiceForRestV1
/v1/scheduling/skill_requirements_settings,PRIVATE,no,no,"POST-,POST-/multi_create",com.kronos.scheduling.setup.skillrequirements.impl.rest.SkillRequirementsSettingsServiceForRestV1
/v1/scheduling/skills,PUBLIC,yes,yes,"DELETE-/{skillId},GET-/,GET-/{skillId},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{skillId}",com.kronos.scheduling.setup.skillcertification.impl.rest.SkillSetupServicesForRestV1
/v1/scheduling/staffing_assistant,PUBLIC,yes,yes,POST-/apply_read,com.kronos.services.schedulingengines.proceduresetengine.rest.ProcedureSetEngineForRestV1
/v1/scheduling/staffing_matrices,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.workloadspan.impl.rest.StaffingMatrixForRestV1
/v1/scheduling/standard_shift_set/standard_shift_set_assignment,PRIVATE,no,no,POST-/multi_read,com.kronos.scheduling.setup.workloadspan.impl.rest.StandardShiftSetAssignmentForRestV1
/v1/scheduling/standard_shift_sets,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.workloadspan.impl.rest.StandardShiftSetRestForRestV1
/v1/scheduling/standard_shift_sets/assignments,PUBLIC,yes,yes,"POST-/apply_delete,POST-/apply_read,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert",com.kronos.scheduling.setup.workloadspan.impl.rest.StandardShiftSetAssignmentRestForRestV1
/v1/scheduling/symbolic_source_types,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.timeoff.impl.rest.SymbolicSourceTypesSetupServiceForRestV1
/v1/scheduling/tag_definitions_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.setup.tagdefinition.impl.sdm.rest.SDMTagDefinitionServiceForRestV1
/v1/scheduling/time_off_request_guided_recommendations,PUBLIC,yes,yes,POST-/apply_read,com.kronos.scheduling.business.guidedrecs.impl.rest.GuidedRecommendationServiceForRestV1
/v1/scheduling/timeoff,PUBLIC,yes,yes,"GET-/accruals,GET-/request_periods,GET-/request_subtypes,GET-/symbolic_values,GET-/{timeOffRequestId},POST-,POST-/apply_create,POST-/apply_update,POST-/multi_read,PUT-/{timeOffRequestId}",com.kronos.scheduling.business.request.rest.impl.TimeOffRequestManagerServiceForRestV1
/v1/scheduling/tor_recommendations,PUBLIC,no,no,GET-,com.kronos.scheduling.widget.staffingagent.api.ITimeOffRequestRecommendationRestService
/v1/scheduling/violations,PUBLIC,yes,yes,"POST-/,POST-/evaluate",com.kronos.scheduling.business.rule.rest.api.IScheduleRuleViolationActionServiceRest
/v1/scheduling/violations_what_if,PRIVATE,no,no,POST-/,com.kronos.scheduling.business.rule.rest.api.IWhatIfRuleViolationsServiceRest
/v1/scheduling/volume,PUBLIC,yes,yes,"POST-/apply_update,POST-/multi_read",com.kronos.scheduling.business.workload.volume.impl.rest.VolumeServiceForRestV1
/v1/scheduling/work_weeks,PRIVATE,no,no,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.workweek.impl.rest.WorkWeeksServiceForRestV1
/v1/scheduling/worker_types,PUBLIC,yes,yes,GET-,com.kronos.scheduling.setup.workertype.impl.rest.WorkerTypeServiceForRestV1
/v1/scheduling/workload,PRIVATE,no,no,"GET-/workloads,POST-/generate_workload,POST-/multi_read,POST-/pattern/current_jobs_span_sets,POST-/pattern/location_span_sets,POST-/pattern/save,POST-/stable_spans",com.kronos.scheduling.business.workload.workload.impl.rest.WorkloadServiceForRestV1
/v1/scheduling/workload/patterns,PRIVATE,no,no,"GET-/,POST-/pattern/delete,POST-/pattern/update",com.kronos.scheduling.business.workload.workload.impl.rest.WorkloadPatternsServiceForRestV1
/v1/scheduling/workload_calendars,PUBLIC,yes,yes,"POST-/multi_delete,POST-/multi_read,POST-/multi_upsert",com.kronos.scheduling.business.workload.workload.impl.rest.WorkloadOverrideBulkServiceForRestV1
/v1/scheduling/workload_coverage,PUBLIC,yes,yes,"POST-/coverage/multi_read,POST-/coverage_detail/multi_read,POST-/coverage_span/multi_read,POST-/workload/multi_read,POST-/workload_coverage_details/multi_read,POST-/workload_detail/multi_read",com.kronos.scheduling.business.coverage.rest.impl.WorkloadCoverageServiceForRestV1
/v1/scheduling/workload_generator,PUBLIC,yes,yes,POST-/apply_update,com.kronos.scheduling.business.workload.workload.impl.rest.WorkloadGeneratorServiceForRestV1
/v1/scheduling/workload_patterns,PUBLIC,yes,yes,"POST-/multi_delete,POST-/multi_read,POST-/multi_upsert",com.kronos.scheduling.business.workload.workload.impl.rest.WorkloadBulkPatternsServiceForRestV1
/v1/scheduling/workload_planner_profile,PRIVATE,no,no,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.workloadplanner.impl.rest.WorkloadPlannerProfileServiceForRestV1
/v1/scheduling/workload_planner_settings,PRIVATE,no,no,,com.kronos.scheduling.setup.workloadplanner.impl.rest.WorkloadPlannerSettingsServiceForRestV1
/v1/scheduling/zone_categories,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.workloadspan.impl.rest.ZoneCategoryServiceForRestV1
/v1/scheduling/zone_set/zone_set_assignment,PRIVATE,no,no,POST-/multi_read,com.kronos.scheduling.setup.workloadspan.impl.rest.ScheduleZoneSetAssignmentForRestV1
/v1/session,PUBLIC,no,no,GET-/getWFMSessionInfo,com.kronos.wfc.rest.services.api.IRestWFMSessionInfoProvider
/v1/swagger_relationship,PUBLIC,yes,yes,,com.kronos.rest.documentation.navigation.SwaggerTagRelationshipManager
/v1/ta/failed_import_transactions,PRIVATE,no,no,GET-/fap,com.kronos.commonapp.transactionassistant.fap.api.TAIntegrationsActionsRestService
/v1/ta/failed_import_transactions,PRIVATE,no,no,"DELETE-,GET-,GET-/retrieve,POST-,POST-/resubmit",com.kronos.commonapp.transactionassistant.IRestTAService
/v1/ta/failed_import_transactions/process,PRIVATE,no,no,POST-,com.kronos.commonapp.transactionassistant.IRestTAIntegrationService
/v1/tenant/bulk,PRIVATE,no,no,"POST-/bulkUpgradeSiteUrl/{action},POST-/{action}",com.kronos.rest.developerportal.BulkUpgardeService
/v1/timekeeping,PUBLIC,yes,yes,POST-/punches/apply_read,com.kronos.timekeeping.service.punch.impl.rest.PunchExtractServiceForRest
/v1/timekeeping,PUBLIC,yes,yes,POST-/paycodes_to_accrual_codes/multi_read,com.kronos.timekeeping.service.accruals.impl.rest.AccrualServiceForRest
/v1/timekeeping,PUBLIC,yes,yes,GET-/attestation_unnaproved_timecard_data,com.kronos.timekeeping.service.attestation.impl.rest.AttestationTimecardUnapproveRestService
/v1/timekeeping,PUBLIC,yes,yes,GET-/attestation_profile_buttons,com.kronos.timekeeping.service.attestation.impl.rest.AttestationProfileButtonsRestService
/v1/timekeeping,PUBLIC,yes,yes,POST-/punches/import,com.kronos.timekeeping.service.punch.impl.rest.BulkPunchServiceForRest
/v1/timekeeping,PUBLIC,yes,yes,"GET-/pending_historical_corrections/compute/async,GET-/pending_historical_corrections/compute/{id}/results,GET-/pending_historical_corrections/compute/{id}/status,POST-/pending_historical_corrections/compute/async",com.kronos.timekeeping.service.calculatetotals.impl.rest.IBulkComputeCorrectionsServiceForRest
/v1/timekeeping,PUBLIC,yes,yes,GET-/attestation_unapproved_timecard_data,com.kronos.timekeeping.service.attestation.impl.rest.AttestationTimecardDataRestService
/v1/timekeeping,PUBLIC,yes,yes,"GET-/pending_historical_corrections/save/async,GET-/pending_historical_corrections/save/{id}/results,GET-/pending_historical_corrections/save/{id}/status,POST-/pending_historical_corrections/save/async",com.kronos.timekeeping.service.calculatetotals.impl.rest.IBulkSaveCorrectionsServiceForRest
/v1/timekeeping/absence_spans,PUBLIC,yes,yes,POST-/multi_read,com.kronos.timekeeping.service.absencespan.impl.rest.AbsenceSpanRestService
/v1/timekeeping/accruals,PUBLIC,yes,yes,"POST-/payouts,POST-/resets,POST-/suspensions",com.kronos.timekeeping.service.accruals.impl.rest.bulkimport.BulkAccrualImportServiceForRest
/v1/timekeeping/accruals/moves,PUBLIC,yes,yes,POST-,com.kronos.timekeeping.service.accruals.impl.rest.AccrualBalanceMoveServiceForRest
/v1/timekeeping/accruals/updates,PUBLIC,yes,yes,POST-,com.kronos.timekeeping.service.accruals.impl.rest.update.BulkAccrualUpdateServiceForRest
/v1/timekeeping/attestation,PUBLIC,yes,yes,"POST-/displayed_forms,POST-/submitted_forms",com.kronos.timekeeping.service.attestation.impl.rest.AttestationFormRestService
/v1/timekeeping/attestation,PUBLIC,yes,yes,POST-/multi_read,com.kronos.timekeeping.service.attestation.impl.rest.AttestationDataRestService
/v1/timekeeping/attestation_answers,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/setup/action_types,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.timekeeping.service.attestation.impl.rest.AttestationAnswerRestService
/v1/timekeeping/attestation_assignments,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.timekeeping.service.attestation.impl.rest.AttestationAssignmentRestService
/v1/timekeeping/attestation_button_display_types,PUBLIC,yes,yes,GET-/,com.kronos.timekeeping.service.attestation.api.service.AttestationButtonDisplayTypeRestService
/v1/timekeeping/attestation_button_sub_types,PUBLIC,yes,yes,GET-/,com.kronos.timekeeping.service.attestation.api.service.AttestationButtonSubTypeRestService
/v1/timekeeping/attestation_button_types,PUBLIC,yes,yes,GET-/,com.kronos.timekeeping.service.attestation.api.service.AttestationButtonTypeRestService
/v1/timekeeping/attestation_buttons,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.timekeeping.service.attestation.impl.rest.AttestationButtonRestService
/v1/timekeeping/attestation_conditions,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.timekeeping.service.attestation.impl.rest.AttestationConditionRestService
/v1/timekeeping/attestation_manual_entry_process,PUBLIC,yes,yes,POST-,com.kronos.timekeeping.service.attestation.impl.rest.AttestationManualEntryProcessRestService
/v1/timekeeping/attestation_manual_entry_process/complete,PUBLIC,yes,yes,POST-,com.kronos.timekeeping.service.attestation.impl.rest.AttestationManualEntryProcessCompleteRestService
/v1/timekeeping/attestation_models,PUBLIC,yes,yes,GET-,com.kronos.timekeeping.service.attestation.impl.rest.AttestationModelRestService
/v1/timekeeping/attestation_process,PUBLIC,yes,yes,POST-,com.kronos.timekeeping.service.attestation.impl.rest.AttestationProcessExecutionRestService
/v1/timekeeping/attestation_process/complete,PUBLIC,yes,yes,POST-,com.kronos.timekeeping.service.attestation.impl.rest.AttestationProcessCompleteRestService
/v1/timekeeping/attestation_process/offline,PUBLIC,yes,yes,POST-,com.kronos.timekeeping.service.attestation.impl.rest.AttestationOfflineProcessRestService
/v1/timekeeping/attestation_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.timekeeping.service.attestation.impl.rest.AttestationProfileRestService
/v1/timekeeping/attestation_questions,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/setup/question_display_types,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.timekeeping.service.attestation.impl.rest.AttestationQuestionRestService
/v1/timekeeping/attestation_template_mappings,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.timekeeping.service.attestation.impl.rest.AttestationTemplateMappingRestService
/v1/timekeeping/attestation_workflow,PUBLIC,yes,yes,"GET-,GET-/like",com.kronos.timekeeping.service.attestation.impl.rest.AttestationWorkflowRestService
/v1/timekeeping/attestation_workflow_attributes,PUBLIC,yes,yes,POST-/{id},com.kronos.timekeeping.service.attestation.impl.rest.AttestationWorkflowAttributesRestService
/v1/timekeeping/brazil_compliance,PUBLIC,yes,yes,"GET-/identity_types,GET-/identity_types/{id}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.IIdentityTypeServiceForRestV1
/v1/timekeeping/brazil_compliance,PUBLIC,yes,yes,"DELETE-/device_groups/{id},GET-/device_groups,GET-/device_groups/{id},POST-/device_groups,PUT-/device_groups/{id}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.IDeviceGroupServiceForRestV1
/v1/timekeeping/brazil_compliance,PUBLIC,yes,yes,GET-/system_settings,com.kronos.timekeeping.brazil.compliance.setup.api.rest.IBrazilSystemSettingsRestService
/v1/timekeeping/brazil_compliance,PUBLIC,yes,yes,"DELETE-/paycode_attribute_definitions/{id},GET-/paycode_attribute_definition_keys,GET-/paycode_attribute_definition_keys/{id},GET-/paycode_attribute_definitions,GET-/paycode_attribute_definitions/{id},POST-/paycode_attribute_definitions,PUT-/paycode_attribute_definitions/{id}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.IPaycodeAttributeServiceForRestV1
/v1/timekeeping/brazil_compliance,PUBLIC,yes,yes,"DELETE-/devices/{id},GET-/devices,GET-/devices/{id},POST-/devices,PUT-/devices/{id}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.IDeviceServiceForRestV1
/v1/timekeeping/brazil_compliance,PUBLIC,yes,yes,"DELETE-/companies/{id},GET-/companies,GET-/companies/{id},POST-/companies,PUT-/companies/{id}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.ICompanyServiceForRestV1
/v1/timekeeping/brazil_compliance/audits/,PUBLIC,yes,yes,POST-/apply_read,com.kronos.timekeeping.service.audit.impl.rest.BrazilAuditServiceForRest
/v1/timekeeping/brazil_compliance/reports,PUBLIC,yes,yes,POST-/audits/apply_read,com.kronos.timekeeping.brazil.compliance.setup.api.rest.IBrazilAuditReportServiceForRestV1
/v1/timekeeping/brazil_compliance/reports,PUBLIC,yes,yes,POST-/sign,com.kronos.timekeeping.brazil.compliance.setup.api.rest.IBRCDigitalSignatureForRestV1
/v1/timekeeping/brazil_compliance/setup,PRIVATE,no,no,"GET-/sdm_companies,GET-/sdm_companies/keys,POST-/sdm_companies,POST-/sdm_companies/dependencies,PUT-/sdm_companies/{key}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.ISDMCompanyRestService
/v1/timekeeping/brazil_compliance/setup,PRIVATE,no,no,"GET-/sdm_device_groups,GET-/sdm_device_groups/keys,POST-/sdm_device_groups,POST-/sdm_device_groups/dependencies,PUT-/sdm_device_groups/{key}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.ISDMDeviceGroupRestService
/v1/timekeeping/brazil_compliance/setup,PRIVATE,no,no,"GET-/sdm_paycode_attribute_definitions,GET-/sdm_paycode_attribute_definitions/keys,POST-/sdm_paycode_attribute_definitions,POST-/sdm_paycode_attribute_definitions/dependencies,PUT-/sdm_paycode_attribute_definitions/{key}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.ISDMPaycodeAttributeRestService
/v1/timekeeping/brazil_compliance/setup,PRIVATE,no,no,"DELETE-/mdui_paycode_attribute_definitions/{id},GET-/mdui_paycode_attribute_definition_keys,GET-/mdui_paycode_attribute_definition_keys/{id},GET-/mdui_paycode_attribute_definitions,GET-/mdui_paycode_attribute_definitions/{id},POST-/mdui_paycode_attribute_definitions,PUT-/mdui_paycode_attribute_definitions/{id}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.IMDUIIPaycodeAttributeRestService
/v1/timekeeping/brazil_compliance/setup,PRIVATE,no,no,"DELETE-/mdui_device_groups/{id},GET-/mdui_device_groups,GET-/mdui_device_groups/{id},POST-/mdui_device_groups,PUT-/mdui_device_groups/{id}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.IMDUIDeviceGroupRestService
/v1/timekeeping/brazil_compliance/setup,PRIVATE,no,no,"DELETE-/mdui_devices/{id},GET-/mdui_devices,GET-/mdui_devices/{id},POST-/mdui_devices,PUT-/mdui_devices/{id}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.IMDUIDeviceRestService
/v1/timekeeping/brazil_compliance/setup,PRIVATE,no,no,"DELETE-/mdui_companies/{id},GET-/mdui_companies,GET-/mdui_companies/{id},POST-/mdui_companies,PUT-/mdui_companies/{id}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.IMDUICompanyRestService
/v1/timekeeping/brazil_compliance/setup,PRIVATE,no,no,"GET-/mdui_identity_types,GET-/mdui_identity_types/{id}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.IMDUIIdentityTypeRestService
/v1/timekeeping/brazil_compliance/setup,PRIVATE,no,no,"GET-/sdm_devices,GET-/sdm_devices/keys,POST-/sdm_devices,POST-/sdm_devices/dependencies,PUT-/sdm_devices/{key}",com.kronos.timekeeping.brazil.compliance.setup.api.rest.ISDMDeviceRestService
/v1/timekeeping/data,PUBLIC,yes,yes,POST-/import,com.kronos.timekeeping.service.compositeitem.impl.rest.CompositeItemServiceForRest
/v1/timekeeping/employee_timecard,PUBLIC,yes,yes,"GET-/,POST-/,POST-/multi_read",com.kronos.timekeeping.service.timecard.impl.rest.TimeCardEmployeeRestService
/v1/timekeeping/employee_timecard_approvals,PUBLIC,yes,yes,"DELETE-,GET-,POST-",com.kronos.timekeeping.service.approval.impl.rest.EmployeeApprovalServiceForRest
/v1/timekeeping/employee_timecard_signoffs,PUBLIC,yes,yes,"DELETE-/,GET-/,POST-/",com.kronos.timekeeping.service.signoff.impl.rest.SignOffServiceForEmployeeForRest
/v1/timekeeping/employee_timecards,PRIVATE,no,no,"GET-/,POST-/",com.kronos.timekeeping.service.timecard.impl.rest.EmployeeTimecardsRestService
/v1/timekeeping/enable_edits,PUBLIC,yes,yes,POST-/import,com.kronos.timekeeping.service.enableedits.impl.rest.BulkEnableEditsServiceForRest
/v1/timekeeping/kiosk_instance_logins/apply_read,PUBLIC,yes,yes,POST-/,com.kronos.commonbusiness.kiosk.rest.service.api.KioskLoginRestServicePublic
/v1/timekeeping/most_recently_used_transfers,PUBLIC,yes,yes,GET-,com.kronos.timekeeping.service.timestamp.impl.rest.TransferServiceForRest
/v1/timekeeping/pay_code_edits,PUBLIC,yes,yes,"GET-/import/async,GET-/import/async/{execution_key}/response,GET-/import/async/{execution_key}/status,POST-/import/async",com.kronos.timekeeping.service.paycodeedit.api.rest.BulkPayCodeAsyncRestService
/v1/timekeeping/pay_code_edits,PUBLIC,yes,yes,"POST-/import,POST-/multi_delete",com.kronos.timekeeping.service.paycodeedit.impl.rest.BulkPayCodeEditServiceForRest
/v1/timekeeping/sdm_attestation_answers,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.timekeeping.service.attestation.impl.rest.SDMAttestationAnswerRestService
/v1/timekeeping/sdm_attestation_assignments,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.timekeeping.service.attestation.impl.rest.SDMAttestationAssignmentRestService
/v1/timekeeping/sdm_attestation_buttons,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.timekeeping.service.attestation.impl.rest.SDMAttestationButtonRestService
/v1/timekeeping/sdm_attestation_conditions,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.timekeeping.service.attestation.impl.rest.SDMAttestationConditionRestService
/v1/timekeeping/sdm_attestation_profiles,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.timekeeping.service.attestation.impl.rest.SDMAttestationProfileRestService
/v1/timekeeping/sdm_attestation_questions,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.timekeeping.service.attestation.impl.rest.SDMAttestationQuestionRestService
/v1/timekeeping/sdm_attestation_template_mappings,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.timekeeping.service.attestation.impl.rest.SDMAttestationTemplateMappingRestService
/v1/timekeeping/sdm_attestation_workflows,PRIVATE,no,no,GET-/like,com.kronos.timekeeping.service.attestation.impl.rest.SDMAttestationWorkflowRestService
/v1/timekeeping/services/activitiesaddon,PRIVATE,no,no,GET-/data,com.kronos.activities.widget.timecard.addon.service.rest.IActivitiesAddOnServiceForREST
/v1/timekeeping/services/hyperfind,PRIVATE,no,no,"GET-/getHyperFindQueriesForCurrentUser,GET-/getHyperFindQueriesWithAdHoc,POST-/getEmployees,POST-/getEmployeesForCurrentUser,POST-/getHyperFindQueries,POST-/getLightEmployeeSurrogatesByLicenseForCurrentUser,POST-/getLightEmployeeSurrogatesForCurrentUser",com.kronos.tk.timecard.widget.services.hyperfind.internal.HyperFindService
/v1/timekeeping/services/orgset,PRIVATE,no,no,"DELETE-/{id},GET-/,GET-/create,GET-/getActiveProfiles,POST-/,POST-/activeProfiles/{id},POST-/createOnDate,PUT-/editOnDate/{id},PUT-/{id}",com.kronos.tk.timecard.widget.services.orgset.IOrganizationSetService
/v1/timekeeping/services/timeFrame,PRIVATE,no,no,"GET-/getAllTimeFrameTypes,GET-/getAllTimeFrames,GET-/getTimeFrame,GET-/getTimeFrames,POST-/getTimeFrameDateSpan",com.kronos.tk.timecard.widget.services.timeframe.ITimeFrameService
/v1/timekeeping/services/timestamp,PRIVATE,no,no,"GET-/getConfigurations,GET-/lastPunchTime,GET-/latestUsedTransfers,GET-/latestUsedTransfersByGeolocation,POST-/recordQuickTimestamp,POST-/recordTimestamp",com.kronos.tk.timecard.widget.services.timekeeping.ITimestampService
/v1/timekeeping/services/tokens,PRIVATE,no,no,"GET-/isTokenValid/{token},GET-/verifyToken/{token},POST-/createToken,POST-/releaseToken/{token}",com.kronos.tk.timecard.widget.services.tokens.TokenServiceAdapterForRest
/v1/timekeeping/setup,PUBLIC,yes,yes,"DELETE-/pay_codes/{id},GET-/employee_pay_code_symbolic_values,GET-/employee_pay_code_symbolic_values/{id},GET-/employee_pay_codes,GET-/employee_pay_codes/{id},GET-/pay_code_symbolic_values,GET-/pay_code_symbolic_values/{id},GET-/pay_codes,GET-/pay_codes/{id},GET-/paycodes,GET-/paycodes/{id},POST-/pay_codes,POST-/pay_codes/multi_create,POST-/pay_codes/multi_delete,POST-/pay_codes/multi_read,POST-/pay_codes/multi_update,POST-/pay_codes/multi_upsert,PUT-/pay_codes/{id}",com.kronos.timekeeping.setup.paycodes.api.IPayCodeServiceForRest
/v1/timekeeping/setup,PUBLIC,yes,yes,"GET-/break_rules,GET-/break_rules/{id}",com.kronos.timekeeping.setup.breakrules.impl.rest.BreakRuleServiceForRest
/v1/timekeeping/setup,PUBLIC,yes,yes,"GET-/paycodes/combined/,GET-/paycodes/combined/{id},POST-/paycodes/combined/apply_read,POST-/paycodes/combined/apply_upsert",com.kronos.timekeeping.setup.paycodes.api.restservice.ICombinedPaycodeRestService
/v1/timekeeping/setup/adjustment_rules,PUBLIC,yes,yes,"DELETE-/{id},GET-,POST-,POST-/apply_read,POST-/versions/apply_upsert,POST-/{id},PUT-/{id}",com.kronos.timekeeping.setup.adjustmentrule.api.restservices.AdjustmentRuleRestService
/v1/timekeeping/setup/auto_signoff,PUBLIC,yes,yes,GET-/,com.kronos.timekeeping.service.signoff.impl.rest.SetupAutoSignoffServiceForRest
/v1/timekeeping/setup/employment_terms,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_read,POST-/versions/apply_upsert,PUT-/{id}",com.kronos.timekeeping.setup.employmentterms.impl.rest.EmploymentTermServiceForRest
/v1/timekeeping/setup/fixed_rules,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/multi_upsert,PUT-/{id}",com.kronos.timekeeping.setup.fixedrules.api.service.rest.FixedRuleRestService
/v1/timekeeping/setup/kiosk_instances,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/mine,GET-/{id},POST-/,POST-/copy/{id},PUT-/,PUT-/{id}",com.kronos.commonbusiness.kiosk.rest.service.api.KioskInstanceAPI
/v1/timekeeping/setup/kiosks,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-/,PUT-/,PUT-/{id}",com.kronos.commonbusiness.kiosk.rest.service.api.KioskConfigAPI
/v1/timekeeping/setup/mdui_exception_categories,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/markAsReviewed,GET-/{id},POST-,PUT-/{id}",com.kronos.timekeeping.exception.tile.impl.rest.MDUIExceptionCategoryRestService
/v1/timekeeping/setup/mdui_exception_tiles,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/call_to_actions/{type},GET-/pending_changes_entity_types,GET-/periods,GET-/refs,GET-/{id},POST-/,PUT-/{id}",com.kronos.timekeeping.exception.tile.impl.rest.MDUIExceptionTileRestService
/v1/timekeeping/setup/overtime_rules,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.timekeeping.setup.overtimerules.api.rest.OvertimeRulesRestService
/v1/timekeeping/setup/overtime_rules/available_for_alerts,PRIVATE,no,no,GET-/{alertTypeId},com.kronos.timekeeping.setup.alertprofile.impl.rest.OvertimeRulesForAlertProfileRestService
/v1/timekeeping/setup/pay_code_distributions,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/apply_upsert,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.timekeeping.setup.paycodedistributions.impl.rest.PayCodeDistributionServiceForRestV1
/v1/timekeeping/setup/pay_code_tags,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.timekeeping.setup.paycodetag.api.rest.IPayCodeTagRestService
/v1/timekeeping/setup/pay_codes/data_access_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},GET-/{id}/available,POST-,POST-/apply_read,POST-/apply_upsert,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.kronos.timekeeping.setup.paycodedap.api.restservice.IPayCodeDAPRestService
/v1/timekeeping/setup/pay_codes/gdap,PUBLIC,yes,yes,GET-,com.kronos.timekeeping.setup.paycodedap.api.restservice.IPayCodeDAPGdapPaycodesRestService
/v1/timekeeping/setup/payrules,PUBLIC,yes,yes,"DELETE-/{id},GET-,POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.timekeeping.setup.payrules.api.restservice.IPayRuleRestService
/v1/timekeeping/setup/percentage_allocation_rules,PUBLIC,yes,yes,"DELETE-/{id},GET-,POST-,POST-/multi_read,POST-/{id},PUT-/{id}",com.kronos.timekeeping.setup.fpa.api.restservices.FPARuleRestService
/v1/timekeeping/setup/prevailing_wages/,PUBLIC,yes,yes,"GET-,POST-/multi_upsert",com.kronos.timekeeping.service.prevailingwage.api.rest.PrevailingWageRestService
/v1/timekeeping/setup/sdm_adjustment_rules,PRIVATE,no,no,"GET-/,GET-/keys,POST-/,POST-/dependencies,PUT-/{key}",com.kronos.timekeeping.setup.adjustmentrule.api.restservices.SDMAdjustmentRuleRestService
/v1/timekeeping/setup/sdm_muster_roll_leave_codes,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.timekeeping.setup.musterrollleavecodes.impl.rest.SDMMusterRollLeaveCodesRestService
/v1/timekeeping/setup/sdm_pay_code_tag,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.timekeeping.setup.paycodetag.impl.rest.SDMPayCodeTagRestService
/v1/timekeeping/setup/sdm_percentage_allocation_rules,PRIVATE,no,no,"GET-/,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.timekeeping.setup.fpa.api.restservices.ISDMFPARuleRestService
/v1/timekeeping/setup/sdm_transfer_rules,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.timekeeping.setup.transferrules.api.rest.ITransferRuleSDMRestService
/v1/timekeeping/setup/time_off_rules,PUBLIC,yes,yes,GET-,com.kronos.timekeeping.setup.timeoffrules.impl.rest.TimeOffServiceForRest
/v1/timekeeping/setup/timecard_addon_columns,PUBLIC,yes,yes,GET-,com.kronos.timekeeping.setup.timecardconfiguration.impl.rest.TimecardAddOnColumnRestService
/v1/timekeeping/setup/timecard_addon_groups,PUBLIC,yes,yes,GET-,com.kronos.timekeeping.setup.timecardconfiguration.impl.rest.TimecardAddOnGroupByTypeRestService
/v1/timekeeping/setup/timecard_addon_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},GET-/{id},POST-,POST-,PUT-/{id},PUT-/{id}",com.kronos.timekeeping.setup.timecardconfiguration.impl.rest.TimecardAddOnProfileRestService
/v1/timekeeping/setup/timecard_addon_types,PUBLIC,yes,yes,GET-,com.kronos.timekeeping.setup.timecardconfiguration.impl.rest.TimecardAddOnTypeRestService
/v1/timekeeping/setup/timecard_settings,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},GET-/{id},POST-,POST-,POST-/multi_read,PUT-/{id},PUT-/{id}",com.kronos.timekeeping.setup.timecardconfiguration.impl.rest.TimecardSettingRestService
/v1/timekeeping/setup/timekeeping_alert_profile,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/alertTypes,GET-/conditionTypes,GET-/metadata,GET-/notifications,GET-/parameterTypes,GET-/parameters,GET-/{id},POST-,PUT-/{id}",com.kronos.timekeeping.setup.alertprofile.impl.rest.AlertProfileRestService
/v1/timekeeping/setup/timekeeping_alert_profile/sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.timekeeping.setup.alertprofile.impl.rest.AlertProfileSDMRestService
/v1/timekeeping/setup/timekeeping_alert_profiles,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.timekeeping.setup.alertprofile.impl.rest.AlertProfilesRestService
/v1/timekeeping/setup/work_rule/data_access_profiles,PUBLIC,yes,yes,"GET-,GET-/{id},GET-/{id}/availableitems,POST-/multi_read",com.ukg.timekeeping.setup.workruledap.api.restservice.IWorkRuleDAPRestService
/v1/timekeeping/signoff_preparation,PUBLIC,yes,yes,"DELETE-/,GET-/,POST-/,POST-/multi_delete,POST-/multi_read,POST-/multi_update",com.kronos.timekeeping.service.signoff.impl.rest.SignOffPreparationRest
/v1/timekeeping/signoffs,PUBLIC,yes,yes,POST-/import,com.kronos.timekeeping.service.signoff.impl.legacy.rest.BulkSignOffServiceForRest
/v1/timekeeping/timecard,PUBLIC,yes,yes,"GET-/,POST-/,POST-/multi_read",com.kronos.timekeeping.service.timecard.impl.rest.TimeCardManagerRestService
/v1/timekeeping/timecard/changes,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_update,PUT-/{id}",com.kronos.timekeeping.service.timecardchange.impl.rest.TimecardChangesRestService
/v1/timekeeping/timecard/changes/reports/,PUBLIC,yes,yes,POST-/multi_read,com.kronos.timekeeping.service.timecardchange.impl.rest.report.TimecardChangeReportRestService
/v1/timekeeping/timecard_annotations,PUBLIC,yes,yes,"DELETE-/,GET-/,POST-/",com.kronos.timekeeping.core.service.timecardannotation.impl.rest.restpublic.TimecardAnnotationServiceForPublicRest
/v1/timekeeping/timecard_approvals,PUBLIC,yes,yes,"DELETE-,GET-,POST-",com.kronos.timekeeping.service.approval.impl.rest.ManagerApprovalServiceForRest
/v1/timekeeping/timecard_data,PRIVATE,no,no,"GET-/,POST-/multi_read,POST-/multi_read_target_hours",com.kronos.timekeeping.service.timecarddata.impl.legacy.rest.TimeCardDataRestService
/v1/timekeeping/timecard_metrics,PUBLIC,yes,yes,POST-/multi_read,com.kronos.timekeeping.service.timecarddata.impl.rest.TimeCardDataRestService
/v1/timekeeping/timecard_signoffs,PUBLIC,yes,yes,"DELETE-/,GET-/,POST-/",com.kronos.timekeeping.service.signoff.impl.rest.SignOffServiceForManagerForRest
/v1/timekeeping/timecards,PRIVATE,no,no,"GET-/,GET-/employee_positions,POST-/,POST-/multi_read",com.kronos.timekeeping.service.timecard.impl.rest.TimeCardRestService
/v1/timekeeping/timestamps,PUBLIC,yes,yes,"GET-,GET-/servertime,POST-",com.kronos.timekeeping.service.timestamp.impl.rest.TimestampsServiceForRest
/v1/timekeeping/widget/service/timestamp,PRIVATE,no,no,POST-/,com.kronos.tk.timecard.widget.services.timekeeping.IWidgetTimestampService
/v1/timezones,PRIVATE,no,no,"GET-/,GET-/name,GET-/now/employee/{employeeId},GET-/now/location/{id},GET-/now/person,GET-/now/tenant/,GET-/now/user/,GET-/timezone/employee/{employeeId},GET-/timezone/location/{id},GET-/timezone/person,GET-/timezone/tenant,GET-/timezone/user,GET-/today/employee/{employeeId},GET-/today/location/{id},GET-/today/person,GET-/today/tenant,GET-/today/user,GET-/{id}",com.kronos.commonapp.timezone.rest.impl.TimeZoneForRestInternalV1Deprecated
/v1/totalizer/extensibility/processor,PRIVATE,no,no,"DELETE-/{id},GET-/,GET-/{id},POST-/,PUT-/{id}",com.kronos.wfc.totalizing.business.extensibility.rest.ExtensibilityProcessorController
/v1/totalizer/extensibility/processor/rule,PRIVATE,no,no,"DELETE-/{id},GET-/,GET-/{id},POST-/,PUT-/{id}",com.kronos.wfc.totalizing.business.extensibility.rest.ProcessorToRuleController
/v1/ultipro/payroll/data/apply_read,PUBLIC,no,no,POST-/,com.kronos.ultipropayroll.aggregation.restservices.api.UltiProPayrollAggregationRest
/v1/work/activities,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/exports/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/multi_upsert,PUT-/{id}",com.kronos.activities.setup.activity.rest.ActivityServiceForRestV1
/v1/work/activities/setup,PUBLIC,yes,yes,"GET-/activity_types,GET-/complete_statuses,GET-/data_access_types,GET-/eligible_default_activities,GET-/event_types,GET-/held_histories,GET-/hours_allocation_types,GET-/priority_types,GET-/process_types,GET-/quantity_allocation_types,GET-/sequence_validation_types",com.kronos.activities.setup.activity.rest.ActivityResourceServiceForV1
/v1/work/activities_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.activity.rest.ActivitySDMServiceForRestV1
/v1/work/activity_efficiencies,PUBLIC,yes,yes,POST-/multi_read,com.kronos.activities.business.efficiency.impl.rest.ActivityEfficiencyServiceForRestV1
/v1/work/activity_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.profile.rest.ProfileServiceForRestV1
/v1/work/activity_profiles/setup,PUBLIC,yes,yes,"GET-/future_validation_types,GET-/tracking_status_types,GET-/variance_check_types,GET-/work_rules",com.kronos.activities.setup.profile.rest.ProfileResourceServiceForRestV1
/v1/work/activity_shifts,PUBLIC,yes,yes,POST-/multi_read,com.kronos.activities.business.timekeeping.timecard.impl.shift.rest.ActivityShiftRestService
/v1/work/activity_shifts,PUBLIC,yes,yes,"POST-/multi_delete,POST-/multi_upsert",com.kronos.activities.business.timekeeping.services.impl.rest.BulkActivitySegmentServiceForRestV1
/v1/work/activity_shifts/historical_net_changes,PUBLIC,yes,yes,POST-/multi_read,com.kronos.activities.business.netchange.impl.rest.HistoricalNetChangeRestService
/v1/work/activity_shifts/net_changes,PUBLIC,yes,yes,POST-/multi_read,com.kronos.activities.business.netchange.impl.rest.NetChangeRestService
/v1/work/activity_totals,PUBLIC,yes,yes,"GET-/,POST-/multi_read",com.kronos.activities.business.timekeeping.services.impl.rest.ActivityTotalServiceRest
/v1/work/activity_transactions,PUBLIC,yes,yes,POST-/multi_read,com.kronos.activities.business.export.impl.transactions.rest.ActivityTransactionExportRestService
/v1/work/customers,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.customer.rest.CustomerServiceForRestV1
/v1/work/customers_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.customer.rest.CustomerSDMServiceForRestV1
/v1/work/employee_activities,PUBLIC,yes,yes,POST-/multi_read,com.kronos.activities.setup.activity.rest.EmployeeActivityAssignmentsServiceForRestV1
/v1/work/field_definitions,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.fielddefinition.rest.FieldDefinitionServiceForRestV1
/v1/work/field_definitions/setup,PUBLIC,yes,yes,GET-/types,com.kronos.activities.setup.fielddefinition.rest.FieldDefinitionResourceServiceForRestV1
/v1/work/field_definitions_mdui_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.fielddefinition.rest.FieldDefinitionSDMServiceForRestV1
/v1/work/form_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.formprofile.rest.FormProfileServiceForRestV1
/v1/work/form_profiles_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.formprofile.rest.FormProfileSDMServiceForRestV1
/v1/work/forms,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.form.rest.FormServiceForRestV1
/v1/work/forms/setup,PUBLIC,yes,yes,"GET-/control_types,GET-/entry_types,GET-/form_types,GET-/input_source_types,GET-/offline_forms,GET-/validation_types",com.kronos.activities.setup.form.rest.FormResourceServiceForRestV1
/v1/work/forms_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.form.rest.FormSDMServiceForRestV1
/v1/work/group_edit,PRIVATE,no,no,"POST-/assign_activities,POST-/assign_activity,POST-/create_duration_segment,POST-/create_segment,POST-/logoff_all_activities,POST-/stop_segment,POST-/unassign_activities,POST-/unassign_activity",com.kronos.activities.business.groupedit.api.rest.ActivitiesGroupEditRestService
/v1/work/move_quantities/audit,PUBLIC,yes,yes,POST-/multi_read,com.kronos.activities.business.movequantity.audit.rest.MoveQuantityAuditRestService
/v1/work/pay_code_actions,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.activityaction.rest.ActivityActionServiceForRestV1
/v1/work/pay_code_actions_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.activityaction.rest.ActivityActionSDMServiceForRestV1
/v1/work/profiles_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.profile.rest.ProfileSDMServiceForRestV1
/v1/work/queries,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.query.rest.QueryServiceForRestV1
/v1/work/queries/setup,PUBLIC,yes,yes,"GET-/date_types,GET-/query_types",com.kronos.activities.setup.query.rest.QueryResourceServiceForV1
/v1/work/queries_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.query.rest.QuerySDMServiceForRestV1
/v1/work/query_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.queryprofile.rest.QueryProfileServiceForRestV1
/v1/work/query_profiles_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.queryprofile.rest.QueryProfileSDMServiceForRestV1
/v1/work/result_code_profiles,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.resultcodeprofile.rest.ResultCodeProfileServiceForRestV1
/v1/work/result_code_profiles_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.resultcodeprofile.rest.ResultCodeProfileSDMServiceForRestV1
/v1/work/result_codes,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/multi_upsert,PUT-/{id}",com.kronos.activities.setup.resultcode.rest.ResultCodeServiceForRestV1
/v1/work/results_templates,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.form.rest.ResultsTemplateServiceForRestV1
/v1/work/results_templates/setup,PUBLIC,yes,yes,GET-/step_types,com.kronos.activities.setup.form.rest.ResultsTemplateResourceServiceForRestV1
/v1/work/results_templates_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.form.rest.ResultsTemplateSDMServiceForRestV1
/v1/work/settings,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.settings.rest.SettingsServiceForRestV1
/v1/work/settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.settings.rest.SettingsSDMServiceForRestV1
/v1/work/setup/resources,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.activities.setup.resource.rest.ResourceServiceForRestV1
/v1/work/setup/resources/queries,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,PUT-/{id}",com.kronos.activities.setup.resourcequery.rest.ResourceQueryServiceForRestV1
/v1/work/setup/resources/queries_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.resourcequery.rest.ResourceQuerySDMServiceForRestV1
/v1/work/team_segments,PUBLIC,yes,yes,"POST-/multi_delete,POST-/multi_read,POST-/multi_upsert",com.kronos.activities.business.team.services.impl.rest.ActivityTeamSegmentRestService
/v1/work/team_segments/active_members,PUBLIC,yes,yes,POST-/multi_read,com.kronos.activities.business.team.services.impl.rest.ActiveTeamMemberRestService
/v1/work/team_segments/audits,PUBLIC,yes,yes,POST-/multi_read,com.kronos.activities.business.team.audit.rest.TeamSegmentAuditRestService
/v1/work/team_segments/net_changes,PUBLIC,yes,yes,POST-/apply_read,com.kronos.activities.business.teamsegment.netchange.rest.TeamSegmentNetChangeRestService
/v1/work/team_transactions,PUBLIC,yes,yes,POST-/multi_read,com.kronos.activities.business.export.impl.teamtransactions.rest.TeamTransactionExportRestService
/v1/work/teams,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-/{id}",com.kronos.activities.setup.team.rest.ActivityTeamDefinitionServiceForRestV1
/v1/work/teams/setup,PUBLIC,yes,yes,GET-/days_of_week,com.kronos.activities.setup.team.rest.ActivityTeamDefinitionResourceServiceForRestV1
/v1/work/teams_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.team.rest.ActivityTeamDefinitionSDMServiceForRestV1
/v1/work/units_of_measure,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.activities.setup.unitofmeasure.rest.UnitOfMeasureServiceForRestV1
/v1/work/units_of_measure_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-,POST-/dependencies,PUT-/{key},PUT-/{key}",com.kronos.activities.setup.unitofmeasure.rest.UnitOfMeasureSDMServiceForRestV1
/v2/auth0/migration,PRIVATE,no,no,POST-/toggle-migration-flag,com.ukg.auth0.migration.api.rest.IRestAuth0MigrationFlagTrigger
/v2/auth0/migration,PUBLIC,yes,yes,GET-/users/export,com.ukg.auth0.migration.api.rest.IRestAuth0MigrationUserDetails
/v2/auth0/migration,PUBLIC,yes,yes,GET-/tenant-policies,com.ukg.auth0.migration.api.rest.IRestAuth0MigrationConfig
/v2/auth0/migration-mode,PUBLIC,yes,yes,POST-,com.ukg.auth0.migration.api.rest.IRestAuth0FlagToggle
/v2/commons/cost_centers,PUBLIC,yes,yes,"GET-,GET-/{id}",com.kronos.commonapp.laborcategory.setup.impl.rest.CostCenterServiceForRestV2
/v2/commons/hours_operation,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/versions/apply_delete,PUT-/{id}",com.kronos.commonbusiness.hoursofoperation.rest.HoursOfOperationServiceForRestV2
/v2/commons/labor_categories,PUBLIC,yes,yes,"GET-,GET-/{id}",com.kronos.commonapp.laborcategory.setup.impl.rest.LaborCategoryServiceForRestV2
/v2/commons/labor_category_lists,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/apply_read,POST-/multi_create,POST-/versions/apply_upsert",com.kronos.commonapp.laborcategory.setup.impl.rest.LaborCategoryListServiceForRestV2
/v2/commons/labor_entries,PUBLIC,yes,yes,"GET-,GET-/{id}",com.kronos.commonapp.laborcategory.setup.impl.rest.LaborCategoryEntryServiceForRestV2
/v2/commons/location_sets,PUBLIC,yes,yes,POST-/multi_read,com.kronos.commonapp.orgmap.rest.impl.OrgMapLocationSetsForRestV2
/v2/commons/locations,PUBLIC,yes,yes,POST-/multi_read,com.kronos.commonapp.orgmap.rest.impl.OrgMapLocationsForRestV2
/v2/commons/notifications,PUBLIC,no,no,"DELETE-/cache/{cacheKey},GET-",com.kronos.controlcenter.api.restservice.notifications.NotificationRestServiceV2
/v2/commons/persons/minor_rules,PUBLIC,yes,yes,"GET-,GET-/{personId},POST-/multi_read,POST-/multi_upsert,PUT-/{person_id}",com.kronos.scheduling.setup.scheduleruleset.impl.rest.minorrule.assignment.PersonMinorRuleAssignmentServiceForRestV2
/v2/commons/persons/paycode_value_profiles,PUBLIC,yes,yes,"DELETE-/{personId},GET-,GET-/{personId},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{personId}",com.kronos.scheduling.setup.timeoff.impl.rest.assignment.PayCodeValueProfileAssignmentServiceForRestV2
/v2/commons/persons/percentage_allocation_rules,PUBLIC,no,no,"DELETE-/,GET-/,GET-/multi_read,GET-/{personId},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/",com.kronos.persons.rest.assignments.service.IRestPercentageAllocationRuleAssignmentV2
/v2/commons/persons/schedule_rule_overrides,PUBLIC,yes,yes,"GET-,GET-/{person_id},POST-/multi_read",com.kronos.scheduling.setup.scheduleruleset.impl.rest.RuleEmployeeOverridesServiceForRestV2
/v2/commons/persons/scheduling_employee_preferences,PUBLIC,yes,yes,"GET-,GET-/{personId},POST-/multi_read",com.kronos.scheduling.setup.workloadspan.impl.rest.EmployeePreferencesAssignmentForRestV2
/v2/forecasting/forecast_week,PUBLIC,yes,yes,GET-,com.kronos.forecasting.setup.forecastweek.rest.ForecastWeekServiceForRestV2
/v2/forecasting/forecast_week,PUBLIC,yes,yes,"GET-/default_start_day,GET-/start_day,POST-/start_days",com.kronos.forecasting.rest.ForecastWeekServiceForRestV2
/v2/forecasting/generic_categories,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.forecasting.setup.genericcategory.rest.GenericCategoryServiceForRestV2
/v2/forecasting/labor_forecast_limits,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-,POST-/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_update,POST-/versions/apply_delete,PUT-/{id}",com.kronos.forecasting.setup.laborforecastlimit.rest.LaborForecastLimitServiceForRestV2
/v2/forecasting/labor_forecast_limits/template,PRIVATE,no,no,"POST-/,POST-/items,POST-/jobs",com.kronos.forecasting.setup.laborforecastlimit.rest.LaborForecastLimitTemplateServiceForRestV2
/v2/forecasting/labor_standard_tasks,PUBLIC,yes,yes,"POST-/import,POST-/purge",com.kronos.forecasting.laborstandard.tasks.rest.LaborStandardServiceTaskForRestV2
/v2/forecasting/labor_standards/template,PUBLIC,no,no,"POST-,POST-/labor_period_elements,POST-/standard_distribution,POST-/time_scale_items",com.kronos.forecasting.laborstandard.rest.LaborStandardTemplateServiceForRestV2
/v2/forecasting/machine_learning_models,PUBLIC,yes,yes,"DELETE-,DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.forecasting.setup.machinelearning.rest.MachineLearningModelServiceForRestV2
/v2/forecasting/static_drivers,PUBLIC,yes,yes,"GET-,GET-/{id}",com.kronos.forecasting.setup.labordriver.rest.StaticDriverServiceForRestV2
/v2/forecasting/task_groups,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/apply_read,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/versions/apply_delete,PUT-/{id}",com.kronos.forecasting.setup.taskgroup.rest.TaskGroupServiceForRestV2
/v2/forecasting/task_groups/template,PRIVATE,no,no,"POST-,POST-/org_jobs,POST-/tasks",com.kronos.forecasting.setup.taskgroup.rest.TaskGroupTemplateServiceForRestV2
/v2/forecasting/tasks,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/versions/apply_delete,PUT-/{id}",com.kronos.forecasting.setup.task.rest.TaskServiceForRestV2
/v2/forecasting/tasks/template,PRIVATE,no,no,"POST-,POST-/labor_standards",com.kronos.forecasting.setup.task.rest.TaskTemplateServiceForRestV2
/v2/forecasting/volume_forecast,PUBLIC,yes,yes,POST-/import,com.kronos.forecasting.volumeforecast.rest.VolumeForecastServiceForRestV2
/v2/forecasting/volume_forecast_model_types,PUBLIC,yes,yes,GET-,com.kronos.forecasting.masterdata.volumeforecastmodeltype.rest.VolumeForecastModelTypeServiceForRestV2
/v2/forecasting/week_symbolic_periods,PUBLIC,yes,yes,GET-,com.kronos.forecasting.setup.weeksymbolicperiod.rest.WeekSymbolicPeriodServiceForRestV2
/v2/leave/case_statuses,PUBLIC,yes,yes,GET-/,com.kronos.leave.service.common.api.rest.referencedata.LeaveCaseStatusRestServiceV2
/v2/scheduling/setup/schedule_groups,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.schedulegroup.impl.rest.ScheduleGroupsServiceForRestV2
/v2/scheduling/violations,PUBLIC,yes,yes,POST-/evaluate,com.kronos.scheduling.business.rule.rest.api.IScheduleRuleViolationAndUpdateServiceRest
/v2/timekeeping/setup,PUBLIC,yes,yes,"GET-/employee_pay_code_symbolic_values,GET-/employee_pay_code_symbolic_values/{id},GET-/employee_pay_codes,GET-/employee_pay_codes/{id},GET-/pay_code_symbolic_values,GET-/pay_code_symbolic_values/{id},GET-/pay_codes",com.kronos.timekeeping.setup.paycodes.api.IPayCodeServiceForRestV2
/v2/timekeeping/setup,PUBLIC,yes,yes,"GET-/break_rules,GET-/break_rules/{id}",com.kronos.timekeeping.setup.breakrules.impl.rest.BreakRuleServiceForRestV2
/v2/timekeeping/setup/employment_terms,PUBLIC,yes,yes,"GET-,GET-/{id}",com.kronos.timekeeping.setup.employmentterms.impl.rest.EmploymentTermServiceForRestV2
/v2/timekeeping/setup/payrules,PUBLIC,yes,yes,"GET-,POST-",com.kronos.timekeeping.setup.payrules.api.restservice.IPayRuleRestServiceV2
/v2/user/search,PUBLIC,no,no,POST-,com.ukg.auth0.migration.api.rest.IRestForgotUsername
scheduling/v1/widget/manager/timeoffrequests,PRIVATE,no,no,"GET-/contextual_resources,GET-/static_resources,GET-/{id},POST-/accruals/preview,POST-/multi_approve,POST-/multi_cancel,POST-/multi_pending,POST-/multi_read,POST-/multi_reject,POST-/read_statuses,POST-/{id}/approve,POST-/{id}/cancel,POST-/{id}/edit,POST-/{id}/pending,POST-/{id}/reject,PUT-",com.kronos.scheduling.widget.timeoffrequest.api.ITimeOffRequestForManagerActionServiceForRest
v1/commons/notifications/actions,PUBLIC,no,no,POST-/resync,com.kronos.inbox.api.restservice.UnifiedInboxReSyncRestService
v1/commons/persons/assignments,PUBLIC,yes,yes,"GET-,GET-/names,GET-/{id},POST-/multi_read,POST-/multi_upsert",com.kronos.people.aggregation.assignment.rest.AssignmentAggregationRest
v1/commons/persons/minor_rules,PUBLIC,yes,yes,"GET-,GET-/{personId},POST-/multi_read,POST-/multi_upsert,PUT-",com.kronos.scheduling.setup.scheduleruleset.impl.rest.minorrule.assignment.PersonMinorRuleAssignmentServiceForRestV1
v1/commons/persons/pay_code_value_profiles,PUBLIC,yes,yes,"DELETE-/{personId},GET-,GET-/{personId},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{personId}",com.kronos.scheduling.setup.timeoff.impl.rest.assignment.PayCodeValueProfileAssignmentServiceForRestV1
v1/commons/persons/schedule_rule_overrides,PUBLIC,yes,yes,"DELETE-/{schedule_rule_override_id},GET-,GET-/{person_id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_upsert,PUT-",com.kronos.scheduling.setup.scheduleruleset.impl.rest.RuleEmployeeOverridesServiceForRestV1
v1/commons/persons/scheduling_employee_preferences,PUBLIC,yes,yes,"GET-,GET-/{personId},POST-/multi_read,POST-/multi_upsert,PUT-",com.kronos.scheduling.setup.workloadspan.impl.rest.EmployeePreferencesAssignmentForRestV1
v1/commons/persons/scheduling_employee_preferences/schedule_generation_overrides,PUBLIC,yes,yes,"GET-,GET-/{personId},POST-/multi_read,POST-/multi_upsert,PUT-/{personId}",com.kronos.scheduling.setup.employeepreferences.impl.rest.EmpSchedGenOverridesPrefrencesForRestV1
v1/commons/persons/worker_types,PUBLIC,yes,yes,"DELETE-/{personId},GET-,GET-/{personId},POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{personId}",com.kronos.scheduling.setup.workertype.impl.rest.WorkerTypeAssignmentForRestV1
v1/commons/sdm/custom_tiles,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.commonapp.customtile.rest.CustomTileSDMRestService
v1/requests,PRIVATE,no,no,POST-/item/resume,com.kronos.request.rest.ResumeRequestRestService
v1/scheduling/ess_calendar_profile,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,PUT-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.esscalendar.impl.rest.ESSCalendarProfileServiceForRestV1
v1/scheduling/ess_calendar_profile_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.setup.esscalendar.impl.sdm.esscalendarprofile.ESSCalendarProfileServiceSDMForRestV1
v1/scheduling/ess_calendar_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.esscalendar.impl.sdm.esscalendarsettings.ESSCalendarSettingsServiceSDMForRestV1
v1/scheduling/export,PUBLIC,yes,yes,GET-/shiftsAndPaycode,com.kronos.scheduling.data.export.rest.ScheduleDataExportRestV1
v1/scheduling/fill_open_shifts,PUBLIC,yes,yes,"POST-/accept,POST-/decline,POST-/notify",com.kronos.scheduling.business.fillopenshift.impl.rest.FillOpenShiftForRestV1
v1/scheduling/manager_tiles_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.setup.managertile.impl.sdm.ManagerTileServiceSDMForRestV1
v1/scheduling/minor_rule_sets,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.scheduleruleset.impl.rest.minorrule.MinorRulesServiceForRestV1
v1/scheduling/procedure_sets,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.sortmatch.impl.rest.ProcedureSetsServiceForRestV1
v1/scheduling/schedule/maintenance,PRIVATE,no,no,"POST-/apply_delete,POST-/apply_update",com.kronos.scheduling.business.schedulemaintenance.impl.rest.ScheduleMaintenanceServiceRest
v1/scheduling/schedule_planner_profile_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,PUT-/{key}",com.kronos.scheduling.setup.scheduleplanner.impl.sdm.scheduleplannerprofile.SchedulePlannerProfileServiceSDMForRestV1
v1/scheduling/schedule_planner_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.scheduleplanner.impl.sdm.scheduleplannersettings.SchedulePlannerSettingsServiceSDMForRestV1
v1/scheduling/schedule_rule_sets,PUBLIC,yes,yes,"DELETE-/{ruleSetId},GET-,GET-/{id},POST-/,POST-/multi_read,PUT-/{ruleSetId}",com.kronos.scheduling.setup.scheduleruleset.impl.rest.RuleSetsServiceForRestV1
v1/scheduling/schedule_rules,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.scheduleruleset.impl.rest.RulesServiceForRestV1
v1/scheduling/schedule_score,PUBLIC,yes,yes,POST-/multi_read,com.kronos.scheduling.business.schedulescore.impl.rest.ScheduleScoreServiceForRestV1
v1/scheduling/schedule_score_definitions_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.schedulescore.impl.sdm.rest.SDMScheduleScoreDefinitionServiceForRest
v1/scheduling/schegen_engine_settings_profile_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.schedgenenginesettings.impl.sdm.SchedGenEngineSettingsProfileSDMServiceForRestV1
v1/scheduling/schegen_engine_settings_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.schedgenenginesettings.impl.sdm.SchedGenEngineSettingsSDMServiceForRestV1
v1/scheduling/school_calendars,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.schoolcalendar.impl.rest.SchoolCalendarServiceForRestV1
v1/scheduling/score_factors_sdm,PRIVATE,no,no,"GET-,GET-/keys,PUT-/{key}",com.kronos.scheduling.setup.schedulescore.impl.sdm.rest.SDMFactorActivationServiceForRest
v1/scheduling/setup/ess_calendar_profiles,PUBLIC,yes,yes,"GET-,GET-/{id},POST-/multi_read",com.kronos.scheduling.setup.esscalendar.impl.rest.ESSCalendarProfileServiceForRest
v1/scheduling/setup/incentive_definition,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.scheduleincentive.impl.rest.ScheduleIncentiveDefinitionServiceForRestV1
v1/scheduling/setup/occurrence_definitions,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.occurencedefinition.impl.rest.impl.OccurrenceDefinitionServiceForRestV1
v1/scheduling/setup/predictive_scheduling_bonus,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.predsched.setup.predictiveschedulingbonuses.rest.impl.PredictiveSchedulingBonusServiceForRestV1
v1/scheduling/setup/schedule_change_criteria,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,POST-/{id}",com.kronos.scheduling.predsched.setup.predictiveschedulingcriteria.rest.impl.ScheduleChangeCriteriaServiceForRestV1
v1/scheduling/setup/schedule_events,PUBLIC,yes,yes,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.scheduleevent.impl.rest.ScheduleEventServiceForRestV1
v1/scheduling/setup/schedule_incentive_definition_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.scheduleincentive.impl.scheduleincentivedefinition.sdm.rest.SDMScheduleIncentiveDefinitionServiceForRestV1
v1/scheduling/setup/schedule_incentive_profiles,PRIVATE,no,no,"DELETE-/{id},GET-,GET-/{id},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.scheduleincentive.impl.rest.ScheduleIncentiveProfileServiceForRestV1
v1/scheduling/setup/schedule_incentive_profiles_sdm,PRIVATE,no,no,"GET-,GET-/keys,POST-,POST-/dependencies,PUT-/{key}",com.kronos.scheduling.setup.scheduleincentive.impl.scheduleincentiveprofile.sdm.rest.SDMScheduleIncentiveProfileServiceForRestV1
v1/scheduling/setup/schedule_score_definitions,PUBLIC,yes,yes,"DELETE-/{id},GET-/,GET-/{id},POST-/,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{id}",com.kronos.scheduling.setup.schedulescore.impl.rest.ScheduleScoreDefinitionServiceForRestV1
v1/scheduling/setup/score_factors,PUBLIC,yes,yes,"GET-,GET-/{id},PUT-/{id}",com.kronos.scheduling.setup.schedulescore.impl.rest.FactorActivationServiceForRestV1
v1/scheduling/shift_profile_sets,PUBLIC,yes,yes,"DELETE-/{shiftProfileSetId},GET-,GET-/{shiftProfileSetId},POST-,POST-/multi_create,POST-/multi_delete,POST-/multi_read,POST-/multi_update,PUT-/{shiftProfileSetId}",com.kronos.scheduling.setup.shiftprofile.impl.rest.ShiftProfileSetsServiceForRestV1
v1/scheduling/team_absence,PRIVATE,no,no,POST-/multi_read,com.kronos.scheduling.business.guidedrecs.impl.rest.TeamAbsenceServiceRest
v1/timekeeping,PUBLIC,yes,yes,"DELETE-/exception_tiles/{id},GET-/exception_tiles,GET-/exception_tiles/{id},POST-/exception_tiles,POST-/exception_tiles/multi_create,POST-/exception_tiles/multi_delete,POST-/exception_tiles/multi_update,PUT-/exception_tiles/{id}",com.kronos.timekeeping.exception.tile.impl.rest.ExceptionTileServiceForRestV1
v1/timekeeping,PUBLIC,yes,yes,"DELETE-/exception_categories/{id},GET-/exception_categories,GET-/exception_categories/{id},GET-/exception_types,POST-/exception_categories,POST-/exception_categories/multi_create,POST-/exception_categories/multi_delete,POST-/exception_categories/multi_read,POST-/exception_categories/multi_update,PUT-/exception_categories/{id}",com.kronos.timekeeping.exception.tile.impl.rest.ExceptionCategoryServiceForRestV1
v1/timekeeping/approvals,PUBLIC,yes,yes,"POST-/import,POST-/multi_delete",com.kronos.timekeeping.service.approval.impl.rest.BulkApprovalServiceForRest
v1/timekeeping/setup,PUBLIC,yes,yes,"DELETE-/full_work_rules/{id},GET-/employee_work_rules,GET-/employee_work_rules/{id},GET-/full_work_rules,GET-/full_work_rules/{id},GET-/work_rules,GET-/work_rules/{id},POST-/full_work_rules,POST-/full_work_rules/multi_create,POST-/full_work_rules/multi_delete,POST-/full_work_rules/multi_read,POST-/full_work_rules/multi_update,PUT-/full_work_rules/{id}",com.kronos.timekeeping.setup.workrules.impl.rest.WorkRuleServiceForRest
v1/timekeeping/setup,PUBLIC,yes,yes,"GET-/deduct_rules,GET-/deduct_rules/{id}",com.kronos.timekeeping.setup.bonusdeductionrules.impl.rest.BonusDeductionServiceForRest
v1/timekeeping/setup,PUBLIC,yes,yes,"GET-/accrual_profiles,GET-/accrual_profiles/{id},POST-/accrual_profiles/multi_read",com.kronos.timekeeping.setup.accrualprofiles.impl.rest.AccrualProfileServiceForRest
v1/timekeeping/setup,PUBLIC,yes,yes,"DELETE-/combination_rules/{id},GET-/combination_rules,GET-/combination_rules/{id},POST-/combination_rules,POST-/combination_rules/apply_upsert,POST-/combination_rules/multi_create,POST-/combination_rules/multi_delete,POST-/combination_rules/multi_read,POST-/combination_rules/multi_update,PUT-/combination_rules/{id}",com.kronos.timekeeping.setup.combinationrules.impl.rest.CombinationRuleServiceForRest
v1/timekeeping/setup,PUBLIC,yes,yes,"GET-/accrual_codes,GET-/accrual_codes/{id}",com.kronos.timekeeping.setup.accrualcodes.impl.rest.AccrualCodeServiceForRest
v1/timekeeping/setup/accrual_policies,PUBLIC,yes,yes,"GET-/{id},POST-/multi_read",com.kronos.timekeeping.setup.accrualpolicies.impl.rest.AccrualPoliciesServiceForRest
v2/timekeeping/setup,PUBLIC,yes,yes,GET-/accrual_codes,com.kronos.timekeeping.setup.accrualcodes.impl.rest.AccrualCodeServiceForRestV2
v2/timekeeping/setup,PUBLIC,yes,yes,"GET-/deduct_rules,GET-/deduct_rules/{id}",com.kronos.timekeeping.setup.bonusdeductionrules.impl.rest.BonusDeductionServiceForRestV2
v2/timekeeping/setup,PUBLIC,yes,yes,"GET-/employee_work_rules,GET-/employee_work_rules/{id},GET-/work_rules,GET-/work_rules/{id}",com.kronos.timekeeping.setup.workrules.impl.rest.WorkRuleServiceForRestV2
v2/timekeeping/setup,PUBLIC,yes,yes,"GET-/accrual_profiles,GET-/accrual_profiles/{id}",com.kronos.timekeeping.setup.accrualprofiles.impl.rest.AccrualProfileServiceForRestV2
